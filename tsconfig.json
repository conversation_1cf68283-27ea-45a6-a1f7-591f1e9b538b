{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@components/*": ["./src/components/*"], "@contexts/*": ["./src/contexts/*"], "@ui/*": ["./src/components/ui/*"], "@lib/*": ["./src/lib/*"], "@public/*": ["./public/*"], "@typesDeclarations/*": ["./types/*"], "@gql-fragments/*": ["./src/graphql/fragments/*"], "@gql-mutations/*": ["./src/graphql/mutations/*"], "@gql-queries/*": ["./src/graphql/queries/*"], "@constants/*": ["./src/constants/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/components/ui/BreadCrumb.jsx", "src/components/Icons", "src/lib/reducers"], "exclude": ["node_modules", ".next"]}