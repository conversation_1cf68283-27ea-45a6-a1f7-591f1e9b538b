export type wishlistProduct = {
  image: {
    sourceUrl: string;
    alt: string;
  };
  name: string;
  slug: string;
  size?: string;
  category: string;
  regularPrice: string;
  salePrice?: string;
  price?: string;
};

export type wishlist = wishlistProduct[];

// LocalStorage types
export type wishlistProduct_LS = {
  databaseId: string | number;
};

export type wishlist_LS = wishlistProduct_LS[];
