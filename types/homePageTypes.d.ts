import type { Prettify } from "./global";

// ? ----------------------------    HTTP RESPONSE TYPES    ---------------------------- //

interface acf_bannerBlock {
  name: "acf/bannerblock";
  attributes: {
    name: "acf/bannerblock";
    data: {
      banner_navbar: string;
    };
  };
  innerBlocks: [] & { length: 0 };
}

// -----------------------------------------------------------------------------------//
/** @usage used in plaze_grid type definition */
type plaze_gridSliderItem = {
  mediaID: number;
  mediaURL: string;
  text: string;
  /** should be used as a slug */
  url: string;
};

/** @usage used in plaze_grid type definition */
type plaze_grid_slider = {
  name: "plaze/grid-slider";
  attributes: {
    media: plaze_gridSliderItem[];
    text: string;
    url: string;
  };
};

/**  @usage used in plaze_grid type definition */
type plaze_grid_card = {
  name: "plaze/grid-card";
  attributes: {
    media: Prettify<Omit<plaze_gridSliderItem, "text">>;
    text: string;
    /** should be used as link  Slug */
    url: string;
  };
};

/** @usage used in plaze_grid type definition */
type plaze_grid_video = {
  name: "plaze/grid-video";
  attributes: {
    media: {
      text: string;
      mediaURL: string;
    };
    videoUrl?: string;
  };
};
type plaze_grid = { name: "plaze/grid" } & (
  | {
      attributes: Array<any>;
      innerBlocks: Array<plaze_grid_slider | plaze_grid_card>;
    }
  | {
      attributes: { layout: 1 };
      innerBlocks: Array<plaze_grid_slider | plaze_grid_card>;
    }
  | {
      attributes: { layout: 2 };
      innerBlocks: Array<plaze_grid_slider | plaze_grid_card>;
    }
  | {
      attributes: { layout: 3 };
      innerBlocks: Array<plaze_grid_video | plaze_grid_card>;
    }
  | {
      attributes: { layout: 4 };
      innerBlocks: Array<plaze_grid_video | plaze_grid_card>;
    }
);

// -----------------------------------------------------------------------------------//

interface plaze_productSlider {
  name: "plaze/product-slider";
  attributes: { categoryId: number } | Array<any>;
}

interface plaze_instagramFeed {
  name: "plaze/instagram-feed";
  // TODO
  // ! this type will be added when  Instagram fetcher is ready
}

interface plaze_brandSliderPlugin {
  name: "plaze/brand-slider-plugin";
  // TODO
  // ! missing props from response, proper type will be implemented after solving bug in WP response
}

interface acf_textAreaBlock {
  name: "acf/textareablock";
  attributes: {
    name: "acf/textareablock";
    data: {
      text_area_title: string;
      text_area: string;
    };
  };
  innerBlocks: [] & { length: 0 };
}

export type blockType =
  | acf_bannerBlock
  | plaze_grid
  | plaze_productSlider
  | plaze_instagramFeed
  | plaze_brandSliderPlugin
  | acf_textAreaBlock;
export interface homePageGraphQLResponse {
  blocks: blockType[];
}

export type homePageDataResponse = Prettify<{
  bannerBlockResponse: acf_bannerBlock;
  gridResponse: plaze_grid;
  productSliderResponse: plaze_productSlider;
  instagramFeedResponse: plaze_instagramFeed;
  brandsPluginResponse: plaze_brandSliderPlugin;
  textAreaBlockResponse: acf_textAreaBlock;
}>;

// ? ---------------------------- PROCESSED RESPONSE TYPES  ---------------------------- //

type imageDisplay = {
  imageURL: string;
  alt?: string | null;
  /** should consume attributes.url */
  slug?: string | null;
  name?: string | null;
};

type videoDisplay = {
  /** @description url for the video; could be Vimeo, Youtube, or WordPress-based video */
  url: string;
  title: string;
};

type TextBlockData = {
  text: string;
  title: string;
};

type instagramPost = {
  id: string;
  caption: string;
  media_url: string;
  media_type: string;
  timestamp: string;
  permalink: string;
  thumbnail_url: string;
};

/** @description a single product belonging to a certain category (which is provided from the HTTP response)) */
type productOfCategory = Prettify<
  imageDisplay &
    (
      | {
          productType: string;
          stockStatus: string;
          price: number;
          description: string;
          regularPrice: string;
          /** should render seedIcon when true
           *   !not provided by BE
           */
          // hasPropertyEcoSet: boolean;

          /** product is no older than 2 weeks
           *   !not provided by BE
           */
          // isNew: boolean;
          category_raw?: {
            id: number;
            name: string;
            slug: string;
          }[];
        }
      | {
          productType: string;
          stockStatus: string;
          price: string;
          description: string;
          regularPrice: string;
          /** should render seedIcon when true
           *   !not provided by BE
           */
          // hasPropertyEcoSet: boolean;

          /** product is no older than 2 weeks
           *   !not provided by BE
           */
          // isNew: boolean;

          /** sale price MIGHT be derived from salePercentage * price */
          salePrice: string;
          /**  a negative integer [-1,-2...,-100]
           * @deprecated ;can be extracted from price and salePrice
           */
          // salePercentage: number;
          category_raw?: {
            id: number;
            name: string;
            slug: string;
          }[];
        }
    )
>;

type sliderData = {
  text: string | null;
  url: string | null;
  media: Array<imageDisplay>;
};

/**
 * type 1:  slider - categories [skate, shoe, streetwear]
 *
 * type 2:  slider - categories [skate, streetwear]
 *
 * type 3:  slider - product
 *
 * type 4:  slider - video - categories [shoe, skate]
 */
export type heroGridData =
  | {
      type: 0;
      sliderData: sliderData;
      categories: Array<imageDisplay>;
    }
  | {
      type: 1;
      sliderData: sliderData;
      categories: Array<imageDisplay>;
    }
  | {
      type: 2;
      sliderData: sliderData;
      productData: imageDisplay;
    }
  | {
      type: 3;
      sliderData: sliderData;
      videoData: videoDisplay;
      categories: imageDisplay[];
    }
  | {
      type: 4;
      videoData: videoDisplay;
      categories: imageDisplay[];
    };

type bannerData = string;
type categoryProductsSliderData = {
  products: productOfCategory[];
  categoryName: String;
};
type brandsSliderData = Array<imageDisplay & { count: number }>;
type textBlockData = TextBlockData;
export type instagramPostsData = instagramPost[];

export type processedHomePageData = Prettify<{
  HeroGrid: Prettify<heroGridData>;
  banner: Prettify<bannerData>;
  categoryProductsSlider: Prettify<categoryProductsSliderData>;
  brandsSlider: Prettify<brandsSliderData>;
  textBlockData: Prettify<textBlockData>;
  InstagramFeed: Prettify<instagramPostsData>;
}>;
