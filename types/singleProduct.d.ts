/* eslint-disable no-unused-vars */

// Define types for optional product fragments
// If Fragment is undefined or null, set an empty object as the type
// Otherwise, use the Fragment type
type OptionalVariableProduct = VariableProductFragment extends undefined | null
  ? {}
  : VariableProductFragment;
type OptionalExternalProduct = ExternalProductFragment extends undefined | null
  ? {}
  : OptionalExternalProduct;
type OptionalGroupProduct = GroupProductFragment extends undefined | null
  ? {}
  : OptionalGroupProduct;
type OptionalSimpleProduct = SimpleProductFragment extends undefined | null
  ? {}
  : OptionalSimpleProduct;

// Define the singleProductType interface by combining the optional product fragments and additional properties
interface singleProductType
  extends OptionalVariableProduct,
    OptionalExternalProduct,
    OptionalGroupProduct,
    OptionalSimpleProduct {
  brands: { thumbnailUrl: string; name: string }[];
  name: string;
  productId: string | number;

  price: number;
  allPaShoesize: string[] | [];
  allPaPantsize: string[] | [];
  allPaClothsize: string[] | [];
  allPaDecksize: string[] | [];
  description: string;
  image: { mediaItemUrl: string };
  galleryImages: { mediaItemUrl: string }[];
}

interface ProductImage {
  mediaItemUrl: string;
}

interface GalleryImage {
  node: {
    mediaItemUrl: string;
  };
}

interface Brand {
  node: {
    thumbnailUrl: string;
    name: string;
  };
}

interface ProductSize {
  node: {
    name: string;
    count: number;
  };
}
interface Categories {
  node: {
    name: string;
  };
}

interface VariableProduct {
  id: string;
  name: string;
  price: string;
}
// Define the Product interface by combining the optional product fragments and additional properties
interface Product
  extends OptionalVariableProduct,
    OptionalExternalProduct,
    OptionalGroupProduct,
    OptionalSimpleProduct {
  id: string;
  slug: string;
  name: string;
  description: string;
  image: ProductImage;
  galleryImages: {
    edges: GalleryImage[];
  };
  brands: {
    edges: Brand[];
  };
  allPaShoesize: {
    edges: ProductSize[];
  };
  allPaPantsize: {
    edges: ProductSize[];
  };
  allPaClothsize: {
    edges: ProductSize[];
  };
  allPaDecksize: {
    edges: ProductSize[];
  };
}
