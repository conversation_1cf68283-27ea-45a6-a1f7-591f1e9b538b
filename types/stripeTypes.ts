export interface PaymentIntent {
  paymentIntentId: string;
  clientSecret: string;
}

export interface stripeSessionResponse {
  id: string;
  after_expiration: null;
  allow_promotion_codes: null;
  amount_subtotal: 2198;
  amount_total: 2198;
  automatic_tax: {
    enabled: false;
    liability: null;
    status: null;
  };
  billing_address_collection: null;
  cancel_url: null;
  client_reference_id: null;
  consent: null;
  consent_collection: null;
  created: number;
  currency: string;
  custom_fields: [];
  custom_text: {
    shipping_address: null;
    submit: null;
  };
  customer: null;
  customer_details: null;
  customer_email: null;
  expires_at: number;
  invoice: null;
  invoice_creation: {
    enabled: false;
    invoice_data: {
      account_tax_ids: null;
      custom_fields: null;
      description: null;
      footer: null;
      issuer: null;
      metadata: {};
      rendering_options: null;
    };
  };
  livemode: false;
  locale: null;
  metadata: {};
  mode: string;
  payment_intent: null;
  payment_link: null;

  payment_status: string;
  phone_number_collection: {
    enabled: false;
  };
  recovered_from: null;
  setup_intent: null;
  shipping_address_collection: null;
  shipping_cost: null;
  shipping_details: null;
  shipping_options: [];
  status: string;
  submit_type: null;
  subscription: null;
  success_url: string;
  total_details: {
    amount_discount: number;
    amount_shipping: number;
    amount_tax: number;
  };
  url: string;
}

export interface LineItem {
  id?: number;
  name?: string;
  product_id?: number;
  variation_id?: number;
  quantity: number;
  tax_class?: string;
  subtotal?: string;
  subtotal_tax?: string;
  total?: string;
  total_tax?: string;
  taxes?: any[];
  sku?: string;
  price?: string;
  price_data: {
    currency: "eur";
    unit_amount: number;
    product_data: {
      name: string;
      images: [string];
    };
  };
}
