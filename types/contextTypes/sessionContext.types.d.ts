import { USER_SESSION_ACTION } from "@typesDeclarations/utilTypes";
import { wishlist_LS } from "@typesDeclarations/wishlist";
import { shoppingCart } from "@typesDeclarations/wooTypes/wooCartTypes";
import { Maybe } from "graphql/jsutils/Maybe";
import { Dispatch } from "react";

import { Customer } from "../../src/graphql/__generated__/schema.graphql";

export type userSessionContext = {
  state: userSession;
  dispatch: Dispatch<USER_SESSION_ACTION>;
  checkoutManager: {
    checkingOut: boolean;
    setCheckingOut: any;
  };
  wishlistManager: {
    wishlist: wishlist_LS;
    toggleWishlistItem: (_databaseId: string | number) => void;
  };
};

export interface userSession {
  cart: Maybe<shoppingCart>;
  customer: Maybe<Customer>;
}
