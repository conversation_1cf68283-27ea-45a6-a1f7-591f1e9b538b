export type Params<T extends "alert" | "confirm" | "prompt"> =
  | Omit<Extract<DialogAction, { type: T }>, "type">
  | string;

type DialogStyleConfig = {
  title?: string;
  description?: string;
  actionButton?: string;
  cancelButton?: string;
};

type BaseDialogAction = {
  styleOptions?: DialogStyleConfig;
  title: string;
  body?: string;
  cancelButton?: string;
};

export type DialogAction =
  | (BaseDialogAction &
      (
        | {
            type: "alert";
          }
        | {
            type: "confirm";
            actionButton?: string;
          }
      ))
  | { type: "close" };

export interface DialogState {
  open: boolean;
  title: string;
  body: string;
  type: "alert" | "confirm";
  cancelButton: string;
  actionButton: string;
  styleOptions?: DialogStyleConfig;
  defaultValue?: string;
  inputProps?: React.PropsWithoutRef<
    React.DetailedHTMLProps<
      React.InputHTMLAttributes<HTMLInputElement>,
      HTMLInputElement
    >
  >;
}
