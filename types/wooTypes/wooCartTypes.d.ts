import {
  CartContentFragment,
  CartItemContentFragment,
} from "@gql-fragments/cartSlices.graphql.interface";
import { Prettify, stylable } from "@typesDeclarations/global";

export type shoppingCart = CartContentFragment & {
  /**
   * @description since the getCart query fails to return the total price (including tax), we're calculating the total manually
   * for a cart with one product of price 130,00 € and `standard tax` enabled, the total is set to be 109,24 € (but it should be 130,00 €)
   *
   */
  manuallyCalculatedTotal: string;
};

export type shoppingCart_item = Prettify<CartItemContentFragment>;

/**
 * @description a processed product received from BE, which is then to be consumed by cart-related components (mainly modal and table)
 */
export type formatted_ShoppingCartProduct = {
  /** @description used to delete/update an item in the shoppingCart */
  productKey: string;
  /** @example 3334689 */
  databaseId: number;
  id: string;
  name: string;

  /** first categories in the response's categories  array */
  category: string;

  /**
   * @description Artikel-Nr technically a number but should be treated as a string - used in cart table
   *
   * @sample  3889029371829
   * @notice
   * TODO make sure that databaseId is the correct value to use
   * @see  [formatter](../../src/lib/utilities/productUtils.ts)
   */
  itemNumber: number;
  /**
   * @description an integer [0,1,2,3,...] that describes that maximum possible quantity of a product
   */
  stockQuantity: number;

  /**
   * @description how many units the customer wants to purchase — can never exceed stockQuantity
   */
  quantity: number;
  stockQuantity: number;
  image: {
    sourceUrl: string;
    altText?: string;
  };

  regularPrice: string;

  salePrice?: string;
  price?: string;

  manualTotalPrice: string;
  /** should always be greater than 1 and less or equal to "stockQuantity" property*/

  /**
   * @description used as URL param that gets consumed in single-product-page
   */
  slug: string;

  /** Optional since some product don't have sizes by nature. */
  size?: string;
  variationId?: number;
};
// # SHOPPING CART PAGE TYPES

/**
 * @usage used in shopping cart tabular view
 */
export type artikelCellProps = Pick<
  shoppingCartRowProps,
  "image" | "name" | "itemNumber" | "size" | "slug" | "category"
>;

/**
 * @usage used in shopping cart tabular view
 */
export type priceCellProps = stylable<
  Pick<shoppingCartRowProps, "regularPrice" | "salePrice">
>;

/**
 * @usage used in shopping cart tabular view
 */
export type priceCellCalculatedProps = stylable<
  Pick<
    shoppingCartRowProps,
    "regularPrice" | "salePrice" | "quantity" | "manualTotalPrice"
  >
>;

export type priceProps = stylable<
  Pick<shoppingCartRowProps, "regularPrice" | "salePrice" | "price">
>;

/**
 * @usage used in shopping cart tabular view
 */
export type quantityCellProps = stylable<
  Pick<shoppingCartRowProps, "quantity" | "stockQuantity" | "productKey">
>;

/**
 * @usage used in shopping cart tabular view
 */
export type totalSumProps = Pick<shoppingCartRowProps, "manualTotalPrice">;

export type removeItemButtonProps = stylable<
  Pick<shoppingCartRowProps, "productKey">
>;
/**
 * @usage used in shopping cart tabular view
 */
export type shoppingCartRowProps = Pick<
  formatted_ShoppingCartProduct,
  | "category"
  | "productKey"
  | "image"
  | "itemNumber"
  | "name"
  | "size"
  | "salePrice"
  | "stockQuantity"
  | "quantity"
  | "slug"
  | "regularPrice"
  | "totalPrice"
  | "databaseId"
  | "manualTotalPrice"
  | "price"
>;

export interface shoppingCartTableProps {
  rows?: adapted_shoppingCartProducts;
}

// # SHOPPING CART MODAL PROPS

/**
 * @description a single product inside the sheet (modal)
 * @usage used in shopping cart modal view
 */
export type shoppingCartModal_item = Prettify<
  Pick<
    formatted_ShoppingCartProduct,
    | "productKey"
    | "image"
    | "stockQuantity"
    | "slug"
    | "name"
    | "quantity"
    | "category"
    | "regularPrice"
    | "salePrice"
    | "size"
    | "variationId"
  > & {
    dataAttributes?: string;
  }
>;

/**
 * @description a single product inside the sheet (modal)
 * @usage used in shopping cart modal view
 */
export type mobileCheckoutProduct_item = Prettify<
  Pick<
    formatted_ShoppingCartProduct,
    | "productKey"
    | "image"
    | "stockQuantity"
    | "slug"
    | "name"
    | "quantity"
    | "category"
    | "regularPrice"
    | "salePrice"
    | "size"
    | "variationId"
    | "manualTotalPrice"
  > & {
    dataAttributes?: string;
  }
>;

export type shoppingCartModalProps = {
  items: shoppingCartModal_item[];
};
