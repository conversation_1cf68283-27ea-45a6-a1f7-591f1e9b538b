import {
  ProductContentSlice_ExternalProduct_Fragment,
  ProductContentSlice_GroupProduct_Fragment,
  ProductContentSlice_SimpleProduct_Fragment,
  ProductContentSlice_VariableProduct_Fragment,
  ProductContentSliceFragment,
} from "@gql-fragments/productSlices.graphql.interface";

/** @description type port for all possible WooCommerce products */
export type product = ProductContentSliceFragment;

export type externalProduct = ProductContentSlice_ExternalProduct_Fragment;

export type groupProduct = ProductContentSlice_GroupProduct_Fragment;

export type simpleProduct = ProductContentSlice_SimpleProduct_Fragment;

export type variableProduct = ProductContentSlice_VariableProduct_Fragment;
