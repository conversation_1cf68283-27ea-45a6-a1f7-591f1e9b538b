{"name": "plaze-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install", "codegen": "graphql-codegen", "postinstall": "pnpm eslint . --fix"}, "dependencies": {"@apollo/client": "^3.13.1", "@cocart/cocart-rest-api": "^1.1.0", "@elastic/elasticsearch": "^8.17.1", "@graphql-typed-document-node/core": "^3.2.0", "@headlessui/react": "^1.7.19", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@libsql/client": "^0.14.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@sentry/nextjs": "^8.55.0", "@stripe/react-stripe-js": "^2.9.0", "@stripe/stripe-js": "^3.5.0", "@supercharge/promise-pool": "^3.2.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/node": "20.3.3", "@types/prop-types": "^15.7.14", "@types/react": "18.2.14", "@types/react-dom": "18.2.6", "@types/woocommerce__woocommerce-rest-api": "^1.0.5", "@types/zxcvbn": "^4.4.5", "@woocommerce/woocommerce-rest-api": "^1.0.1", "apollo-link-context": "1.0.20", "autoprefixer": "10.4.14", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^0.2.1", "cookies-next": "^5.1.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.39.3", "encoding": "^0.1.13", "eslint-config-next": "^15.2.0", "graphql": "^16.10.0", "graphql-request": "^6.1.0", "heroicons": "^2.2.0", "html-react-parser": "^5.2.2", "instantsearch.css": "^8.5.1", "instantsearch.js": "^4.77.3", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.268.0", "next": "^15.2.0", "nextjs-breadcrumbs": "^1.1.9", "nextjs-toploader": "^3.7.15", "postcss": "8.4.24", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-instantsearch": "^7.15.3", "react-instantsearch-nextjs": "^0.4.4", "react-player": "^2.16.0", "resend": "^3.5.0", "sharp": "^0.33.5", "stripe": "^14.25.0", "styled-components": "^5.3.11", "swiper": "^11.2.4", "tailwind-merge": "^1.14.0", "tailwindcss": "3.3.2", "typesense": "^2.0.3", "typesense-instantsearch-adapter": "^2.9.0", "usehooks-ts": "^2.16.0", "woocommerce-rest-ts-api": "^7.0.0", "zod": "3.21.4", "zustand": "^5.0.3", "zxcvbn": "^4.4.2"}, "devDependencies": {"@graphql-codegen/cli": "5.0.0", "@graphql-codegen/near-operation-file-preset": "2.5.0", "@graphql-codegen/schema-ast": "4.0.0", "@graphql-codegen/typed-document-node": "5.0.1", "@graphql-codegen/typescript": "4.0.1", "@graphql-codegen/typescript-operations": "4.0.1", "@redux-devtools/extension": "^3.3.0", "@stripe/react-stripe-js": "^1.4.1", "@stripe/stripe-js": "^1.16.0", "@types/styled-components": "^5.1.34", "@types/woocommerce__woocommerce-rest-api": "^1.0.2", "@types/zxcvbn": "^4.4.4", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@woocommerce/woocommerce-rest-api": "^1.0.1", "drizzle-kit": "^0.30.5", "eslint": "8.44.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-simple-import-sort": "^10.0.0", "husky": "^8.0.3", "lint-staged": "^13.3.0", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.3.0", "tailwindcss-animate": "^1.0.7", "typescript": "5.1.6"}}