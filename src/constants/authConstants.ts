// LS: Local Storage
// SS: Session Storage

export const tokenKeys = {
  woocommerce_session_header: "woocommerce-session",
  SESSION_TOKEN_LS_KEY: "session_token",
  REFRESH_TOKEN_LS_KEY: "refresh_token",
  AUTH_TOKEN_SS_KEY: "auth_token",
  AUTH_KEY_TIMEOUT: "30000",
  AUTH_COOKIE: "isAuthenticated",
  EXPIRATION_DATE: "expiration_date",
};

export const authenticationIsRequired_OPERATIONS = [
  "getCustomerData",
  "getOrders",
  "getData",
  "getPersonalData",
  "updatePersonalData",
  "updatePassword",
  "PersonalData",
  "password",
  "getAddress",
  "billingAddress",
  "shippingAddress",
  "deleteBillingAddress",
  "deleteShippingAddress",
  "deleteUser",
];

/**
 * @description mainly used for cart-related GraphQL operations
 */
export const authenticationIsOptional_OPERATIONS = [
  "getCart",
  "addToCart",
  "RemoveItemsFromCart",
  "UpdateCartItemQuantities",
  "getFullCustomerData",
  "applyCoupon",
  "emptyCart",
  "getCountries",
];

export const authErrors = {
  FAILED_FETCH_REFRESH_TOKEN:
    "Das Aktualisierungstoken konnte nicht abgerufen werden",
  FAILED_FETCH_AUTH_TOKEN:
    "Das Authentifizierungstoken konnte nicht abgerufen werden",
  FAILED_FETCH_SESSION_TOKEN: "Sitzungstoken konnte nicht abgerufen werden",
  INVALID_SESSION_TOKEN: "Ungültiges Sitzungstoken",
  INVALID_AUTH_TOKEN: "Ungültiges Authentifizierungstoken",
  INVALID_REFRESH_TOKEN: "Ungültiges Aktualisierungstoken",
  MISSING_REFRESH_TOKEN: "Es wurde kein Aktualisierungstoken gefunden",
  TOKEN_EXPIRATION_TIME_STAMP: "Kein Zeitstempel für den Token-Ablauf",
};
