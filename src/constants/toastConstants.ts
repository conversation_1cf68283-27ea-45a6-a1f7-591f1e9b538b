export const toast_TIMEOUT = {
  /** 300 millisecond */
  quick: 300,
  /** 600 millisecond */
  normal: 600,
  /** 1000 millisecond */
  second: 1000,
};

/**
 * @description each toastID should match exactly operation (there could be multiple toasts with different IDs at the same time but there should never be two or more toasts with the same ID at the same time)
 * */
export const toast_IDs = {
  GQL_OPS: {
    loginOP: "login-toast-toast-id",
    registerOP: "register-toast-toast-id",
    addToCart: "add-to-cart-toast-id",
    getCart: "get-card-toast-id",
  },
  CHECKOUT: {
    getStripeToken: "request-stripe-sessions-token-id",
  },
};

export const toast_TITLES = {
  GQL_OPS: {
    cart: {
      getCart_FAILED: "Warenkorbdaten konnten nicht abgerufen werden",
    },
  },
  GENERAL_STATE: {
    SUCCESS: "Erfolg",
    ERROR: "error",
  },
};
export const toast_DESCRIPTIONS = {
  GQL_OPS: {
    cart: {
      getCart_FAILED: "Es konnten keine Artikel im Warenkorb abgerufen werden",

      addToCart_SUCCESS: "Artikel zum Warenkorb hinzugefügt",
      addToCart_FAILED:
        "Der Artikel konnte nicht zum Warenkorb hinzugefügt werden",

      updateItemQuantities_SUCCESS: "Produktmenge aktualisieren",

      updateItemQuantities_FAILED:
        "Produktmenge konnte nicht aktualisiert werden",

      removeItemFromCart_FAILED:
        "Der Artikel konnte nicht aus dem Warenkorb entfernt werden",
      removeItemFromCart_SUCCESS:
        "Artikel erfolgreich aus dem Warenkorb entfernt",

      applyCoupon_SUCCESS: "Gutschein erfolgreich angewendet",
      applyCoupon_FAILED: "Gutschein konnte nicht angewendet werden",
    },
    register: {
      register_success: (username: string) =>
        `${username} wurde erfolgreich registriert, Sie werden zur Anmeldeseite weitergeleitet`,
    },
  },
  CHECKOUT: {
    getStripeTokenFailed: "Stripe-Sitzung konnte nicht generiert werden",
  },
};
