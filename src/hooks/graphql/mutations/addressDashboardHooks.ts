import { useMutation } from "@apollo/client";
import { useToast } from "@components/ui/use-toast";

import { DeleteBillingAddressDocument } from "../../../graphql/mutations/deleteBillingAddress.customer.graphql.interface";
import { DeleteShippingAddressDocument } from "../../../graphql/mutations/deleteShippingAddress.customer.graphql.interface";
import { BillingAddressDocument } from "../../../graphql/mutations/updateBillingAddress.customer.graphql.interface";
import { ShippingAddressDocument } from "../../../graphql/mutations/updateShippingAddress.customer.graphql.interface";
import { GetAddressDocument } from "../../../graphql/queries/address.customer.graphql.interface";
export const useUpdateBillingAddress = () => {
  const { toast } = useToast();
  const [sendBillingAddress, { loading }] = useMutation(
    BillingAddressDocument,
    {
      onError(error) {
        toast({
          title: "misslungen",
          variant: "destructive",
          description: error.message,
        });
      },
      refetchQueries: [GetAddressDocument],
      awaitRefetchQueries: true,
      onCompleted(data) {
        if (data) {
          toast({
            title: "ERFOLG",
            variant: "success",
            duration: 400,
          });
        } else {
          toast({
            title: "misslungen",
            variant: "destructive",
            description: "Irgendetwas stimmt nicht",
          });
        }
      },
    }
  );
  return { sendBillingAddress, loading };
};
export const useUpdateShippingAddress = () => {
  const { toast } = useToast();
  const [sendShippingAddress, { loading }] = useMutation(
    ShippingAddressDocument,
    {
      onError(error) {
        toast({
          title: "misslungen",
          variant: "destructive",
          description: error.message,
        });
      },
      refetchQueries: [GetAddressDocument],
      awaitRefetchQueries: true,
      onCompleted(data) {
        if (data) {
          toast({
            title: "ERFOLG",
            variant: "success",
            duration: 400,
          });
        } else {
          toast({
            title: "misslungen",
            variant: "destructive",
            description: "Irgendetwas stimmt nicht",
          });
        }
      },
    }
  );
  return { sendShippingAddress, loading };
};
export const useDeleteBillingAddress = () => {
  const { toast } = useToast();
  const [DeleteBillingAddress] = useMutation(DeleteBillingAddressDocument, {
    onError(error) {
      toast({
        title: "misslungen",
        variant: "destructive",
        description: error.message,
      });
    },
    refetchQueries: [GetAddressDocument],
    onCompleted(data) {
      if (data) {
        toast({
          title: "ERFOLG",
          variant: "success",
          duration: 400,
        });
      } else {
        toast({
          title: "misslungen",
          variant: "destructive",
          description: "Irgendetwas stimmt nicht",
        });
      }
    },
  });
  return { DeleteBillingAddress };
};

export const useDeleteShippingAddress = () => {
  const { toast } = useToast();
  const [DeleteShippingAddress] = useMutation(DeleteShippingAddressDocument, {
    onError(error) {
      toast({
        title: "misslungen",
        variant: "destructive",
        description: error.message,
      });
    },
    refetchQueries: [GetAddressDocument],
    onCompleted(data) {
      if (data) {
        toast({
          title: "ERFOLG",
          variant: "success",
          duration: 400,
        });
      } else {
        toast({
          title: "misslungen",
          variant: "destructive",
          description: "Irgendetwas stimmt nicht",
        });
      }
    },
  });
  return { DeleteShippingAddress };
};
