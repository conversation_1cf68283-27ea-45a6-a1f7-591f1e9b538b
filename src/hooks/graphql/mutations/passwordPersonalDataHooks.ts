import { useMutation } from "@apollo/client";
import { useToast } from "@components/ui/use-toast";

import { UpdatePasswordDocument } from "../../../graphql/mutations/password.customer.graphql.interface";

export const useSendPasswordPersonalData = () => {
  const { toast } = useToast();

  const [sendPasswordPersonalData, { data, loading }] = useMutation(
    UpdatePasswordDocument,
    {
      onError(error) {
        toast({
          title: "fehlgeschlagen",
          variant: "destructive",
          description: error.message,
        });
      },

      onCompleted(data) {
        if (data) {
          toast({
            title: "ERFOLG",
            variant: "success",
            duration: 400,
          });
        } else {
          toast({
            title: "fehlgeschlagen",
            variant: "destructive",
            description: "Etwas stimmt nicht",
          });
        }
      },
    }
  );
  return { sendPasswordPersonalData, data, loading };
};
