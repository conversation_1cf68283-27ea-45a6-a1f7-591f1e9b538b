import { useMutation } from "@apollo/client";
import { useToast } from "@components/ui/use-toast";
import {
  toast_DESCRIPTIONS,
  toast_TIMEOUT,
  toast_TITLES,
} from "@constants/toastConstants";
import { useSessionContext } from "@contexts/userSessionProvider";
import {
  AddToCartDocument,
  ApplyCouponDocument,
  EmptyCartDocument,
  RemoveItemsFromCartDocument,
  UpdateCartItemQuantitiesDocument,
  UpdateCartShippingDocument,
} from "@gql-mutations/cart.graphql.interface";

/**
 * @addToCart - adds a new item to the shopping cart and updates the shoppingCart context value (inside userSessionContext)
 * @state
 * * loading: true when mutation is actively running; false otherwise
 * * data
 * * error: GraphQL/network errors raised while running addToCart Mutation
 */
export const useAddToCart = () => {
  const { dispatch } = useSessionContext();
  const { toast } = useToast();
  const [addToCart, { loading, data, error }] = useMutation(AddToCartDocument, {
    onError(error) {
      toast({
        title: toast_TITLES.GENERAL_STATE.ERROR,
        variant: "destructive",
        description: error.message,
      });
    },
    onCompleted: async (data) => {
      if (!data.addToCart?.cart?.total) {
        toast({
          title: toast_TITLES.GENERAL_STATE.ERROR,
          variant: "destructive",
          description: toast_DESCRIPTIONS.GQL_OPS.cart.addToCart_FAILED,
        });
      } else {
        toast({
          title: toast_TITLES.GENERAL_STATE.SUCCESS,
          variant: "success",
          description: toast_DESCRIPTIONS.GQL_OPS.cart.addToCart_SUCCESS,
          duration: toast_TIMEOUT.quick,
        });

        const updatedCart = data.addToCart.cart;
        dispatch({ type: "SET_CART", payload: updatedCart });
      }
    },
  });

  return {
    /**
     * @description adds a new item to the shopping cart and updates the shoppingCart context value (inside userSessionContext)
     */
    addToCart,
    state: {
      /**
       * @true  adding cart is an active operation
       * @false no cart is being add OR operation is complete (success or fail)
       */
      loading,
      data,
      /**
       * GraphQL/network errors raised while running addToCart Mutation
       */
      error,
    },
  };
};

export const useUpdateCartItemQuantities = () => {
  const { dispatch } = useSessionContext();
  const { toast } = useToast();
  const [updateCartItemQuantity, { data, loading, error }] = useMutation(
    UpdateCartItemQuantitiesDocument,
    {
      onError(error) {
        toast({
          title: toast_TITLES.GENERAL_STATE.ERROR,
          variant: "destructive",
          description: error.message,
        });
      },
      onCompleted: async (data) => {
        if (!data.updateItemQuantities?.cart?.total) {
          toast({
            title: toast_TITLES.GENERAL_STATE.ERROR,
            variant: "destructive",
            description:
              toast_DESCRIPTIONS.GQL_OPS.cart.updateItemQuantities_FAILED,
          });
        } else {
          toast({
            title: toast_TITLES.GENERAL_STATE.SUCCESS,
            variant: "success",
            description:
              toast_DESCRIPTIONS.GQL_OPS.cart.updateItemQuantities_SUCCESS,
          });
          const updatedCart = data.updateItemQuantities.cart;
          dispatch({ type: "SET_CART", payload: updatedCart });
        }
      },
    }
  );

  return {
    /**
     * @description increase/decrease to-be-purchased quantity of a single product
     */
    updateCartItemQuantity,
    state: {
      /**
       * @true  updating an Item is an active operation
       * @false no cart is being add OR operation is complete (success or fail)
       */
      loading,
      data,
      /**
       * GraphQL/network errors raised while running removeItemFromCart Mutation
       */
      error,
    },
  };
};

export const useRemoveItemFromCart = () => {
  const { dispatch } = useSessionContext();
  const { toast } = useToast();
  const [removeItemFromCart, { loading, data, error }] = useMutation(
    RemoveItemsFromCartDocument,
    {
      onError(error) {
        toast({
          title: toast_TITLES.GENERAL_STATE.ERROR,
          variant: "destructive",
          description: error.message,
        });
      },
      onCompleted: async (data) => {
        if (!data.removeItemsFromCart?.cart?.total) {
          toast({
            title: toast_TITLES.GENERAL_STATE.ERROR,
            variant: "destructive",
            description:
              toast_DESCRIPTIONS.GQL_OPS.cart.removeItemFromCart_FAILED,
          });
        } else {
          toast({
            title: toast_TITLES.GENERAL_STATE.SUCCESS,
            variant: "success",
            description:
              toast_DESCRIPTIONS.GQL_OPS.cart.removeItemFromCart_SUCCESS,
          });
          const updatedCart = data.removeItemsFromCart.cart;
          dispatch({ type: "SET_CART", payload: updatedCart });
        }
      },
    }
  );
  return {
    /**
     * @description adds a new item to the shopping cart and updates the shoppingCart context value (inside userSessionContext)
     */
    removeItemFromCart,
    state: {
      /**
       * @true  removing an Item is an active operation
       * @false no cart is being add OR operation is complete (success or fail)
       */
      loading,
      data,
      /**
       * GraphQL/network errors raised while running removeItemFromCart Mutation
       */
      error,
    },
  };
};

export const useApplyCoupon = () => {
  const { dispatch } = useSessionContext();
  const { toast } = useToast();

  const [applyCoupon, { loading, data, error }] = useMutation(
    ApplyCouponDocument,
    {
      onError(error) {
        toast({
          title: toast_TITLES.GENERAL_STATE.ERROR,
          variant: "destructive",
          description: error.message,
        });
      },
      onCompleted: async (data) => {
        if (!data.applyCoupon?.cart?.total) {
          toast({
            title: toast_TITLES.GENERAL_STATE.ERROR,
            variant: "destructive",
            description: toast_DESCRIPTIONS.GQL_OPS.cart.applyCoupon_FAILED,
          });
        } else {
          toast({
            title: toast_TITLES.GENERAL_STATE.SUCCESS,
            variant: "success",
            description: toast_DESCRIPTIONS.GQL_OPS.cart.applyCoupon_SUCCESS,
          });
          const updatedCart = data.applyCoupon?.cart;

          dispatch({ type: "SET_CART", payload: updatedCart });
        }
      },
    }
  );

  return {
    /**
     * @description applies one coupon to the shopping cart and updates the shoppingCart context value (inside userSessionContext)
     */
    applyCoupon,
    state: {
      /**
       * @true  applying coupon is an active operation
       * @false no coupon is being applied OR operation is complete (success or fail)
       */
      loading,
      data,
      /**
       * GraphQL/network errors raised while running ApplyCoupon Mutation
       */
      error,
    },
  };
};

export const useEmptyCart = () => {
  const { dispatch } = useSessionContext();
  const { toast } = useToast();
  const [emptyCart, { loading, data, error }] = useMutation(EmptyCartDocument, {
    onError(error) {
      toast({
        title: toast_TITLES.GENERAL_STATE.ERROR,
        variant: "destructive",
        description: error.message,
      });
      return false;
    },
    onCompleted: async () => {
      dispatch({ type: "SET_CART", payload: null });
      return true;
    },
  });

  return { emptyCart, state: { data, loading, error } };
};

export const useUpdateCartShipping = () => {
  const { dispatch } = useSessionContext();

  const [updateCartShipping, { loading, data, error }] = useMutation(
    UpdateCartShippingDocument,
    {
      onCompleted: (data) => {
        const updatedCart = data.updateShippingMethod?.cart;
        dispatch({ type: "SET_CART", payload: updatedCart });
      },
      onError: () => {},
    }
  );
  return { updateCartShipping, state: { data, loading, error } };
};
