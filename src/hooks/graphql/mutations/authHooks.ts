import { useLazyQuery, useMutation } from "@apollo/client";
import { useToast } from "@components/ui/use-toast";
import { authErrors } from "@constants/authConstants";
import {
  toast_DESCRIPTIONS,
  toast_IDs,
  toast_TIMEOUT,
} from "@constants/toastConstants";
import { useSessionContext } from "@contexts/userSessionProvider";
import { RefreshJwtAuthTokenDocument } from "@gql-mutations/refreshJWTAuth.token.graphql.interface";
import { saveCredentials } from "@lib/utilities/authUtils";
import { useRouter } from "next/navigation";

import { LoginDocument } from "../../../graphql/mutations/login.customer.graphql.interface";
import { LogoutDocument } from "../../../graphql/mutations/logout.customer.graphql.interface";
import { RegisterDocument } from "../../../graphql/mutations/register.customer.graphql.interface";
import { GetSessionTokenDocument } from "../../../graphql/queries/getSession.token.graphql.interface";
import { useLazyGetCart } from "../queries/cartHooks";

export const useRegister = () => {
  const { toast, dismiss } = useToast();
  const router = useRouter();

  const [register, { loading }] = useMutation(RegisterDocument, {
    onError(error) {
      toast({
        title: "fehlgeschlagen",
        variant: "destructive",
        description: error.message,
      });
    },

    onCompleted(data) {
      dismiss(toast_IDs.GQL_OPS.registerOP);
      const username = data.registerCustomer?.customer?.username;
      toast({
        duration: toast_TIMEOUT.normal,
        title: "Erfolg",
        variant: "success",
        description: toast_DESCRIPTIONS.GQL_OPS.register.register_success(
          username ?? ""
        ),
      });
      // Ii used setTimeOut for better user experience so the user won't be redirected until the toast is finished
      // Don't hate me for this but I didn't find a better way
      // maybe when there is time refactor the useToast hook so it can it utilize promises to wait for the toast duration
      setTimeout(() => {
        router.push("/login");
      }, 600);
    },
  });

  /** @description fires a 'submitting 'toast then calls the login mutation  */
  const callRegister = async (params: (typeof register)["arguments"]) => {
    toast({
      title: "submitting",
      variant: "default",
      itemID: toast_IDs.GQL_OPS.registerOP,
    });

    await register(params);
  };
  return { register: callRegister, loading };
};

export const useLogin = () => {
  const { toast, dismiss } = useToast();
  const { dispatch } = useSessionContext();

  const { getCart } = useLazyGetCart();
  const router = useRouter();

  const [login, { loading, error }] = useMutation(LoginDocument, {
    fetchPolicy: "no-cache",
    onError(error) {
      toast({
        title: "fehlgeschlagen",
        variant: "destructive",
        description: error.message,
      });
    },
    onCompleted: async (data) => {
      if (!data?.login?.customer?.jwtAuthToken) {
        toast({
          title: "fehlgeschlagen",
          variant: "destructive",
          description: "AuthToken konnte nicht abgerufen werden",
        });
      } else {
        const loginResults = data?.login;
        const sessionToken = loginResults?.customer?.sessionToken,
          refreshToken = loginResults?.customer?.jwtRefreshToken,
          authToken = loginResults?.customer?.jwtAuthToken,
          tokenExpirationTimeStamp = loginResults.customer?.jwtAuthExpiration;
        if (!refreshToken) {
          throw new Error(authErrors.FAILED_FETCH_REFRESH_TOKEN);
        } else if (!authToken) {
          throw new Error(authErrors.FAILED_FETCH_AUTH_TOKEN);
        } else if (!sessionToken) {
          throw new Error(authErrors.FAILED_FETCH_SESSION_TOKEN);
        } else if (tokenExpirationTimeStamp == undefined) {
          throw new Error(authErrors.TOKEN_EXPIRATION_TIME_STAMP);
        } else {
          toast({
            duration: 600,
            title: "Erfolg",
            variant: "success",
          });

          saveCredentials({
            authToken,
            refreshToken,
            sessionToken,
            tokenExpirationTimeStamp,
          });
          const [username, displayName, email] = [
            data.login.customer?.username,
            data.login.customer?.displayName,
            data.login.customer?.email,
          ];

          dispatch({
            type: "SET_CUSTOMER",
            payload: { username, displayName, email },
          });
          await getCart();
          const prevRoute = sessionStorage.getItem("prevRoute");
          router.push(
            prevRoute && prevRoute !== "/" ? prevRoute : "/dashboard/main"
          );
        }
        dismiss(toast_IDs.GQL_OPS.loginOP);
      }
    },
  });

  /** @description fires a 'submitting 'toast then calls the login mutation  */
  const callLogin = async (params: (typeof login)["arguments"]) => {
    toast({
      title: "submitting",
      variant: "default",
      itemID: toast_IDs.GQL_OPS.loginOP,
    });

    await login(params);
  };

  return { login: callLogin, loading, error };
};

export const useLogout = () => {
  const router = useRouter();
  const { toast } = useToast();
  const [logout, { loading }] = useMutation(LogoutDocument, {
    onError(error) {
      toast({
        title: "fehlgeschlagen",
        variant: "destructive",
        description: error.message,
      });
    },
    onCompleted(data) {
      if (data.logout?.status === "SUCCESS") {
        toast({
          title: "umleiten...",
          variant: "default",
          duration: 400,
        });
        router.replace("/");
      }
    },
  });
  return { logout, loggingOut: loading };
};

export const useGetSessionToken = () => {
  const [fetch, { loading, data, error }] = useLazyQuery(
    GetSessionTokenDocument,
    {
      onError(error) {
        throw new Error(error.message);
      },
      onCompleted(data) {
        if (data.customer?.sessionToken) {
          return data.customer.sessionToken;
        }
        throw new Error("getSession hook error");
      },
    }
  );
  return { getSessionToken: fetch, state: { loading, error, data } };
};

export const useRefreshedToken = () => {
  const [fetch, { loading, data, error }] = useLazyQuery(
    RefreshJwtAuthTokenDocument,
    {
      onError(error) {
        throw new Error(error.message);
      },
      onCompleted(data) {
        if (data.refreshJwtAuthToken?.authToken) {
          return data.refreshJwtAuthToken?.authToken;
        }
        throw new Error("Das Aktualisierungstoken ist abgelaufen");
      },
    }
  );
  return { getNewToken: fetch, state: { loading, error, data } };
};
