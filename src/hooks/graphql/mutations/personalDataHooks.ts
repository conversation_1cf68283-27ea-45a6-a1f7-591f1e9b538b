import { useMutation } from "@apollo/client";
import { useToast } from "@components/ui/use-toast";

import { UpdatePersonalDataDocument } from "../../../graphql/mutations/personalData.customer.graphql.interface";
import { GetPersonalDataDocument } from "../../../graphql/queries/personalData.customer.graphql.interface";

export const useSendPersonalData = () => {
  const { toast } = useToast();

  const [sendPersonalData, { data, loading }] = useMutation(
    UpdatePersonalDataDocument,
    {
      onError(error) {
        toast({
          title: "misslungen",
          variant: "destructive",
          description: error.message,
        });
      },
      refetchQueries: [GetPersonalDataDocument],
      awaitRefetchQueries: true,
      onCompleted(data) {
        if (data) {
          toast({
            title: "ERFOLG",
            variant: "success",
            duration: 400,
          });
        } else {
          toast({
            title: "misslungen",
            variant: "destructive",
            description: "<PERSON>rgendetwas stimmt nicht",
          });
        }
      },
    }
  );
  return { sendPersonalData, data, loading };
};
