import { useQuery } from "@apollo/client";
import { useToast } from "@components/ui/use-toast";

import { GetCountriesDocument } from "../../../graphql/queries/country.customer.graphql.interface";

export const useGetCountries = () => {
  const { toast } = useToast();
  const { data, loading, error } = useQuery(GetCountriesDocument, {
    onError(error) {
      toast({
        title: "Fehler beim Abrufen der Benutzeradresse",
        variant: "destructive",
        description: error.message,
      });
    },
  });
  const regionNamesInGermany = new Intl.DisplayNames(["de"], {
    type: "region",
  });
  const countries = data?.countries?.map((country) => {
    return {
      id: country as string,
      name: regionNamesInGermany.of(country as string) as string,
    };
  });

  return { data, countries, loading, error };
};
