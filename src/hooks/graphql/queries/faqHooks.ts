import { useQuery } from "@apollo/client";
import { toast } from "@components/ui/use-toast";

import { GetFaqDocument } from "../../../graphql/queries/faq.graphql.interface";

export const GetFaq = () => {
  const { data, loading, error } = useQuery(GetFaqDocument, {
    onError(error) {
      toast({
        title: "fehlgeschlagen",
        variant: "destructive",
        description: error.message,
      });
    },
  });
  return { data, loading, error };
};
