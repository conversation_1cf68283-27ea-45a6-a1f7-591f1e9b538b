import { useLazyQuery, useQuery } from "@apollo/client";
import { useToast } from "@components/ui/use-toast";
import {
  toast_DESCRIPTIONS,
  toast_TIMEOUT,
  toast_TITLES,
} from "@constants/toastConstants";
import { useSessionContext } from "@contexts/userSessionProvider";
import { GetCartDocument } from "@gql-queries/cart.graphql.interface";
import { getShippingMethodsByCountry } from "@gql-queries/getShippingMethodsByCountry";

/**
 * @description fetches cart information
 * * getCart query (should be provided with customerID for authenticated users)
 * * getCartState provides the "data,loading, error" graphQL tuple
 *
 */
export const useLazyGetCart = () => {
  const { toast } = useToast();
  const { dispatch } = useSessionContext();
  const [getCart, { data, loading, error }] = useLazyQuery(GetCartDocument, {
    onCompleted(data) {
      console.log("cart error 1", data, error);
      if (data.cart) {
        const cartData = data.cart;
        dispatch({ type: "SET_CART", payload: cartData });
        return cartData;
      } else {
        toast({
          title: toast_TITLES.GQL_OPS.cart.getCart_FAILED,
          variant: "destructive",
          duration: toast_TIMEOUT.normal,
          description: toast_DESCRIPTIONS.GQL_OPS.cart.getCart_FAILED,
        });
      }
    },
    onError(error) {
      console.log("cart error", error);
      toast({
        title: toast_TITLES.GQL_OPS.cart.getCart_FAILED,
        variant: "destructive",
        duration: toast_TIMEOUT.normal,
        description: error.message,
      });
    },
  });

  return {
    getCart,
    getCartState: {
      getCartData: data,
      getCartLoading: loading,
      getCartError: error,
    },
  };
};

/**
 * @description fetches shopping cart data and update userSession context state
 */
export const useGetCart = () => {
  const { toast } = useToast();
  const { dispatch } = useSessionContext();
  const { data, error, loading } = useQuery(GetCartDocument, {
    onCompleted(data) {
      if (data.cart) {
        const cartData = data.cart;
        dispatch({ type: "SET_CART", payload: cartData });
        return cartData;
      } else {
        toast({
          title: toast_TITLES.GQL_OPS.cart.getCart_FAILED,
          variant: "destructive",
          duration: toast_TIMEOUT.normal,
          description: toast_DESCRIPTIONS.GQL_OPS.cart.getCart_FAILED,
        });
      }
    },
    onError(error) {
      toast({
        title: toast_TITLES.GQL_OPS.cart.getCart_FAILED,
        variant: "destructive",
        duration: toast_TIMEOUT.normal,
        description: error.message,
      });
    },
  });

  return {
    data,
    loading,
    error,
  };
};

// had to manually introduce type since `GetShippingMethodsByCountryQuery` types couldn't be autogenerated; the query is not defined on Graphql `rootQuery`
export interface FetchedShippingMethod {
  id: string;
  instanceId: string;
  methodId: string;
  name: string;
  price: number;
  /** an array of applicable taxes per item (each tax is represented as a string).
   * @note a shipping method might have multiple taxes added
   */
  taxes: Array<string>;
}

interface GetShippingMethodsByCountryQuery {
  availableShippingMethods: Array<FetchedShippingMethod>;
}

/**
 * @description fetches available shipping methods according to selected country (country ISO code) */
export const useGetShippingMethodsByCountry = () => {
  const { toast } = useToast();

  const [getShippingMethods, { data, loading, error }] = useLazyQuery<
    GetShippingMethodsByCountryQuery,
    { country: string }
  >(getShippingMethodsByCountry, {
    onError(error) {
      toast({
        title: toast_TITLES.GQL_OPS.cart.getCart_FAILED,
        variant: "destructive",
        duration: toast_TIMEOUT.normal,
        description: error.message,
      });
    },
  });

  return {
    getShippingMethods,
    state: { data, loading, error },
  };
};
