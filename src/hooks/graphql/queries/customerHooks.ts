import { useLazyQuery } from "@apollo/client";
import { useSessionContext } from "@contexts/userSessionProvider";

import {
  GetCustomerDataDocument,
  GetFullCustomerDataDocument,
} from "../../../graphql/queries/data.customer.graphql.interface";

export const useGetCustomerData = () => {
  const { dispatch } = useSessionContext();
  // const { toast } = useToast();
  const [query, { data, loading, error }] = useLazyQuery(
    GetCustomerDataDocument,
    {
      onError(error) {
        // toast({
        //   title: "failed to fetch user data",
        //   variant: "destructive",
        //   description: error.message,
        // });
        throw Error(`Benutzerdaten konnten nicht abgerufen werden: ${error}`);
      },
      onCompleted(data) {
        if (data.customer) {
          const [displayName, customerId, email] = [
            data.customer?.displayName as string,
            data.customer.id as string,
            data.customer?.email,
          ];
          dispatch({
            type: "SET_CUSTOMER",
            payload: {
              customerId,
              displayName,
              email,
            },
          });
        } else {
          // toast({
          //   title: "error in fetching user data",
          //   variant: "destructive",
          // });
          throw Error(`Benutzerdaten konnten nicht abgerufen werden`);
        }
      },
    }
  );

  const setupUser = query;
  return { setupUser, state: { data, loading, error } };
};

export const useLazyGetFullCustomerData = () => {
  const [query, { data, loading, error }] = useLazyQuery(
    GetFullCustomerDataDocument,
    {
      onError(error) {
        throw Error(`Benutzerdaten konnten nicht abgerufen werden: ${error}`);
      },
    }
  );
  return { getFullCustomerData: query, state: { data, loading, error } };
};
