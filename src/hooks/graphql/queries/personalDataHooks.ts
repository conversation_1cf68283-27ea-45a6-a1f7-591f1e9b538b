import { useQuery } from "@apollo/client";
import { toast } from "@components/ui/use-toast";

import { GetPersonalDataDocument } from "../../../graphql/queries/personalData.customer.graphql.interface";

export const GetDataDocument = () => {
  const { data, loading, error } = useQuery(GetPersonalDataDocument, {
    onError(error) {
      toast({
        title: "misslungen",
        variant: "destructive",
        description: error.message,
      });
    },
  });
  return { data, loading, error };
};
