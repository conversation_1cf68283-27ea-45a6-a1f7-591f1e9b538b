import { useToast } from "@components/ui/use-toast";
import { toast_TIMEOUT, toast_TITLES } from "@constants/toastConstants";
import { useSessionContext } from "@contexts/userSessionProvider";
import { getProductsForWishlist } from "@gql-queries/wishlist";
import { apolloClient } from "@lib/apollo/client";
import {
  adaptWishlistSimpleProduct,
  adaptWishlistVariableProduct,
} from "@lib/utilities/wishlistUtils";

export const useGetWishlistProducts = () => {
  const { toast } = useToast();
  const {
    wishlistManager: { wishlist: localStorageWishlist },
  } = useSessionContext();

  const updateWishList = async () => {
    try {
      if (localStorageWishlist.length > 0) {
        const { data } = await apolloClient.query({
          query: getProductsForWishlist(
            localStorageWishlist.map((uri) => uri.databaseId)
          ),
        });
        const products: any = Object.keys(data).map((key) => data[key]);
        const processedData = products.map((product: any) => {
          const type = product.type;

          return type === "SIMPLE"
            ? adaptWishlistSimpleProduct(product)
            : (() => {
                const _variationId =
                  localStorageWishlist.find((productLS) => {
                    return productLS.databaseId === product.databaseId;
                  }) ?? "";

                const t = adaptWishlistVariableProduct(product);
                return t;
              })();
        });

        return processedData;
      }
    } catch (error) {
      const err = error as any;
      if (err.message === 'Die "globale ID" ist ungültig') {
        toast({
          title: toast_TITLES.GENERAL_STATE.ERROR,
          variant: "destructive",
          duration: toast_TIMEOUT.second,
          description: `Error while fetching wishList products: ${err.message}`,
        });
      }
      throw error;
    }
  };

  return { updateWishList };
};
