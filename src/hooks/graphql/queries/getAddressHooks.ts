import { useQuery } from "@apollo/client";
import { useToast } from "@components/ui/use-toast";

import { GetAddressDocument } from "../../../graphql/queries/address.customer.graphql.interface";

export const useGetAddressData = () => {
  const { toast } = useToast();
  const { data, loading, error } = useQuery(GetAddressDocument, {
    onError(error) {
      toast({
        title: "Fehler beim Abrufen der Benutzeradresse",
        variant: "destructive",
        description: error.message,
      });
    },
  });
  return { data, loading, error };
};
