import { useQuery } from "@apollo/client";
import { toast } from "@components/ui/use-toast";

import { GetOrdersDocument } from "../../../graphql/queries/orders.customer.graphql.interface";

export const GetOrder = () => {
  const { data, loading, error } = useQuery(GetOrdersDocument, {
    onError(error) {
      toast({
        title: "fehlgeschlagen",
        variant: "destructive",
        description: error.message,
      });
    },
  });
  return { data, loading, error };
};
