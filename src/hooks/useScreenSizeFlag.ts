"use client";

import { useEffect, useState } from "react";

export function useScreenSizeFlag() {
  const [isSmallScreen, setIsSmallScreen] = useState(true);
  useEffect(() => {
    function handleResize() {
      setIsSmallScreen(Boolean(window.innerWidth > 768));
    }

    // assuring this runs only on client-side
    if (typeof window !== "undefined" && window.screen) {
      // Add event listener
      window.addEventListener("resize", handleResize);

      // Call handler right away so state gets updated with initial window size
      handleResize();

      // Remove event listener on cleanup
      return () => window.removeEventListener("resize", handleResize);
    }
  }, []);
  return isSmallScreen;
}
