import { DialogContext } from "@contexts/DialogProvider";
import { Params } from "@typesDeclarations/contextTypes/dialogContext.types";
import { useContext } from "react";

export function useAlert() {
  const dialog = useContext(DialogContext);

  return (params: Params<"alert">) => {
    return dialog({
      ...(typeof params === "string" ? { title: params } : params),
      type: "alert",
    });
  };
}
