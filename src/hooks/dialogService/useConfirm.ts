import { DialogContext } from "@contexts/DialogProvider";
import { Params } from "@typesDeclarations/contextTypes/dialogContext.types";
import { useCallback, useContext } from "react";

const useConfirm = () => {
  const dialog = useContext(DialogContext);

  return useCallback(
    (params: Params<"confirm">) => {
      return dialog({
        ...(typeof params === "string" ? { title: params } : params),
        type: "confirm",
      });
    },
    [dialog]
  );
};
export default useConfirm;
