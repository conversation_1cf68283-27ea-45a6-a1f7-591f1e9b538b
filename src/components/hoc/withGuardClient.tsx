"use client";
import { redirect } from "next/navigation";
import { useEffect } from "react";

/**
 * !TO BE USED ONLY WITH CLIENT RENDERED COMPONENTS
 * To be a good boy and keeping separation of concerns in mind I add HOC for server component also
 * A High Order Component that is used to protect the rendering of components based on a condition
 * I took the HOC approach cause it's more error prone in next than using custom hook and it doesn't allow for layout shifts and we can have more control on the flow, like rendering a loading component or such.
 * @param Component The Component to be rendered when the condition is met
 * @param configObject Has two params the condition and the redirect URL to make the HOC as general as possible.
 * TODO
 * For now the condition is passed as a param but in case we decided to make this guard only responsible for authentication status as the name implies a logic for checking if we have an authenticated user should be implemented here so we don't have to write the code in every component and just pass the condition as boolean.
 * Also a third param can be added which is the loader component and it can be called inside the HOC.
 * @usage Just export the component you want to protect like this export default
 * withAuth(
 *  Component: The component to be protected
 *  {
 *      condition: The condition to be rendered based on
 *      redirectTo: The path you wish to redirect to when the condition is not met
 *  }
 * )
 */

export default function withGuard(
  Component: any,
  {
    condition,
    redirectTo,
  }: { condition: boolean | (() => boolean); redirectTo: string }
) {
  // Using javaScript closure create a HOC
  return function WithAuth(props: any) {
    useEffect(() => {
      let extractedCondition: boolean;

      if (typeof condition === "function") {
        extractedCondition = condition();
      } else {
        extractedCondition = condition;
      }
      // Redirect to redirectTo path if the condition is not met
      if (!extractedCondition) {
        redirect(redirectTo);
      }
    }, []);

    /**
     * I wrote two approaches, the first is to not render anything at all, which is Okay but from a UX overview it should be a loader component to indicate that checks are being made.
     */

    // Any approach can be used either rendering nothing or rendering a loader component
    // if (!condition) {
    //   return <div>Loading.....</div>; // Render a loader until the redirect is complete
    // }

    // Render nothing until the redirect is complete
    if (!condition) {
      return null;
    }

    // If all the checks are passed render the component passing all the props by default
    return <Component {...props} />;
  };
}
