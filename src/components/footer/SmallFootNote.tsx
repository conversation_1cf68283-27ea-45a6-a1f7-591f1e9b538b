import Link from "next/link";
/**
 * @description
 * contain copyright and privacy in third section in footer
 * [in mobile screen]
 */
const SmallFootNote = () => {
  return (
    <div>
      <div className="py-5 font-thin">
        <small>
          <div className="px-4 py-4 "> © 2024 PLAZE</div>
          <div className="py-1">
            <Link
              href="/terms"
              className="px-5 hover:text-primary hover:underline"
            >
              AGB
            </Link>
            |
            <Link
              href="/datenschutz"
              className=" px-5 hover:text-primary  hover:underline"
            >
              Datenschutz
            </Link>
            |
            <Link
              href="/impressum"
              className="px-5 hover:text-primary  hover:underline"
            >
              Impressum
            </Link>
            |
          </div>
          <Link
            href="https://plaze-shop.de/cookie-information/"
            className="px-5 hover:text-primary  hover:underline"
          >
            Cookie Information
          </Link>
        </small>
      </div>
    </div>
  );
};

export default SmallFootNote;
