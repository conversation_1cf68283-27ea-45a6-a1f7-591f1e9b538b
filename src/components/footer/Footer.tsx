import Link from "next/link";
import React from "react";

import BadgeIcon from "../Icons/BadgeIcon";
import EmailIcon from "../Icons/EmailIcon";
import MessengerIcon from "../Icons/MessengerIcon";
import PhoneIcon from "../Icons/PhoneIcon";
import ShopIcon from "../Icons/ShopIcon";
import TruckIcon from "../Icons/TruckIcon";
import DefaultFootNote from "./DefaultFootNote";
import MobileAccordion from "./MobileAccordion";
import PaymentMethods from "./PaymentMethods";
import SmallFootNote from "./SmallFootNote";
import SocialMedia from "./SocialMedia";
/**
 * @description footer Composed of three sections
 *
 * @section1 web site services
 *
 * @section2 contact and payment methods and services
 *
 * @section3 footNote
 */

const Footer: React.FC = () => {
  return (
    <footer>
      <section className="  flex h-28 items-center justify-around bg-gray-bright py-20 font-thin max-lg:hidden">
        <ul className="flex ">
          <li>
            <TruckIcon />
          </li>

          <li className="list-none px-5">
            Lieferung innherhalb von 1-2 Werktagen nach
            <br />
            Eingang der Bestätigung
          </li>
        </ul>
        <ul className="flex">
          <li>
            <BadgeIcon />
          </li>
          <li className="list-none px-5 ">
            Die Versandkosten deiner Bestellung betragen <br />
            Pauschal 7,50€
          </li>
        </ul>
        <ul className="flex list-none">
          <li>
            <ShopIcon />
          </li>
          <li className="list-none px-5">
            Zu unseren Öffnungszeiten konnen alle
            <br />
            Bestellungen im Store abgeholt werden
          </li>
        </ul>
      </section>

      <section className="flex justify-around bg-background max-lg:hidden ">
        <div className="flex-col">
          <p className=" h-7 w-52  border-b-2 border-solid border-black py-10  ">
            PLAZE STORE CHEMNITZ
          </p>
          <ul className="py-5 font-thin">
            <li className="list-none">
              Du hast ein Problem oder eine Frage?
              <br />
              Wir sind für dich da.
            </li>
            <li key={"telephone"}>
              <a
                href="tel:+49 (0)371 666 00 81"
                className="flex  flex-row py-3 hover:text-primary hover:underline "
              >
                <PhoneIcon />
                <span className="px-2">+49 (0)371 666 00 81</span>
              </a>
            </li>
            <li key={"email"}>
              <a
                href="email:<EMAIL> "
                className="flex py-2  hover:text-primary  hover:underline "
              >
                <EmailIcon />
                <span className="px-2"><EMAIL></span>
              </a>
            </li>
            <li>
              <a
                href="https://www.messenger.com/login.php?next=https%3A%2F%2Fwww.messenger.com%2Ft%2F156836941045571%2F%3Fmessaging_source%3Dsource%253Apages%253Amessage_shortlink%26source_id%3D1441792%26recurring_notification%3D0"
                className="flex py-2 hover:text-primary  hover:underline"
              >
                <MessengerIcon />
                <span className="px-2">Facebook-Messenger</span>
              </a>
            </li>
            <li className="py-2  ">
              Mo-Fr 11:00-19:00 Uhr
              <br />
              Sa 10:00-18:00 Uhr
            </li>
            <li className="py-1  ">
              Innere Klosterstraße 13
              <br />
              09111 Chermniz
            </li>
          </ul>
        </div>
        <div className="flex flex-col">
          <span className="h-7 w-52 border-b-2 border-solid border-black   py-10  ">
            SERVICE & SUPPORT
          </span>
          <ul className="flex flex-col py-5 font-thin ">
            <li className="py-2">
              <Link
                href="/contact"
                className="py-2  hover:text-primary  hover:underline"
              >
                Kontakt
              </Link>
            </li>
            <li className="py-2">
              <Link
                href="/revocation"
                className="py-2  hover:text-primary  hover:underline"
              >
                Retoure & Reklamation
              </Link>
            </li>
            {/* <li className="py-2">
              <Link
                href=""
                className="py-2 hover:text-primary  hover:underline"
              >
                Liefrung & Versand
              </Link>
            </li> */}
            {/* <li className="py-2">
              <Link
                href=""
                className="py-2 hover:text-primary  hover:underline"
              >
                Bestellung Verfolgen
              </Link>
            </li> */}
            {/* <li className="py-2">
              <Link
                href=""
                className="py-2 hover:text-primary  hover:underline"
              >
                Größentabellen
              </Link>
            </li> 
            <li className="py-2">
              <Link
                href="/faq"
                className="py-2 hover:text-primary  hover:underline"
              >
                Hilfecenter
              </Link>
            </li>
            */}
          </ul>
        </div>

        <ul>
          <li className="h-7 w-64 border-b-2  border-solid border-black  py-10 ">
            ZAHLUNGSARTEN & VERSAND
          </li>

          <PaymentMethods />
        </ul>
        <ul className="py-10 lg:h-7 lg:w-44 lg:border-b-2  lg:border-solid lg:border-black">
          <li>
            <SocialMedia />
          </li>
        </ul>
      </section>

      <section className="flex h-20 items-center justify-around  bg-gray-bright  max-lg:hidden">
        <DefaultFootNote />
      </section>
      <div className=" flex flex-col bg-gray-bright px-5  lg:hidden">
        <MobileAccordion />

        <SocialMedia />
        <SmallFootNote />
      </div>
    </footer>
  );
};

export default Footer;
