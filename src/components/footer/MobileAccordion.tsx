import EmailIcon from "@components/Icons/EmailIcon";
import MessengerIcon from "@components/Icons/MessengerIcon";
import PhoneIcon from "@components/Icons/PhoneIcon";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@ui/accordion";
import Link from "next/link";

import PaymentMethods from "./PaymentMethods";

/**@description for responsive footer on mobile */

const MobileAccordion = () => {
  const PHONE_NUMBER = "+49 (0)371 666 00 81";
  const EMAIL = "<EMAIL>";
  return (
    <div>
      <Accordion type="single" collapsible className="w-full lg:hidden">
        <AccordionItem value="PLAZE STORE CHEMNITZ">
          <AccordionTrigger className="flex px-5  text-left font-normal   ">
            PLAZE STORE CHEMNITZ
          </AccordionTrigger>
          <AccordionContent className="flex  px-5">
            <p>
              Du hast ein problem oder eine Frage?<br></br> Wir sind fur dich
              da.
            </p>
            <Link
              href={`tel: ${PHONE_NUMBER}`}
              className="flex py-1 hover:text-primary   hover:underline"
            >
              <PhoneIcon />
              <p className="px-2">{PHONE_NUMBER}</p>
            </Link>
            <Link
              href={`email: ${EMAIL}`}
              className="flex  py-1 hover:text-primary  hover:underline "
            >
              <EmailIcon />
              <p className="px-2">{EMAIL}</p>
            </Link>
            <Link href="" className=" flex hover:text-primary  hover:underline">
              <MessengerIcon />
              <p className="px-2 py-1"> Facebook-Messenger</p>
            </Link>
            <p className="py-1">
              Mo-Fr 11:00-19:00 Uhr
              <br />
              Sa 10:00-18:00 Uhr
            </p>
            <p className="py-1">
              Innere Klosterstraße 13<br></br>09111 Chermniz
            </p>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="SERVICE & SUPPORT">
          <AccordionTrigger className="flex px-5  text-left font-normal ">
            SERVICE & SUPPORT
          </AccordionTrigger>
          <AccordionContent className="flex px-5">
            <div className="flex flex-col py-5 font-thin ">
              <Link
                href="/contact"
                className="py-1 hover:text-primary  hover:underline"
              >
                Kontakt
              </Link>
              <Link
                href="/revocation"
                className="py-1 hover:text-primary   hover:underline"
              >
                Retoure & Reklamation
              </Link>
              {/* <Link
                href=""
                className="py-1 hover:text-primary  hover:underline"
              >
                Liefrung & Versand
              </Link> */}
              {/* <Link
                href=""
                className="py-1 hover:text-primary  hover:underline"
              >
                Bestellung Verfolgen
              </Link> */}
              {/* <Link
                href=""
                className="py-1 hover:text-primary  hover:underline"
              >
                Grobentabellen
              </Link> 
              <Link
                href="/faq"
                className="py-1 hover:text-primary  hover:underline"
              >
                Hilfecenter
              </Link>
              */}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="ZAHLUNGSARTEN & VERSAND">
          <AccordionTrigger className="flex px-5 text-left font-normal">
            ZAHLUNGSARTEN & VERSAND
          </AccordionTrigger>
          <AccordionContent className="flex px-5">
            <PaymentMethods />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default MobileAccordion;
