import { cn, getChangePercentage } from "@lib/utils";
import { priceProps } from "@typesDeclarations/wooTypes/wooCartTypes";
import { FunctionComponent } from "react";
// TODO price and price cell function in the same component
export const Price: FunctionComponent<priceProps> = ({
  regularPrice,
  price,
  className,
}) => {
  const percentage = price ? getChangePercentage(regularPrice, price) : 0;
  return (
    <div className={cn("font-medium", className)} tabIndex={0}>
      {!price ? (
        regularPrice
      ) : (
        <div>
          <p className="text-destructive">{price}</p>
          <p className="text-xs">
            <span className=" text-sm font-normal text-gray-middle line-through">
              {regularPrice}
            </span>
            <br />
            <span className="font-normal text-gray-middle ">
              Du sparst {`${percentage}%`}
            </span>
          </p>
        </div>
      )}
    </div>
  );
};
