"use client";
import { Button } from "@components/ui/Button";
import { Icon } from "@components/ui/Icon";
import { Separator } from "@components/ui/separator";
import {
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON>ooter,
  She<PERSON><PERSON>eader,
  Sheet<PERSON>rigger,
} from "@components/ui/sheet";
import { HeartIcon, XMarkIcon } from "@heroicons/react/24/outline";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@radix-ui/react-tooltip";
import { FC, useEffect } from "react";

import { useUserStore } from "../../state/user";
import WishListProductCard from "./WishListProductCard";

/** @description renders modal and open/close button for the wishlist */
const WishlistModal: FC = () => {
  const wishlist = useUserStore((s) => s.wishlist);
  const synchronizeWishlist = useUserStore((s) => s.synchronizeWishlist);

  useEffect(() => {
    synchronizeWishlist();
  }, []);

  return (
    <Sheet>
      <SheetTrigger asChild>
        <div className=" flex flex-row items-center gap-x-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Button
                  id="update-wishlist-products-data-button"
                  asChild
                  variant={"ghost"}
                  size={"icon"}
                  className="p-2.5"
                >
                  <HeartIcon />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Merkzettel</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </SheetTrigger>
      <SheetContent
        className="flex w-full max-w-full flex-col bg-white"
        side={"right"}
      >
        <SheetHeader className="gap-x-2 bg-gray-bright pl-2">
          <div className="flex w-full items-center justify-between pl-2">
            <p className="w-full text-center capitalize ">
              Zum Warenkorb hinzugefügt
            </p>
            <SheetClose asChild>
              <Button
                variant={"outline"}
                size={"icon"}
                className="h-14 w-14 min-w-fit gap-x-2 border-0 bg-transparent p-2 font-light capitalize text-black"
              >
                <Icon className="aspect-square h-full w-full border-2 border-black">
                  <XMarkIcon className="mx-auto" />
                </Icon>
              </Button>
            </SheetClose>
          </div>
        </SheetHeader>
        {/* -------------------- BEGIN SHEET BODY -------------------- */}
        {wishlist.length > 0 ? (
          <ul className="mb-auto mt-0  max-h-full w-full   flex-grow  gap-y-1 overflow-y-auto">
            {wishlist.map((p) => (
              <li key={p.id}>
                <WishListProductCard product={p} />
              </li>
            ))}
          </ul>
        ) : (
          <p className="mx-auto flex min-w-full  justify-center">
            Keine Elemente gefunden
          </p>
        )}
        {/* --------------------- END SHEET BODY --------------------- */}
        <SheetFooter className="flex !flex-col">
          <Separator className="bg-gray-middle" />
          <div className="!m-0 [&>div]:px-4 [&>div]:py-2">
            <div className="flex flex-col p-4  [&>*]:my-1">
              <SheetClose asChild>
                <Button
                  className="h-12 w-full border-black"
                  variant={"outline"}
                >
                  WEITERSHOPPEN
                </Button>
              </SheetClose>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default WishlistModal;
