"use client";
import { PriceCell } from "@components/shoppingCart/ShoppingCartTable";
import Image from "next/image";
import Link from "next/link";
import { FC } from "react";

import { Product } from "../../state/schema";
import AddToWishListButton from "./AddToWishListButton";

// TODO refactor to account for prices (prices were removed in order to harmonize with current variationId handling mechanism for singlePageProduct)
const WishListProductCard: FC<{ product: Product }> = ({ product }) => {
  return (
    <article className="relative flex h-40 w-full max-w-full  overflow-hidden border-b-2 border-gray-light bg-white p-2 ">
      <div className="flex w-[30%]  min-w-[30%] max-w-[30%] items-center justify-center  ">
        <Link href={`/products/${product.slug}`}>
          <Image
            priority
            src={product.images[0]?.src.full ?? ""}
            alt={product.name}
            width={100}
            height={120}
            className=" hover:brightness-95"
          />
        </Link>
      </div>

      <div className=" flex h-full flex-grow flex-row  justify-between   px-4 ">
        <div className="my-auto gap-y-2 overflow-hidden text-ellipsis whitespace-pre-wrap py-2 ">
          <h4 className=" font-medium">{product.name}</h4>
          <br />
        </div>
        <div className="translate-x-16">
          <AddToWishListButton databaseId={product.id} name={product.name} />
        </div>
      </div>
      <div className="flex w-[25%] translate-y-28 flex-col  items-end justify-between">
        <PriceCell
          regularPrice={product.prices.regular_price}
          salePrice={product.prices.sale_price}
        />
      </div>
    </article>
  );
};

export default WishListProductCard;
