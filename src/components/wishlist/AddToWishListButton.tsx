"use client";
import { But<PERSON> } from "@components/ui/Button";
import { HeartIcon } from "@heroicons/react/24/outline";
import { cn } from "@lib/utils";
import { addToWishListButtonProps } from "@typesDeclarations/GenericUITypes/addToWishList";

import { useUserStore } from "../../state/user";

const AddToWishListButton = ({
  databaseId,
  className,
}: addToWishListButtonProps) => {
  const wishlist = useUserStore((s) => s.wishlist);
  const toggleWishlistItem = useUserStore((s) => s.toggleWishlistItem);
  const isClicked = !!wishlist.find((s) => s.id === databaseId);

  return (
    <Button
      onClick={async () => {
        toggleWishlistItem(databaseId);
      }}
      title={`Zur Wunschliste hinzufügen`}
      variant={"secondary"}
      size={"icon"}
      className={cn(
        "relative bottom-1 right-1 z-10 bg-transparent p-2 ",
        className
      )}
    >
      <HeartIcon
        className={` ${isClicked ? "fill-current" : "hover:fill-current"}`}
      />
    </Button>
  );
};
export default AddToWishListButton;
