import React from "react";
import zxcvbn from "zxcvbn";
/**
 * @description
 * Helper component that used to check the strength of the password
 * @param password string which we want to check its strength
 * @returns indicator which display the strength of the given string
 */
const PasswordStrength = ({ password }: { password: string }) => {
  const passwordStrength = () => {
    let strength = { name: "", color: "" };
    switch (zxcvbn(password).score) {
      case 0:
        strength = { name: "Sehr schwach", color: "bg-password-very_weak" };
        break;
      case 1:
        strength = { name: "schwach", color: "bg-password-weak" };
        break;
      case 2:
      case 3:
        strength = { name: "<PERSON><PERSON><PERSON>", color: "bg-password-medium" };
        break;
      case 4:
        strength = { name: "Stark", color: "bg-password-strong" };
        break;
    }
    return strength;
  };

  const { name, color } = passwordStrength();
  return (
    <>
      <div className={`mt-4 sm:w-96 ${color}`}>
        <p className="text-center text-sm">{name}</p>
      </div>
    </>
  );
};

export default PasswordStrength;
