"use client";
import "swiper/css";
import "swiper/css/navigation";

import { Button } from "@components/ui/Button";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { brandsSliderData } from "@typesDeclarations/homePageTypes";
import Image from "next/image";
import Link from "next/link";
import { FC } from "react";
import { A11y, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide, useSwiper } from "swiper/react";

import { brand } from "../../lib/utilities/brandsHelpers";

interface brandsSliderProps {
  data: brandsSliderData;
}
interface brandsProps {
  data: brand[];
}
function TopSwiperButtons() {
  const swiper = useSwiper();

  return (
    <div className="absolute top-[35%] z-20 flex w-full justify-between max-md:top-[50%]">
      <Button
        variant={"ghost"}
        className="relative -translate-x-12 hover:bg-foreground hover:text-white"
        onClick={() => {
          swiper.slidePrev();
        }}
      >
        <ChevronLeftIcon className="absolute left-1/2 top-1/2 h-full w-full -translate-x-1/2 -translate-y-1/2" />
      </Button>
      <Button
        variant={"ghost"}
        className="relative -translate-x-12 hover:bg-foreground hover:text-white"
        onClick={() => {
          swiper.slideNext();
        }}
      >
        <ChevronRightIcon className="absolute left-1/2 top-1/2 h-full w-full -translate-x-1/2 -translate-y-1/2" />
      </Button>
    </div>
  );
}

const BrandsSlider: FC<brandsSliderProps | brandsProps> = ({ data }) => {
  const imageDefault =
    "https://plaze.k3.network/wp-content/uploads/2021/08/Adidas_klassisches_logo.svg";
  return (
    data && (
      <section className="w-full">
        <Swiper
          slidesPerView={7}
          spaceBetween={50}
          modules={[A11y, Navigation]}
          breakpoints={{
            320: {
              slidesPerView: 1,
              spaceBetween: 50,
            },
            640: {
              slidesPerView: 3,
              spaceBetween: 50,
            },
            768: {
              slidesPerView: 5,
              spaceBetween: 60,
            },
            1024: {
              slidesPerView: 7,
              spaceBetween: 70,
            },
          }}
          centeredSlides={true}
          centeredSlidesBounds={true}
          className="relative flex !px-12 !py-4 max-sm:w-5/6 lg:w-4/5"
        >
          {Array.isArray(data) &&
            data.map((item) => {
              // TODO: If no image src property from Wordpress is undefined make the webpage raise an error
              // const noImage = !!item.imageURL;
              // if (!noImage) {
              //   const error = new Error("missing image src property from Wordpress");
              //   throw error;
              // }

              return (
                <SwiperSlide key={item.slug} className="absolute my-auto">
                  {
                    <Link
                      href={`products/?refinementList[brands.name][0]=${item.name}`}
                    >
                      <Image
                        width={200}
                        height={200}
                        alt={item.name ?? ""}
                        src={item.imageURL ? item.imageURL : imageDefault}
                        className="bg-fill h-full w-full transition-all"
                        sizes="(max-width: 639px) 100vw, (max-width: 1023px): 20vw, 15vw"
                      />
                    </Link>
                  }
                </SwiperSlide>
              );
            })}
          <TopSwiperButtons />
        </Swiper>
      </section>
    )
  );
};

export default BrandsSlider;
