// some-inner-component.jsx
import { Button } from "@components/ui/Button";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { useSwiper } from "swiper/react";

/**
 * @description
 * contain chevron left and right for swiper in product slider
 */

function TopSwiperButtons() {
  const swiper = useSwiper();

  return (
    <div className=" absolute right-0 z-10 flex  gap-x-4 max-md:hidden">
      <Button
        variant={"ghost"}
        className="translate-x-5 hover:bg-foreground hover:text-white"
        onClick={() => {
          swiper.slidePrev();
        }}
      >
        <ChevronLeftIcon className="h-full w-full " />
      </Button>
      <Button
        variant={"ghost"}
        className="hover:bg-foreground hover:text-white"
        onClick={() => {
          swiper.slideNext();
        }}
      >
        <ChevronRightIcon className="h-full w-full" />
      </Button>
    </div>
  );
}

function InlineSwiperButtons() {
  const swiper = useSwiper();

  return (
    <div className="absolute top-[50%] z-10 flex w-full justify-between md:hidden ">
      <Button
        title="swipe-left-button"
        variant={"ghost"}
        className="hover:bg-foreground hover:text-white"
        onClick={() => {
          swiper.slidePrev();
        }}
      >
        <ChevronLeftIcon className="h-full w-full " />
      </Button>
      <Button
        title="swipe-right-button"
        variant={"ghost"}
        className="hover:bg-foreground hover:text-white"
        onClick={() => {
          swiper.slideNext();
        }}
      >
        <ChevronRightIcon className="h-full w-full" />
      </Button>
    </div>
  );
}

export { InlineSwiperButtons, TopSwiperButtons };
