import { _processCategorySliderData } from "@lib/utilities/homePageHelpers";
import { homePageDataResponse } from "@typesDeclarations/homePageTypes";
import React from "react";

import { SwiperSlider } from "../EntitiesSliderComponents/SwiperSlider";

const CategorySlider: React.FC<{
  data: homePageDataResponse["productSliderResponse"];
}> = async ({ data }) => {
  const processedData = await _processCategorySliderData(data);
  return <SwiperSlider data={processedData} />;
};

export default CategorySlider;
