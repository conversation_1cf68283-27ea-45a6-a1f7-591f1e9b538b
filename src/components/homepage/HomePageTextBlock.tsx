"use client";
import "../../app/textarea.css";

import { textBlockData } from "@typesDeclarations/homePageTypes";
import ReactHtmlParser from "html-react-parser";
import { FC } from "react";

interface acf_textAreaBlock {
  data: textBlockData;
}

const HomePageTextBlock: FC<acf_textAreaBlock> = ({ data }) => {
  return (
    data && (
      <section className="group w-full">
        <div className="m-auto w-10/12 border-b-2 py-8 group-last:border-b-0">
          <div className="m-auto max-sm:w-5/6 md:w-1/2 lg:w-1/3">
            <div className="text-center font-bold">
              {ReactHtmlParser(data?.title)}
            </div>
            <div className="!pt-3 text-left">
              {ReactHtmlParser(
                `<div class="listItem-textarea">${data.text}</div>`
              )}
            </div>
          </div>
        </div>
      </section>
    )
  );
};

export default HomePageTextBlock;
