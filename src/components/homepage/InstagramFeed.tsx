"use client";
import ImagesGroupInsta from "@components/Icons/ImagesGroupInstagram";
import InstagramWhiteIcon from "@components/Icons/InstagramWhiteIcon";
import ReelsInsta from "@components/Icons/ReelsInstaIcon";
import { Button } from "@components/ui/Button";
import { instagramPostsData } from "@typesDeclarations/homePageTypes";
import Image from "next/image";
import Link from "next/link";
import { FC } from "react";

import getTimeDifference from "../../lib/utilities/TimeDifference";

interface instagramFeedProps {
  data: instagramPostsData;
}

const InstagramFeed: FC<instagramFeedProps> = ({ data }) => {
  return (
    data && (
      <section className="w-full !pt-36">
        <div className="top-0 z-10 mb-8 flex w-full items-center">
          <p className="mx-auto flex justify-center border-b-4 border-solid border-foreground px-4 py-1 text-center uppercase max-sm:w-72 sm:max-w-fit">
            INSTAGRAM
          </p>
        </div>
        <div className="flex gap-4 max-sm:flex-col lg:flex-row">
          {data?.map((item) => {
            const imageUrl =
              item.media_type === "VIDEO" ? item.thumbnail_url : item.media_url;
            return (
              <div
                key={item.id}
                className="group relative w-1/4 max-md:w-1/3 max-sm:w-full"
              >
                <Link href={item.permalink}>
                  <Image
                    width={200}
                    height={200}
                    alt={item.caption}
                    src={imageUrl}
                    className="bg-fill hover:filter-grayscale aspect-square h-full w-full overflow-hidden bg-cover hover:brightness-50"
                  />
                  {item.media_type === "CAROUSEL_ALBUM" && (
                    <div className="absolute right-3 top-2">
                      <ImagesGroupInsta />
                    </div>
                  )}
                  {item.media_type === "VIDEO" && (
                    <div className="absolute right-3 top-2">
                      <ReelsInsta />
                    </div>
                  )}
                  <div className="opacity-0 group-hover:opacity-100">
                    <div className="absolute bottom-2 right-3">
                      <InstagramWhiteIcon />
                    </div>
                    <div className="absolute bottom-2 right-[41%] text-white">
                      {getTimeDifference(item.timestamp)}
                    </div>
                  </div>
                </Link>
              </div>
            );
          })}
        </div>
        <div className="my-8 flex items-center">
          <Link
            href={"https://www.instagram.com/plaze_skateshop/"}
            target="_blank"
            className="mx-auto flex justify-center"
          >
            <Button className="bg-black">plaze skateshop</Button>
          </Link>
        </div>
      </section>
    )
  );
};

export default InstagramFeed;
