import "swiper/css";
import "swiper/css/pagination";
import "../../../app/swiper.css";

import { Button } from "@components/ui/Button";
import { cn } from "@lib/utils";
import { sliderData } from "@typesDeclarations/homePageTypes";
import Image from "next/image";
import Link from "next/link";
import React, { useState } from "react";
import { A11y, Autoplay, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

/**
 *
 * @description  the HeroGridSwiper component is responsible for managing SwiperSlide
 * including Image and text
 *
 */

function HeroGridSwiper({
  imageData,
  className,
}: {
  imageData: sliderData;
  className?: string;
}) {
  const [activeSlide, setActiveSlide] = useState(0);
  return (
    <div className={cn("h-full w-full", className)}>
      <Swiper
        centeredSlides={true}
        autoplay={{
          delay: 2500,
          disableOnInteraction: false,
        }}
        pagination={{
          clickable: true,
        }}
        slidesPerView={1}
        modules={[Autoplay, Pagination, A11y]}
        className="h-full w-full"
        onSlideChange={(swiper) => setActiveSlide(swiper.activeIndex)}
      >
        {/* using React key as index as seen on Swiper js docs */}
        {/* TODO: replace the index with proper unique identifier when real data is used */}
        {imageData.url
          ? imageData.media.map((item, index: React.Key) => (
              <SwiperSlide key={index}>
                <Link
                  href={`${imageData.url}`}
                  className="group/card items-center"
                >
                  <Image
                    alt={item?.name ?? ""}
                    src={item.imageURL}
                    sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
                    fill
                    priority
                    style={{ objectFit: "cover" }}
                    className={`bg-fill  h-full  w-full object-cover object-center  transition-all 
                ${index === activeSlide ? "block" : "hidden"}`}
                  />
                  {imageData.text && imageData.url && (
                    <Button
                      variant={"outline"}
                      size={"default"}
                      className="absolute bottom-4 left-0 right-0 mx-auto flex w-3/4 min-w-fit max-w-[30ch] items-end justify-center bg-transparent text-primary-foreground group-hover/card:bg-foreground group-hover/card:text-background "
                    >
                      {imageData.text}
                    </Button>
                  )}
                </Link>
              </SwiperSlide>
            ))
          : imageData.media.map((item, index: React.Key) => (
              <SwiperSlide key={index}>
                <Image
                  priority
                  alt={item?.name ?? ""}
                  src={item.imageURL}
                  sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
                  fill
                  style={{ objectFit: "cover" }}
                  className={`bg-fill  h-full  w-full object-cover object-center  transition-all 
                  ${index === activeSlide ? "block" : "hidden"}`}
                />
              </SwiperSlide>
            ))}
      </Swiper>
    </div>
  );
}

export default HeroGridSwiper;
