"use client";
import { videoDisplay } from "@typesDeclarations/homePageTypes";
import { useEffect, useState } from "react";
import ReactPlayer from "react-player";

/**
 *
 * @description  the HeroGridVideo component is responsible for managing Video
 * including video and text
 *
 */
function HeroGridVideo({ cardData }: { cardData: videoDisplay }) {
  const { title, url } = cardData;

  /** checks whether the video is uploaded from WordPress or loaded from Youtube/Vimeo */
  const isUploadedOnWordpress =
    url.toLowerCase().includes("youtube") ||
    url.toLowerCase().includes("vimeo");

  // Use useState and useEffect to solve hydration problem
  // https://nextjs.org/docs/messages/react-hydration-error#possible-ways-to-fix-it
  // isClient State is used to know whether the page is rendering clientSide or serverSide
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="relative h-full w-full items-center overflow-hidden">
      {isClient && ( // TODO should assert proper behaviour for all possible video sources
        <ReactPlayer
          playsinline={isUploadedOnWordpress}
          url={url}
          controls={false}
          loop={true}
          muted={true}
          playing={true}
          height={"100%"}
          width={"100%"}
        />
      )}
      <p className="absolute bottom-8 left-8 text-lg font-medium capitalize text-primary-foreground">
        {title}
      </p>
    </div>
  );
}

export default HeroGridVideo;
