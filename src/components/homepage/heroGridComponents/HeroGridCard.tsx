import { Button } from "@components/ui/Button";
import { imageDisplay } from "@typesDeclarations/homePageTypes";
import Image from "next/image";
import Link from "next/link";

/**
 *
 * @description  the HeroGridCard component is responsible for managing Cards
 * including Image and text
 *
 */

function HeroGridCard({
  cardData: { alt, imageURL, name, slug },
}: {
  cardData: imageDisplay;
}) {
  console.log(alt, imageURL, name, slug);
  return (
    <div className="relative h-full w-full overflow-hidden">
      {slug ? (
        <Link href={`${slug}`} className="group/card items-center">
          <Image
            alt={alt ?? ""}
            src={imageURL}
            className=" object-cover object-center transition-all duration-500 ease-in-out hover:scale-110 hover:brightness-50  "
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
            fill
            style={{ objectFit: "cover" }}
            priority
          />
          {name && (
            <Button
              variant={"outline"}
              size={"default"}
              className="absolute bottom-4 left-0 right-0 mx-auto flex w-3/4 min-w-fit max-w-[30ch] items-end justify-center bg-transparent text-primary-foreground group-hover/card:bg-foreground group-hover/card:text-background "
            >
              {name}
            </Button>
          )}
        </Link>
      ) : (
        <div className="group/card items-center">
          <Image
            alt={alt ?? ""}
            src={imageURL}
            className=" object-cover object-center transition-all duration-500 ease-in-out hover:scale-110 hover:brightness-50  "
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
            fill
            style={{ objectFit: "cover" }}
            priority
          />
          {name && (
            <Button
              variant={"outline"}
              size={"default"}
              className="absolute bottom-4 left-0 right-0 mx-auto flex w-3/4 min-w-fit max-w-[30ch] items-end justify-center bg-transparent text-primary-foreground group-hover/card:bg-foreground group-hover/card:text-background "
            >
              {name}
            </Button>
          )}
        </div>
      )}
    </div>
  );
}

export default HeroGridCard;
