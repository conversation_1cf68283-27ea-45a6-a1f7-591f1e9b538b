import { heroGridData } from "@typesDeclarations/homePageTypes";

import HeroGridCard from "../HeroGridCard";
import HeroGridSwiper from "../HeroGridSwiper";

/** @includes: Slider - Card - Card -Card */
function Layout0(data: heroGridData & { type: 0 }) {
  const { categories, sliderData } = data;
  return (
    <div
      id="homepage-hero-grid"
      className="grid min-h-[40rem] w-full min-w-[96dvw] grid-cols-1 grid-rows-4 gap-4 px-4 lg:h-[40rem] lg:grid-cols-4 lg:grid-rows-2 "
    >
      <div className=" col-start-1 col-end-2  row-start-1 row-end-2  aspect-square w-full  lg:col-start-1 lg:col-end-3 lg:row-start-1 lg:row-end-3 lg:aspect-auto lg:h-auto lg:w-auto   ">
        <HeroGridSwiper imageData={sliderData} />
      </div>
      <div className="col-start-1 col-end-2 row-start-2 row-end-3 aspect-square w-full  lg:col-start-3  lg:col-end-4 lg:row-start-1  lg:row-end-2 lg:aspect-auto lg:h-auto lg:w-auto ">
        <HeroGridCard cardData={categories[0]} />
      </div>
      <div className="col-start-1 col-end-2 row-start-3 row-end-4 aspect-square w-full  lg:col-start-3 lg:col-end-4 lg:row-start-2  lg:row-end-3 lg:aspect-auto lg:h-auto lg:w-auto">
        <HeroGridCard cardData={categories[1]} />
      </div>
      <div className="col-start-1 col-end-2 row-start-4 row-end-5 aspect-square w-full lg:col-start-4 lg:col-end-5 lg:row-start-1  lg:row-end-3 lg:aspect-auto lg:h-auto lg:w-auto ">
        <HeroGridCard cardData={categories[2]} />
      </div>
    </div>
  );
}

export default Layout0;
