import { heroGridData } from "@typesDeclarations/homePageTypes";

import HeroGridCard from "../HeroGridCard";
import HeroGridSwiper from "../HeroGridSwiper";

/** @includes: slider - Card - Card */
function Layout1(data: heroGridData & { type: 1 }) {
  const { categories, sliderData } = data;
  return (
    <div
      id="homepage-hero-grid"
      className="grid min-h-[40rem] w-full min-w-[96dvw] grid-cols-1 grid-rows-3 gap-4 px-4 lg:h-[40rem] lg:grid-cols-4 lg:grid-rows-1"
    >
      <div className="col-start-1 col-end-2  row-start-1 row-end-2 w-full lg:col-start-1 lg:col-end-3 lg:row-start-1 lg:row-end-1 lg:aspect-auto lg:h-auto lg:w-auto">
        <HeroGridSwiper imageData={sliderData} />
      </div>
      <div className="col-start-1 col-end-2 row-start-2 row-end-3 lg:col-start-3 lg:col-end-4 lg:row-start-1 lg:row-end-1 lg:aspect-auto lg:h-auto lg:w-auto">
        <HeroGridCard cardData={categories[0]} />
      </div>
      <div className="col-start-1 col-end-2 row-start-3 row-end-4 lg:col-start-4 lg:col-end-5 lg:row-start-1 lg:row-end-1 lg:aspect-auto lg:h-auto lg:w-auto">
        <HeroGridCard cardData={categories[1]} />
      </div>
    </div>
  );
}

export default Layout1;
