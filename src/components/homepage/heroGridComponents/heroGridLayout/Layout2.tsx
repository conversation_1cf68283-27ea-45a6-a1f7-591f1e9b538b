import { heroGridData } from "@typesDeclarations/homePageTypes";

import HeroGridCard from "../HeroGridCard";
import HeroGridSwiper from "../HeroGridSwiper";

/** @includes: slider - Card */
function Layout2(data: heroGridData & { type: 2 }) {
  const { productData, sliderData } = data;
  return (
    <div
      id="homepage-hero-grid"
      className=" grid min-h-[40rem] w-full min-w-[96dvw] grid-cols-1 grid-rows-4 gap-4  px-4 lg:h-[40rem] lg:grid-cols-4 lg:grid-rows-2 "
    >
      <div className=" col-start-1 col-end-2  row-start-1 row-end-2  aspect-square w-full  lg:col-start-1 lg:col-end-3 lg:row-start-1 lg:row-end-3 lg:aspect-auto lg:h-auto lg:w-auto   ">
        <HeroGridSwiper imageData={sliderData} />
      </div>
      <div className="col-start-1 col-end-2 row-start-2 row-end-3 aspect-square w-full  lg:col-start-3  lg:col-end-5 lg:row-start-1  lg:row-end-3 lg:aspect-auto lg:h-auto lg:w-auto ">
        <HeroGridCard cardData={productData} />
      </div>
    </div>
  );
}

export default Layout2;
