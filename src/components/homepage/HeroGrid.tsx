"use client";
import { heroGridData } from "@typesDeclarations/homePageTypes";
import { FC } from "react";

import Layout0 from "./heroGridComponents/heroGridLayout/Layout0";
import Layout1 from "./heroGridComponents/heroGridLayout/Layout1";
import Layout2 from "./heroGridComponents/heroGridLayout/Layout2";
import Layout3 from "./heroGridComponents/heroGridLayout/Layout3";
import Layout4 from "./heroGridComponents/heroGridLayout/Layout4";

/**
 *
 * @description  the HeroGrid component is responsible for managing all LayoutGrit
 * including Layout 1 to 5
 *
 */

interface heroGridProps {
  data: heroGridData;
}

const HeroGrid: FC<heroGridProps> = ({ data }) => {
  const type = data.type;

  switch (type) {
    case 0:
      return <HeroGrid_Type0 data={data} />;
    case 1:
      return <HeroGrid_Type1 data={data} />;
    case 2:
      return <HeroGrid_Type2 data={data} />;
    case 3:
      return <HeroGrid_Type3 data={data} />;
    case 4:
      return <HeroGrid_Type4 data={data} />;
    default:
      return (
        <div className="bg-destructive font-medium text-destructive-foreground">
          Ungültiger Rasterlayouttyp
        </div>
      );
  }
};

export default HeroGrid;

const HeroGrid_Type0: FC<{ data: heroGridData & { type: 0 } }> = ({ data }) => {
  const { sliderData, categories } = data;

  return <Layout0 type={0} sliderData={sliderData} categories={categories} />;
};

const HeroGrid_Type1: FC<{ data: heroGridData & { type: 1 } }> = ({ data }) => {
  const { sliderData, categories } = data;

  return <Layout1 type={1} sliderData={sliderData} categories={categories} />;
};

const HeroGrid_Type2: FC<{ data: heroGridData & { type: 2 } }> = ({ data }) => {
  const { sliderData, productData: ProductData } = data;

  return <Layout2 type={2} sliderData={sliderData} productData={ProductData} />;
};

const HeroGrid_Type3: FC<{ data: heroGridData & { type: 3 } }> = ({ data }) => {
  const { sliderData, categories, videoData } = data;

  return (
    <Layout3
      type={3}
      sliderData={sliderData}
      videoData={videoData}
      categories={categories}
    />
  );
};

const HeroGrid_Type4: FC<{ data: heroGridData & { type: 4 } }> = ({ data }) => {
  const { categories, videoData } = data;

  return <Layout4 type={4} videoData={videoData} categories={categories} />;
};
