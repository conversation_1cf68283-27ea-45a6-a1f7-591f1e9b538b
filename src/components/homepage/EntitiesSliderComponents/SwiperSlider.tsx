"use client";
import "swiper/css";
import "swiper/css/navigation";

import ProductSlider from "@components/general/ProductSlider";
import { getChangePercentage } from "@lib/utils";

/**
 * @description
 * swiper slider contain products in slider
 */

export const SwiperSlider = ({ data }: { data: any }) => {
  return (
    <div>
      <div className="z-10 flex w-full items-center">
        <p className="mx-auto flex justify-center border-b-2 border-solid border-foreground px-4 text-center uppercase  max-sm:w-72 sm:max-w-fit ">
          {data?.categoryName}
        </p>
      </div>
      <div className="flex justify-center">
        <div className="grid grid-cols-1 gap-5 pt-16 max-sm:w-5/6 sm:grid-cols-2 md:w-3/4 md:gap-10 lg:w-4/6 lg:grid-cols-3 lg:gap-2.5">
          {data &&
            data.products.slice(0, 9).map((product: any) => {
              const hasSale = product.regularPrice === product.price;
              return (
                <div key={product.name}>
                  {hasSale ? (
                    <ProductSlider
                      key={product.slug}
                      slug={product.slug}
                      sourceImage={product.imageURL}
                      description={product.name}
                      newPrice={product.price}
                      className="text-black"
                    />
                  ) : (
                    <ProductSlider
                      key={product.slug}
                      slug={product.slug}
                      sourceImage={product.imageURL}
                      discount={`${getChangePercentage(
                        product.regularPrice,
                        product.price
                      ).toString()} %`}
                      description={product.name}
                      oldPrice={product.regularPrice}
                      newPrice={product.price}
                      className="text-destructive"
                    />
                  )}
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};
