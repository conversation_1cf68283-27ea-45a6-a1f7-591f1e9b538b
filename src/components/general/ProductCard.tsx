"use client";
import "../../app/algolia.css";

import AddToWishListButton from "@components/wishlist/AddToWishListButton";
import { getChangePercentage, getPriceFormatter } from "@lib/utils";
import Image from "next/image";
import Link from "next/link";
import PropTypes from "prop-types";

/**
 * @description
 * contain product image and description and price
 *[use it in single category page]
 */
const ProductCard = ({ hit }: { hit: any }) => {
  const { deutschFormatter } = getPriceFormatter(true, false);

  return (
    <div className="bg-Plaze-white group relative w-full">
      <div className="flex items-center justify-between bg-white">
        <div className="absolute left-0 top-1 hidden max-md:hidden max-sm:hidden md:group-hover:block">
          <AddToWishListButton databaseId={hit.objectID} name={hit.slug} />
        </div>
        {hit.regular_price === hit.price ||
        hit.regular_price === "" ||
        hit.sale_price === "" ? (
          ""
        ) : (
          <div className="flex max-w-sm">
            <h1 className="absolute right-2 top-1 z-10">
              {`-${getChangePercentage(
                hit.regular_price,
                hit.sale_price
              ).toString()}`}
              %
            </h1>
          </div>
        )}
      </div>
      <Link href={`products/${hit.slug}`}>
        <div className=" w-full overflow-hidden">
          <div className=" w-full  bg-white">
            <div className="flex aspect-square w-full justify-between overflow-hidden ">
              <Image
                src={hit.images[0]}
                alt={hit.name}
                className="h-full w-full object-cover object-center transition-all duration-500 ease-in-out group-hover:scale-105 sm:h-full sm:w-full"
                width={512}
                height={512}
                sizes="(max-width: 639px) 100vw, (max-width: 1023px) 50vw, (max-width: 1279px) 33vw, 25vw"
              />
            </div>
          </div>
        </div>

        <div className="flex flex-1 flex-col space-y-2 bg-background ">
          <p className="mt-4 line-clamp-3 text-sm text-gray-700">{hit.name}</p>
          <div className="mt-2">
            {hit.stock_status === "outofstock" ? (
              <div className="flex h-10 w-fit flex-wrap justify-center overflow-hidden  bg-input px-3 py-2 ">
                <p className="font-medium text-destructive">ausverkauft</p>
              </div>
            ) : hit.regular_price === hit.price ||
              hit.regular_price === "" ||
              hit.sale_price === "" ? (
              <div className="flex h-10 w-fit flex-wrap justify-center overflow-hidden  bg-input px-3 py-2 ">
                <p className="font-medium ">{deutschFormatter(hit.price)}</p>
              </div>
            ) : (
              <div className="  flex h-10 w-fit flex-wrap  justify-center overflow-hidden  bg-input px-3 py-2 ">
                <p className=" mr-2 line-through">
                  {deutschFormatter(hit.regular_price)}
                </p>
                <p className="font-medium text-destructive ">
                  {deutschFormatter(hit.price)}
                </p>
              </div>
            )}
          </div>
        </div>
        <div className="flex  flex-wrap items-center  font-black  md:hidden lg:hidden">
          <AddToWishListButton databaseId={hit.objectID} name={hit.slug} />
          Merken
        </div>
      </Link>
    </div>
  );
};

ProductCard.propTypes = {
  hit: PropTypes.object.isRequired,
};

export default ProductCard;
