"use client";
import { ProductCardProps } from "@lib/utilities/PageHelper";
import { cn } from "@lib/utils";
import Image from "next/image";
import Link from "next/link";
import React from "react";

/**
 * @description
 * contain product image and description and price
 *[use it in product slider in home page]
 */
const ProductSlider: React.FC<ProductCardProps> = ({
  sourceImage,
  discount,
  description,
  oldPrice,
  newPrice,
  className,
  slug,
}) => {
  return (
    <div className="mb-16">
      <Link href={`/products/${slug}`}>
        <div className="relative flex flex-col bg-white px-10 py-10 ">
          {discount && (
            <div className="absolute right-2 top-2 z-50 flex justify-end">
              <h1 className="self-start">-{discount}</h1>
            </div>
          )}
          <Image
            src={sourceImage}
            alt={description}
            width={370}
            height={370}
            className="bg-Plaze-white h-fit w-fit"
            sizes="(max-width: 639px) 100vw, (max-width: 1023px): 50vw, 25vw"
          />
        </div>
        <div className="h-auto ">
          <p className="xxl:py-4 overflow-hidden px-4 py-3 ">{description}</p>
        </div>
        <div className="px-4 py-1  ">
          <div className="flex h-10 w-fit flex-wrap justify-center  overflow-hidden bg-input py-2 ">
            <p className=" mr-3 line-through ">{oldPrice}</p>

            <p className={cn("mr-2  font-medium", className)}>{newPrice}</p>
          </div>
        </div>
      </Link>
    </div>
  );
};

export default ProductSlider;
