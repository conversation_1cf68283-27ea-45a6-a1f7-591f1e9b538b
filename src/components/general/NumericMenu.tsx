import React, { useState } from "react";
import {
  useClearRefinements,
  useNumericMenu,
  UseNumericMenuProps,
} from "react-instantsearch";

const NumericMenu = (props: UseNumericMenuProps) => {
  const { refine: clearRefined } = useClearRefinements();
  const { items, refine } = useNumericMenu(props);
  const [selectedRefinement, setSelectedRefinement] = useState<string>("");
  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    if (value === selectedRefinement) {
      setSelectedRefinement("");
      clearRefined();
      return;
    }
    setSelectedRefinement(value);
    refine(value);
  };

  return (
    <div>
      <ul>
        {items.map((item) => (
          <li key={item.value}>
            <label className="inline-flex items-center">
              <input
                type="checkbox"
                name={item.value}
                value={item.value}
                checked={item.isRefined}
                onChange={handleRadioChange}
                className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"
              />
              <span className="ml-2">{item.label}</span>
            </label>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default NumericMenu;
