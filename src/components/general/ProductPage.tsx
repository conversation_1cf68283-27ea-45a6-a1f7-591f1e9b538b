"use client";
import "instantsearch.css/themes/satellite.css";

import { EmptyComponent } from "@components/algolia-search/NoResultsFound";
import ProductCard from "@components/general/ProductCard";
import ScrollToTopButton from "@components/general/ScrollToTopButton";
import Home from "@components/Home";
import FilterCard from "@components/ui/FilterCard";
import MobileSelect from "@components/ui/MobileSelect";
import SelectCard from "@components/ui/SelectCard";
import { searchClient } from "@lib/typesense/typesenseClient";
import { singleIndex } from "instantsearch.js/es/lib/stateMappings";
import { useSearchParams } from "next/navigation";
import { Configure, Hits, Stats } from "react-instantsearch";
import { useStats } from "react-instantsearch";
import { InstantSearchNext } from "react-instantsearch-nextjs";
//TODO dynamic meta  data
export function CustomStats() {
  const { nbHits } = useStats();

  return (
    <>
      {nbHits === 0 ? (
        <div className="justify-center md:mx-auto lg:mx-32 lg:my-[10%]">
          <EmptyComponent />
        </div>
      ) : (
        <div>
          <Hits
            hitComponent={ProductCard}
            classNames={{
              list: "grid grid-cols-1 gap-y-4 sm:grid-cols-2 sm:gap-x-6 sm:gap-y-6 lg:gap-x-1 xl:grid-cols-3 ",
              item: "relative bg-white rounded-lg flex flex-col overflow-hidden ",
            }}
          />
          <div className="bg-background px-6">
            <div className="flex justify-center border-b-2 border-solid border-black py-3">
              <Home />
            </div>
            <div className="mb-32 self-start">
              <Stats
                translations={{
                  rootElementText: ({ nbHits }) => `${nbHits} Artikel`,
                }}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default function ProductPage() {
  const searchParams = useSearchParams();
  const category = searchParams.get(`hierarchicalMenu[category.lvl0][0]`);
  return (
    <div className="mx-4 md:mx-20 xl:mx-28">
      <InstantSearchNext
        indexName="products"
        searchClient={searchClient}
        future={{ preserveSharedStateOnUnmount: true }}
        routing={{ stateMapping: singleIndex("products") }}
        key={category}
      >
        <Configure hitsPerPage={12} />
        <div className="flex grid-cols-[260px_1fr] flex-col gap-5 bg-background max-sm:items-center lg:grid xl:grid-cols-[300px_1fr] 2xl:grid-cols-[350px_1fr]">
          <div className="w-full">
            <FilterCard />
            <MobileSelect />
          </div>
          <div className=" mt-5 ">
            <div className="hidden items-center justify-between lg:flex">
              <div className="bg-background px-2 pb-[10px] max-sm:hidden  ">
                <h1 className="mt-5 w-fit ">
                  <Stats
                    translations={{
                      rootElementText: ({ nbHits }) => `${nbHits} Artikel`,
                    }}
                  />
                </h1>
              </div>
              <div className="">
                <SelectCard />
              </div>
            </div>

            <div className=" mb-8 flex justify-between max-md:justify-center">
              <CustomStats />
            </div>
          </div>
        </div>

        <ScrollToTopButton />
      </InstantSearchNext>
    </div>
  );
}
