"use client";
import { Button } from "@components/ui/Button";
import ArrowUp from "@heroicons/react/24/outline/ArrowLongUpIcon";
import { useEffect, useState } from "react";

const ScrollToTopButton = () => {
  // Only show the button when the user scrolls down
  const [isVisible, setIsVisible] = useState(false);

  const toggleButtonVisibility = () => {
    if (window && window.scrollY > 200) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  };

  useEffect(() => {
    // To avoid ReferenceError potentials
    if (!window) {
      return;
    }
    window.addEventListener("scroll", toggleButtonVisibility);

    // Clean up all subscriptions when the component unmounts
    return () => {
      window.removeEventListener("scroll", toggleButtonVisibility);
    };
  }, []);

  const scrollToTop = () => {
    // To avoid ReferenceError potentials
    if (!window) {
      return;
    }
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    isVisible && (
      // I used static values for the width and height of the container to ensure consistency
      <div className="fixed bottom-[10dvh] right-4 z-10 h-[50px] w-[45px]">
        <Button
          onClick={scrollToTop}
          size={"icon"}
          className="h-full w-full bg-black py-5"
        >
          <ArrowUp className="w-full" />
        </Button>
      </div>
    )
  );
};

export default ScrollToTopButton;
