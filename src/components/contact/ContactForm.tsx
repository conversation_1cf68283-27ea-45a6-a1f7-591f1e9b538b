"use client";
import { Button } from "@components/ui/Button";
import { Checkbox } from "@components/ui/checkbox";
import ContactSelect from "@components/ui/ContactSelect";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@components/ui/form";
import { Input } from "@components/ui/input";
import { Label } from "@components/ui/label";
import { useToast } from "@components/ui/use-toast";
import { ChevronRightIcon } from "@heroicons/react/24/outline";
import { zodResolver } from "@hookform/resolvers/zod";
import { sendEmail } from "@lib/restAPI/helpers/sendEmailHelper";
import { useRouter } from "next/navigation";
import { FieldValues, useForm } from "react-hook-form";
import * as z from "zod";

import { FormSchema } from "./FormSchhema";

export const ContactForm: React.FC = () => {
  const { toast } = useToast();
  const router = useRouter();
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    mode: "onChange",
    defaultValues: {
      email: "",
      note: "",
      vorname: "",
      nachname: "",
      Bestellnummer: "",
      telephone: "",
      politik: false,
      subject: "",
    },
  });
  const TOnSubmit = async (data: FieldValues) => {
    try {
      const response = await sendEmail(data, "CONTACT");
      if (response && response.code === 200) {
        toast({
          duration: 600,
          title: "Erfolg",
          description:
            "Wir haben Ihre E-Mail erhalten, wir werden uns so schnell wie möglich mit Ihnen in Verbindung setzen",
          variant: "success",
        });
        setTimeout(() => {
          router.push("/");
        }, 600);
      }
    } catch (e) {
      toast({
        duration: 600,
        title: "fehlgeschlagen",
        variant: "destructive",
        description: "E-Mail konnte nicht gesendet werden",
      });
    }
  };
  const isValid = form.formState.isValid;
  const { handleSubmit, control } = form;

  return (
    <div className="mb-24 max-sm:ml-2  md:ml-20 lg:ml-36 ">
      <Form {...form}>
        <form onSubmit={handleSubmit(TOnSubmit)}>
          <div className="mb-5 sm:flex sm:w-full md:w-3/4 lg:w-3/4 xl:w-1/2 ">
            <FormField
              control={control}
              name="vorname"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Vorname*"
                      {...field}
                      className="mr-5 mt-5 flex h-12 border  sm:w-72  md:w-64 lg:w-80  "
                    />
                  </FormControl>
                  <FormMessage>
                    {form.formState.errors?.vorname?.message}
                  </FormMessage>
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="nachname"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Nachname*"
                      {...field}
                      className="mt-5 h-12 sm:ml-3 sm:w-80 md:w-64 lg:w-80 "
                    />
                  </FormControl>

                  <FormMessage>
                    {form.formState.errors?.nachname?.message}
                  </FormMessage>
                </FormItem>
              )}
            />
          </div>
          <div className="mb-5 sm:flex  md:w-3/4 lg:w-3/4 xl:w-1/2">
            <FormField
              control={control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="E-mail*"
                      {...field}
                      className="mr-5 flex h-12 border max-sm:mb-5 sm:w-72 md:w-64 lg:w-80 "
                    />
                  </FormControl>
                  <FormMessage>
                    {form.formState.errors?.email?.message}
                  </FormMessage>
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="telephone"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Telefonnummer"
                      {...field}
                      className="  flex h-12 border max-sm:hidden sm:ml-3 sm:w-80  md:w-64 lg:w-80  "
                    />
                  </FormControl>
                  <FormMessage>
                    {form.formState.errors?.telephone?.message}
                  </FormMessage>
                </FormItem>
              )}
            />
          </div>
          <div className="max-sm:flex ">
            <FormField
              control={control}
              name="Bestellnummer"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Bestellnummer"
                      {...field}
                      className=" flex h-12 border max-sm:w-52 md:w-3/4 lg:w-3/4 xl:w-1/2 "
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="telephone"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Telefonnummer"
                      {...field}
                      className=" ml-2  flex h-12 min-w-max border  max-sm:w-52 sm:hidden"
                    />
                  </FormControl>
                  <FormMessage>
                    {form.formState.errors?.telephone?.message}
                  </FormMessage>
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={control}
            name="subject"
            render={({ field }) => (
              <FormItem className="mt-5 ">
                <FormControl>
                  <ContactSelect onChange={field.onChange} />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="note"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    type="text"
                    placeholder="Ihre Nachricht"
                    {...field}
                    className="mt-5  h-48 border md:w-3/4 lg:w-3/4 xl:w-1/2"
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="politik"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div {...field} className="mt-5 flex flex-row">
                    <Checkbox
                      id="policy"
                      className="h-6 w-6 border-gray-400 data-[state=checked]:bg-success"
                      form="text-white"
                      slot="h-5 w-5 bg-success"
                    />
                    <Label
                      htmlFor="policy"
                      className="ml-2 flex flex-wrap text-base font-bold max-md:flex-col"
                    >
                      <div className="flex flex-row">
                        <span>
                          Ich habe die Datenschutzbestimmungen zur Kenntnis
                          genommen.
                        </span>
                      </div>
                    </Label>
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
          <Button
            type="submit"
            disabled={!isValid}
            className="group relative mb-2 mt-5 h-10 bg-primary  hover:bg-primary disabled:bg-gray-500 max-md:mb-16 max-sm:ml-1 max-sm:mr-1 max-sm:h-12 max-sm:w-full sm:w-72"
          >
            <p className="text-base font-light uppercase animate-in group-hover:-translate-x-3">
              ABSENDEN
            </p>
            <ChevronRightIcon className="absolute right-2 hidden w-8 text-white animate-in group-hover:block group-hover:-translate-x-3 max-sm:w-12" />
          </Button>
        </form>
      </Form>
    </div>
  );
};
