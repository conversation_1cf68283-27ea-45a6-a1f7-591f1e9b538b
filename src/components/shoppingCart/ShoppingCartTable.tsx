"use client";

import ScrollableSelect from "@components/ui/ScrollableSelect";
import {
  cn,
  getChangePercentage,
  getPriceFormatter,
  productRange,
} from "@lib/utils";
import { scrollableSelectProps } from "@typesDeclarations/GenericUITypes/InputTypes";
import {
  priceCellProps,
  quantityCellProps,
} from "@typesDeclarations/wooTypes/wooCartTypes";
import { values } from "lodash";
import Image from "next/image";
import Link from "next/link";
import { FunctionComponent } from "react";

import { CartProduct } from "../../state/schema";
import { useUserStore } from "../../state/user";
import RemoveItemButton from "./RemoveItemButton";

// cells
// head
// row
// table

// # ------------------ CELLS ---------------------- //

const ItemCell: FunctionComponent<{ product: CartProduct }> = ({ product }) => {
  const size = values(product.meta.variation[0])[0];
  return (
    <Link href={`products/${product.slug}`} target="_blank">
      <article className="flex min-w-fit max-w-[25rem] flex-col gap-x-4 p-1 transition-all hover:bg-primary sm:flex-row">
        <Image
          priority
          src={product.featured_image}
          alt={`image of ${product.name}`}
          width={60}
          height={60}
          className="aspect-square h-auto max-h-32 w-auto"
        />
        <div className="gap-y-2 whitespace-pre-wrap ">
          <h4 className="font-medium">{product.name}</h4>
          {size && (
            <p>
              {`${size}`} <br />
            </p>
          )}
          <p>Artikel-Nr.: {product.meta.sku}</p>
        </div>
      </article>
    </Link>
  );
};

export const PriceCell: FunctionComponent<priceCellProps> = ({
  regularPrice,
  salePrice,
  className,
}) => {
  const percentage = salePrice
    ? getChangePercentage(regularPrice, salePrice)
    : 0;

  const { deutschFormatter } = getPriceFormatter(true);

  return (
    <div className={cn("font-medium", className)} tabIndex={0}>
      {!salePrice ? (
        deutschFormatter(regularPrice)
      ) : (
        <div>
          <p className="text-destructive">{deutschFormatter(salePrice)}</p>
          <p className="text-xs">
            <span className=" text-sm font-normal text-gray-middle line-through">
              {deutschFormatter(regularPrice)}
            </span>
            <br />
            <span className="font-normal text-gray-middle ">
              Du sparst {`${percentage}%`}
            </span>
          </p>
        </div>
      )}
    </div>
  );
};

export const QuantityCell: FunctionComponent<quantityCellProps> = ({
  className,
  stockQuantity,
  quantity,
  productKey,
}) => {
  const options: scrollableSelectProps["options"] = productRange(
    1,
    stockQuantity
  ).map((option) => {
    return { label: `${option}`, value: `${option}` };
  });

  const loading = useUserStore((s) => s.loading);
  const updateCartItemQuantity = useUserStore((s) => s.updateCartStock);

  const updateQuantity = (newQuantity: string) => {
    updateCartItemQuantity(productKey, Number(newQuantity));
  };

  return (
    <ScrollableSelect
      className={className}
      loading={loading}
      options={options}
      value={`${quantity}`}
      onValueChange={updateQuantity}
    />
  );
};

const TotalSumCell: FunctionComponent<{
  manualTotalPrice: number | string;
}> = ({ manualTotalPrice }) => {
  const { deutschFormatter } = getPriceFormatter(true, false);
  return (
    <p tabIndex={0} className="font-bold">
      {deutschFormatter(manualTotalPrice)}
    </p>
  );
};

// # ------------------ HEAD ---------------------- //

const ShoppingCartTableHead: FunctionComponent = () => {
  return (
    <tr className="text-sm uppercase">
      <th
        scope="col"
        className="py-3.5 pl-6 pr-3 text-left font-normal text-card-foreground"
      >
        ARTIKEL
      </th>
      <th
        scope="col"
        className="py-3.5 pl-6 pr-3 text-right font-normal text-card-foreground"
      >
        Stückpreis
      </th>
      <th
        scope="col"
        className="py-3.5 pl-6 pr-3 text-left font-normal text-card-foreground"
      >
        MENGE
      </th>
      <th
        scope="col"
        className="py-3.5 pl-6 pr-3 text-left font-normal text-card-foreground"
      >
        Summe
      </th>
      {/* Empty th tag to accommodate for te 'delete button' positioning */}
      <th />
    </tr>
  );
};

// # ------------------ ROW ---------------------- //

const ShoppingCartRow: FunctionComponent<{ product: CartProduct }> = ({
  product,
}) => {
  // TODO fetch salePrice from backend
  return (
    <>
      <td className="whitespace-nowrap  py-4">
        <ItemCell product={product} />
      </td>
      <td className="whitespace-nowrap px-6 py-4 text-right">
        <PriceCell
          regularPrice={product.price_regular}
          salePrice={
            product.price_sale === "0" ? undefined : product.price_sale
          }
        />
      </td>
      <td className="whitespace-nowrap px-6 py-4">
        <QuantityCell
          productKey={product.item_key}
          quantity={product.quantity.value}
          stockQuantity={product.quantity.max_purchase}
        />
      </td>
      <td className="whitespace-nowrap px-6 py-4">
        <TotalSumCell
          manualTotalPrice={product.totals.total + product.totals.tax}
        />
      </td>
      <td>
        <RemoveItemButton productKey={product.item_key} />
      </td>
    </>
  );
};

// # ------------------ TABLE ---------------------- //

const ShoppingCartTable: FunctionComponent = () => {
  const cart = useUserStore((s) => s.cart);

  return (
    <div className="hidden w-full flex-grow flex-col md:flex lg:max-w-full">
      <table className="w-full text-left text-sm">
        <thead className=" select-none border-b  ">
          <ShoppingCartTableHead />
        </thead>
        <tbody>
          {cart?.items.map((row) => {
            return (
              <tr key={row.id} className="border-b">
                <ShoppingCartRow product={row} />
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export { ShoppingCartTable };
