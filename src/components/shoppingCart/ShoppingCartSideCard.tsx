import Spinner from "@components/general/Spinner";
import { Button } from "@components/ui/Button";
import { Icon } from "@components/ui/Icon";
import { Input } from "@components/ui/input";
import { Separator } from "@components/ui/separator";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { cn, getPriceFormatter } from "@lib/utils";
import { FunctionComponent, useState } from "react";

import { useUserStore } from "../../state/user";
import CheckOutButton from "./checkoutButton";
const { deutschFormatter: formatPrice } = getPriceFormatter();
const ShoppingCartDescriptionCard: FunctionComponent = () => {
  const cart = useUserStore((s) => s.cart);

  const discountTotal = cart?.totals.discount_total,
    subTotal = cart?.totals.subtotal,
    totalTax = cart?.totals.total_tax,
    total = cart?.totals.total;

  const [coupon, setCoupon] = useState("");

  const applyCoupon = useUserStore((s) => s.applyCoupon);
  const loading = useUserStore((s) => s.couponLoading);

  return (
    <section
      tabIndex={0}
      className="sticky top-72 flex h-fit w-auto flex-col justify-between max-md:w-full md:p-3 "
    >
      <dl
        tabIndex={0}
        className="flex w-52 flex-col gap-y-7 bg-gray-bright p-4 max-md:w-full max-md:bg-white sm:min-w-[20rem] "
      >
        <div className="flex flex-row justify-between ">
          <dt className="text-sm font-light" tabIndex={0}>
            Zwischensumme
          </dt>
          <dd className="font-medium" tabIndex={0}>
            {formatPrice(subTotal ?? "")}
          </dd>
        </div>

        <div className="flex flex-row justify-between ">
          <dt className="text-sm font-light" tabIndex={0}>
            Steuer
          </dt>
          <dd className="font-medium" tabIndex={0}>
            {formatPrice(totalTax ?? "")}
          </dd>
        </div>

        <div className="flex flex-row justify-between">
          <dt className="text-sm font-light" tabIndex={0}>
            Rabatt
          </dt>
          <dd tabIndex={0}> -{formatPrice(discountTotal ?? "0")}</dd>
        </div>

        <Separator className=" bg-gray-middle" />

        <div className="flex flex-row justify-between">
          <dt className="text-sm font-light" tabIndex={0}>
            Gesamtsumme
          </dt>
          <dd tabIndex={0}> {formatPrice(total ?? "")}</dd>
        </div>

        <Separator className=" bg-gray-middle" />
        <span className="mx-auto text-sm font-light text-gray-middle ">
          inkl. gesetzlich geltender MwSt.
        </span>
      </dl>
      <div className=" py-2  ">
        <div className="relative">
          <Input
            disabled={loading}
            value={coupon}
            onChange={(e) => setCoupon(e.target.value)}
            className="h-12"
            placeholder="Gutscheincode eingeben"
          />
          <Button
            variant={"icon"}
            size={"icon"}
            className="absolute right-0 top-0 h-full bg-gray-light"
            onClick={async () => {
              await applyCoupon(coupon);
            }}
          >
            <Icon
              size={"medium"}
              className={cn("aspect-square p-2", coupon ? "" : "text-white")}
              intent={coupon ? "default" : "disabled"}
            >
              {loading ? <Spinner /> : <ArrowRightIcon />}
            </Icon>
          </Button>
        </div>
        <CheckOutButton rendersIcon={true} />
      </div>
    </section>
  );
};

export default ShoppingCartDescriptionCard;
