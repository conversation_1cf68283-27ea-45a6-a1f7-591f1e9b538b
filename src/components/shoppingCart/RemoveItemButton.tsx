"use client";
import Spinner from "@components/general/Spinner";
import { Icon } from "@components/ui/Icon";
import { TrashIcon } from "@heroicons/react/24/outline";
import { cn } from "@lib/utils";
import { removeItemButtonProps } from "@typesDeclarations/wooTypes/wooCartTypes";
import { FunctionComponent } from "react";

import { useUserStore } from "../../state/user";

const RemoveItemButton: FunctionComponent<removeItemButtonProps> = ({
  productKey,
  className,
}) => {
  const removeItem = useUserStore((s) => s.removeFromCart);
  const loading = useUserStore((s) => s.loading);

  const deleteItem = () => {
    removeItem(productKey);
  };

  return (
    <Icon
      tabIndex={0}
      intent={"default"}
      onClick={deleteItem}
      size={"small"}
      className={cn(" hover:text-destructive", className)}
    >
      {loading ? <Spinner /> : <TrashIcon />}
    </Icon>
  );
};

export default RemoveItemButton;
