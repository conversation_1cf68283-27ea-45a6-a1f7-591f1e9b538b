"use client";
import Spinner from "@components/general/Spinner";
import { Button } from "@components/ui/Button";
import { Icon } from "@components/ui/Icon";
import { useToast } from "@components/ui/use-toast";
import { STRIPE } from "@constants/sessionConstants";
import { ChevronRightIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";
import { FunctionComponent } from "react";

import { useUserStore } from "../../state/user";

interface CheckOutButtonProps {
  /** show chevron right Icon `>` when true (used on cart page) */
  rendersIcon?: boolean;
}
const CheckOutButton: FunctionComponent<CheckOutButtonProps> = ({
  rendersIcon = false,
}) => {
  // const stripe = useStripe();
  const router = useRouter();

  const cart = useUserStore((s) => s.cart);
  const checkingOut = useUserStore((s) => s.checkingOut);
  const setCheckingOut = useUserStore((s) => s.setCheckingOut);

  const { toast } = useToast();
  // const { emptyCart } = useEmptyCart();
  const isDisabled = !((cart?.item_count ?? 0) > 0) || checkingOut;

  /**
   * @description this function initiates the checkout process as follows:
   *  * create order (consumes cart items and generates an order ID)
   *  * create checkout session (consumes cart items & order ID to generate a stripe Session)
   */
  const performCheckout = async () => {
    try {
      setCheckingOut(true);

      // ----------------------- BEGIN CREATE PAYMENT_INTENT ----------------------- //

      const paymentIntentResponse = await fetch("/create-payment-intent", {
        method: "POST",
        mode: "cors",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          total: cart?.totals.total,
        }),
      });

      const paymentIntentData = await paymentIntentResponse.json();

      const { paymentIntent } = paymentIntentData;

      sessionStorage.setItem(
        STRIPE.paymentIntent,
        JSON.stringify(paymentIntent)
      );
      // ----------------------- END CREATE PAYMENT_INTENT ----------------------- //

      router.push(`/checkout`);
    } catch (error) {
      toast({
        variant: "destructive",
        description: "Die Checkout-Sitzung konnte nicht erstellt werden",
      });
    } finally {
      setCheckingOut(false);
    }
  };

  /**
  there's a design difference when rendering the checkout Button on on the modal component and on the cart page
   *  */
  const conditionalStyles = rendersIcon
    ? "group/checkoutButton my-2 flex h-14 w-full text-xs uppercase hover:bg-primary"
    : "h-12 w-full";

  return (
    <Button
      className={conditionalStyles}
      disabled={isDisabled}
      onClick={performCheckout}
    >
      {checkingOut ? (
        <div className="flex items-center gap-x-2">
          <Spinner />
          <span>Einen Augenblick</span>
        </div>
      ) : (
        <>
          zur kasse
          {rendersIcon && (
            <Icon className=" absolute right-6 aspect-square w-8 translate-x-4 text-white opacity-0 transition-all group-hover/checkoutButton:translate-x-0 group-hover/checkoutButton:opacity-100">
              <ChevronRightIcon />
            </Icon>
          )}
        </>
      )}
    </Button>
  );
};

export default CheckOutButton;
