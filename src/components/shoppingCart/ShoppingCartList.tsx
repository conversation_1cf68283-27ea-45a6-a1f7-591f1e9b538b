import { values } from "lodash";
import Image from "next/image";
import Link from "next/link";
import { FunctionComponent } from "react";

import { CartProduct } from "../../state/schema";
import { useUserStore } from "../../state/user";
import RemoveItemButton from "./RemoveItemButton";
import { PriceCell, QuantityCell } from "./ShoppingCartTable";

/**
 *  @description single product in cart modal / cart page for small screens
 */
export const ShoppingCartProductCard: FunctionComponent<CartProduct> = ({
  item_key,
  featured_image,
  name,
  slug,
  meta,
  quantity,
  price_regular,
  price_sale,
}) => {
  return (
    <article className="h-50 relative flex w-full max-w-full  overflow-hidden border-b-2 border-gray-light bg-white p-2 ">
      <div className=" flex w-[30%]  min-w-[30%] max-w-[30%] items-center justify-center  ">
        <Link href={`/products/${slug}`}>
          <Image
            priority
            src={featured_image}
            alt={`image of ${name}`}
            width={100}
            height={120}
            sizes="100px"
            className=" hover:brightness-95"
          />
        </Link>
      </div>

      <div className=" flex h-full flex-grow flex-col justify-between   px-4 ">
        <div className="  flex max-w-[8rem] flex-col gap-y-2 overflow-hidden  text-ellipsis whitespace-pre-wrap py-2">
          <h4 className=" font-medium">{name}</h4>
          {/*}
          <span>{`Kategorie: ${category}`}</span>
          {*/}
          <span>
            {values(meta.variation)[0] && `Größe: ${values(meta.variation)[0]}`}
          </span>
        </div>
        <QuantityCell
          className="min-h-fit w-20"
          productKey={item_key}
          quantity={quantity.value}
          stockQuantity={quantity.max_purchase}
        />
      </div>
      <div className="flex w-[25%] flex-col items-end  justify-between">
        <RemoveItemButton className="m-0" productKey={item_key} />
        <PriceCell
          regularPrice={price_regular}
          salePrice={price_sale === "0" ? undefined : price_sale}
        />
      </div>
    </article>
  );
};

export const ShoppingCartList: FunctionComponent<{
  /** @description forces the list to be rendered regardless of current screen size */
  forceShow?: boolean;
}> = ({ forceShow = false }) => {
  const cart = useUserStore((data) => data.cart);
  const items = cart?.items ?? [];

  return (
    <ul
      className={`${forceShow ? "flex flex-col" : "md:hidden "} w-full gap-y-2`}
    >
      {items.map((data) => (
        <li key={data.item_key}>
          <ShoppingCartProductCard {...data} />
        </li>
      ))}
    </ul>
  );
};
