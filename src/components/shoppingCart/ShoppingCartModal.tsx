"use client";
import Spinner from "@components/general/Spinner";
import { Badge } from "@components/ui/badge";
import { Button } from "@components/ui/Button";
import { Icon } from "@components/ui/Icon";
import { Separator } from "@components/ui/separator";
import {
  Sheet,
  <PERSON>et<PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@components/ui/sheet";
import { Skeleton } from "@components/ui/skeleton";
import { ChevronRightIcon, ShoppingBagIcon } from "@heroicons/react/24/outline";
import { getPriceFormatter } from "@lib/utils";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@radix-ui/react-tooltip";
import Link from "next/link";
import { FunctionComponent, useEffect } from "react";

import { useUserStore } from "../../state/user";
import CheckOutButton from "./checkoutButton";
import { ShoppingCartList } from "./ShoppingCartList";
const ShoppingCartModal: FunctionComponent = () => {
  const cart = useUserStore((data) => data.cart);
  const loading = useUserStore((data) => data.loading);

  const { deutschFormatter: formatPrice } = getPriceFormatter();
  // Pricing-related values
  const discountTotal = formatPrice(cart?.totals.discount_total ?? "0"),
    subTotal = formatPrice(cart?.totals.subtotal ?? "0"),
    totalTax = formatPrice(cart?.totals.total_tax ?? "0"),
    total = formatPrice(cart?.totals.total ?? "0");

  const itemsCount = cart?.item_count;

  const synchronize = useUserStore((data) => data.synchronize);

  useEffect(() => {
    const init = async () => {
      await synchronize();
    };
    init();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Sheet>
      <SheetTrigger asChild>
        <div className="group/ShoppingCart flex cursor-pointer flex-row items-center gap-x-1 ">
          {loading ? (
            <Spinner />
          ) : (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <div className="relative">
                    <Button
                      asChild
                      variant={"ghost"}
                      size={"icon"}
                      className="bg-gray-light p-2 text-white group-hover/ShoppingCart:bg-accent-foreground group-hover/ShoppingCart:text-white"
                    >
                      <ShoppingBagIcon />
                    </Button>
                    <Badge
                      className={`hover:bg-success ${
                        itemsCount ? "inline-flex" : "hidden"
                      } absolute  -right-2 -top-1  select-none items-center rounded-full  bg-success px-2 py-1 font-mono  text-xs text-success-foreground `}
                    >
                      {itemsCount}
                    </Badge>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Warenkorb ({itemsCount})</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          {loading ? (
            <Skeleton className="ml-2 h-4 w-20 " />
          ) : (
            <h3
              tabIndex={0}
              className="  hidden px-2 font-mono text-sm font-light group-hover/ShoppingCart:text-accent-foreground group-hover/ShoppingCart:underline md:block"
            >
              {total}
            </h3>
          )}
        </div>
      </SheetTrigger>
      <SheetContent
        className="flex w-full max-w-full flex-col  bg-white"
        side={"right"}
      >
        <SheetHeader className="gap-x-2 bg-gray-light pl-2">
          <div className="flex w-full items-center justify-between pl-2">
            <p className="capitalize ">Warenkorb</p>
            <SheetClose asChild>
              <Button
                variant={"outline"}
                size={"icon"}
                className="h-14 w-14 min-w-fit gap-x-2 border-0 bg-transparent p-2 font-light capitalize text-black"
              >
                Weitershoppen
                <Icon className="aspect-square w-4">
                  <ChevronRightIcon />
                </Icon>
              </Button>
            </SheetClose>
          </div>
        </SheetHeader>
        {/* -------------------- BEGIN SHEET BODY -------------------- */}
        <div className="mb-auto mt-0  max-h-full w-full   flex-grow  gap-y-1 overflow-y-auto">
          <ShoppingCartList forceShow />
        </div>
        {/* --------------------- END SHEET BODY --------------------- */}
        <SheetFooter className="flex !flex-col  ">
          <Separator className="bg-gray-middle" />
          <div className="!m-0 [&>div]:px-4 [&>div]:py-2">
            <div className="flex min-w-full justify-between  ">
              <span className="text-sm  font-light capitalize">
                Zwischensumme
              </span>
              <span className="font-bold">{subTotal}</span>
            </div>

            <div className="flex min-w-full justify-between  ">
              <span className="text-sm  font-light capitalize">Steuer</span>
              <span className="font-bold">{totalTax}</span>
            </div>

            <div className="flex min-w-full justify-between  ">
              <span className="text-sm  font-light capitalize">Rabatt</span>
              <span className="font-bold"> - {discountTotal}</span>
            </div>

            <div className="flex w-full justify-between bg-gray-light ">
              <span className="font-light capitalize">Gesamtsumme</span>
              <span className="font-bold">{total}</span>
            </div>
            <div className="flex flex-col p-4  [&>*]:my-1">
              <SheetClose asChild>
                <div>
                  <CheckOutButton />
                </div>
              </SheetClose>
              <SheetClose asChild>
                <Link href="/cart">
                  <Button
                    className="h-12 w-full border-black"
                    variant={"outline"}
                  >
                    zum warenkorb
                  </Button>
                </Link>
              </SheetClose>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default ShoppingCartModal;
