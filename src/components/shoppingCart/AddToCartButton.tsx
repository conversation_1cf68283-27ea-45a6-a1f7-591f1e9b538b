import Spinner from "@components/general/Spinner";
import { Button } from "@components/ui/Button";
import { ChevronRightIcon } from "@heroicons/react/24/outline";
import { cn } from "@lib/utils";
import { shoppingCartRowProps } from "@typesDeclarations/wooTypes/wooCartTypes";

import { useUserStore } from "../../state/user";

const AddToCartButton = ({
  databaseId,
  quantity = 1,
  stockQuantity,
  stockStatus,
  variation,
}: {
  databaseId: Pick<shoppingCartRowProps, "databaseId">;
  quantity?: number;
  stockQuantity: number;
  stockStatus: string;
  variation: Record<string, string | undefined> | null;
}) => {
  const addToCart = useUserStore((data) => data.addToCart);
  const loading = useUserStore((data) => data.loading);

  const addItemToCart = () => {
    if (databaseId)
      addToCart(
        /* @ts-ignore */
        parseInt(databaseId),
        quantity,
        variation
      );
  };

  return (
    <div className="w-full">
      <Button
        disabled={
          (stockQuantity === null && stockStatus === "OUT_OF_STOCK") || loading
        }
        onClick={addItemToCart}
        className={cn("group relative h-16 w-full bg-primary hover:bg-primary")}
      >
        {loading ? (
          <Spinner />
        ) : (
          <span className="flex translate-x-2 transform items-center justify-center text-center duration-300 ease-in-out group-hover:translate-x-0">
            In den Warenkorb
          </span>
        )}
        <ChevronRightIcon className="absolute right-1 h-12 w-12 opacity-0 ease-in-out group-hover:opacity-100" />
      </Button>
    </div>
  );
};

export default AddToCartButton;
