"use client";
import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/outline";
import { useState } from "react";

import { Button } from "./Button";
import FaqAccordion from "./FaqAccordion";

const FaqMobileAccordion = ({
  faqData,
  handleFaq,
  selectedQuestion,
  selectedTitle,
}: {
  faqData: any;
  handleFaq: any;
  selectedQuestion: string;
  selectedTitle: string;
}) => {
  const [isClicked, setIsClicked] = useState(true);
  const onCLickedOpenFaqFilter = () => setIsClicked(false);
  const onCLickedCloseFaqFilter = () => setIsClicked(true);
  return (
    <div className="sm:hidden">
      {isClicked ? (
        <div onClick={onCLickedOpenFaqFilter}>
          <Button className="relative ml-6 w-full bg-black text-white max-sm:w-11/12 sm:hidden">
            Hilfecenter Themen
            <ChevronDownIcon className="absolute right-[5%] w-5" />
          </Button>
        </div>
      ) : (
        <div>
          <div onClick={onCLickedCloseFaqFilter}>
            <Button className="relative ml-6 w-full bg-black text-white max-sm:w-11/12 sm:hidden">
              Hilfecenter Themen
              <ChevronUpIcon className="absolute right-[5%] w-5" />
            </Button>
          </div>
          <div className="ml-6 ">
            <FaqAccordion
              faqData={faqData}
              handleFaq={handleFaq}
              selectedTitle={selectedTitle}
              selectedQuestion={selectedQuestion}
            />
          </div>
        </div>
      )}
    </div>
  );
};
export default FaqMobileAccordion;
