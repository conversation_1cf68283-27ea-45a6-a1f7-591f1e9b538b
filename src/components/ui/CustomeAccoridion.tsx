import { useState } from "react";

const CustomeAccoridion = ({
  title,
  content,
}: {
  title: any;
  content: any;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="rounded-lg border-b-2">
      <button
        className="my-4 flex w-full flex-1 items-center justify-between pl-4 font-medium transition-all hover:underline "
        onClick={() => setIsOpen((prevState) => !prevState)}
      >
        <span>{title}</span>
        <svg
          className={`h-6 w-6 ${isOpen ? "rotate-45 transform" : ""}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d={isOpen ? "M19 9,l-7 7,l-7" : "M12 6v6m0 0v6m0-6h6m-6 0H6"}
          />
        </svg>
      </button>
      <div className={`transition-all ${isOpen ? "block" : "hidden"}`}>
        <div className="pl-2">{content}</div>
      </div>
    </div>
  );
};

export default CustomeAccoridion;
