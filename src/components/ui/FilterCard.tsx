"use client";
import {
  AdjustmentsHorizontalIcon,
  ChevronDownIcon,
  ChevronUpIcon,
} from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import { Breadcrumb } from "react-instantsearch";

import AccordionCard from "./AccordionCard";
import { Button } from "./Button";

const FilterCard = () => {
  const [isClicked, setIsClicked] = useState(true);
  const onCLickedOpenFilter = () => setIsClicked(false);
  const onCLickedCloseFilter = () => setIsClicked(true);
  useEffect(() => {
    const handleScreenSizeChange = () => {
      if (window.innerWidth >= 768) {
        setIsClicked(true);
      }
    };
    window.addEventListener("resize", handleScreenSizeChange);
    return () => {
      window.removeEventListener("resize", handleScreenSizeChange);
    };
  }, []);

  return (
    <div className="mt-4 w-full md:mt-10">
      <div className="flex flex-col border-t border-gray-200 sm:border-0">
        <div className="mb-2 md:ml-4 lg:absolute lg:mt-2">
          <Breadcrumb
            attributes={["category.lvl0", "category.lvl1", "category.lvl2"]}
            translations={{
              rootElementText: "Produkte",
            }}
            classNames={{
              root: "",
              list: "",
              item: "",
              link: "text-sm text-gray-600 cursor-pointer",
              separator: "",
            }}
            separator="/"
          />
        </div>
      </div>
      <div className="hidden lg:block">
        <AccordionCard />
      </div>
      {isClicked ? (
        <div onClick={onCLickedOpenFilter} className="w-full">
          <Button className="relative w-full bg-black text-white lg:hidden">
            Filter
            <AdjustmentsHorizontalIcon className="absolute left-4 w-5" />
            <ChevronDownIcon className="absolute right-4 w-5" />
          </Button>
        </div>
      ) : (
        <div className="w-full">
          <div onClick={onCLickedCloseFilter} className="md:mx-auto lg:hidden">
            <Button className="relative w-full bg-black text-white lg:hidden ">
              Filter
              <AdjustmentsHorizontalIcon className="absolute left-4 w-5" />
              <ChevronUpIcon className="absolute right-4 w-5" />
            </Button>
          </div>
          <div className="lg:hidden">
            <AccordionCard />
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterCard;
