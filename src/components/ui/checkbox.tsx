"use client";

import { cn } from "@lib/utils";
import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { Check } from "lucide-react";
import * as React from "react";

const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>
>(({ className, ...props }, ref) => (
  <CheckboxPrimitive.Root
    ref={ref}
    className={cn(
      "data-[state=checked]:border-white-foreground peer h-4 w-4 shrink-0 rounded-sm border border-accent ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-white ",
      className
    )}
    {...props}
  >
    {/* props.form is used to pass styles to this component */}
    <CheckboxPrimitive.Indicator
      className={cn(
        "flex items-center justify-center text-current",
        props.form
      )}
    >
      {/* props.slot is used to pass styles to this component */}
      <Check className={cn("h-3 w-3 bg-accent", props.slot)} />
    </CheckboxPrimitive.Indicator>
  </CheckboxPrimitive.Root>
));
Checkbox.displayName = CheckboxPrimitive.Root.displayName;

export { Checkbox };
