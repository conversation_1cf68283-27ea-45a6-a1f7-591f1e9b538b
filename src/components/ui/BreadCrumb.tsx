import Link from "next/link";
import React from "react";
interface breadcrumbsProps {
  breadcrumbs: { url: string; name: string }[];
}

const BreadCrumb: React.FC<breadcrumbsProps> = ({ breadcrumbs }) => {
  return (
    <section className=" mx-auto py-5 lg:ml-0  ">
      <div className="container px-4  ">
        <ol className=" inline-flex flex-wrap items-center text-gray-600  ">
          {breadcrumbs?.map((breadcrumb, index) => (
            <Link
              href={breadcrumb.url}
              key={index}
              className=" text-sm 
           text-black hover:text-orange-600"
            >
              {breadcrumb.name}
            </Link>
          ))}
        </ol>
      </div>
    </section>
  );
};

export default BreadCrumb;
