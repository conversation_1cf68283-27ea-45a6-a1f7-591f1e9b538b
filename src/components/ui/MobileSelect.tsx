import { Select } from "@components/ui/select";
import React from "react";
import { SortBy } from "react-instantsearch";

const MobileSelect = () => {
  return (
    <div className="mt-4 w-full lg:hidden">
      <Select>
        <SortBy
          items={[
            { label: "Hervorgehoben", value: "products" },
            { label: "Hochster preis", value: "products_price_asc" },
            { label: "Niedrigster Preis", value: "products_price_desc" },
          ]}
        />
      </Select>
    </div>
  );
};

export default MobileSelect;
