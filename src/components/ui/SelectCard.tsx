import { Select } from "@components/ui/select";
import React from "react";
import { SortBy } from "react-instantsearch";
const SelectCard = () => {
  return (
    <div className="flex justify-end">
      <Select>
        <SortBy
          items={[
            { label: "<PERSON><PERSON><PERSON><PERSON><PERSON>", value: "products" },
            {
              label: "Hochster preis",
              value: "products/sort/price:desc",
            },
            {
              label: "Niedrigster Preis",
              value: "products/sort/price:asc",
            },
          ]}
          classNames={{
            select:
              "w-full m-0 pl-4 pr-8 py-2 rounded-md border-0 bg-transparent cursor-pointer text-sm font-medium text-gray-500 hover:text-gray-800 focus:outline-none focus:ring-2 focus:ring-indigo-500",
          }}
        />
      </Select>
    </div>
  );
};
export default SelectCard;
