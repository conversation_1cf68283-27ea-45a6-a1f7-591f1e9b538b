import Spinner from "@components/general/Spinner";
import { Label } from "@components/ui/label";
import { RadioGroup, RadioGroupItem } from "@components/ui/radio-group";
import { STRIPE } from "@constants/sessionConstants";
import {
  convertPriceToFrontEnd,
  SS_setShippingMethod,
} from "@lib/utilities/StripeHelpers/checkoutUtils";
import { getPriceFormatter } from "@lib/utils";
import { useEffect, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";

import {
  FetchedShippingMethod,
  useGetShippingMethodsByCountry,
} from "../../hooks/graphql/queries/cartHooks";
import { CoCartV1, useUserStore } from "../../state/user";

const { deutschFormatter: formatPrice } = getPriceFormatter(true, false);

export function ShippingRadioGroupDemo() {
  const { watch, control } = useFormContext();
  const shippingCountry = watch("shippingCountry"),
    billingCountry = watch("billingCountry"),
    isShippingSameAsBilling = watch("shippingSameAsBilling");

  // THIS IS A TEMPORARY FIX
  // TODO: Get Available Shipping methods from Cart (Add it to Cart Query)
  const cart = useUserStore((s) => s.cart);
  const total =
    cart?.totals.total && cart?.totals.total_tax
      ? parseInt(cart?.totals.total)
      : 0;
  const [loading, setLoading] = useState(true);

  const activeShippingCountry = isShippingSameAsBilling
    ? billingCountry
    : shippingCountry;
  const [shippingMethods, setShippingMethods] = useState<
    FetchedShippingMethod[] | undefined
  >(undefined);

  const { getShippingMethods } = useGetShippingMethodsByCountry();

  useEffect(() => {
    setShippingMethods([]);
    const fetchOptions = async () => {
      const gqlRes = await getShippingMethods({
        variables: { country: activeShippingCountry },
      });
      const smGQL = gqlRes?.data?.availableShippingMethods;

      const response = (await CoCartV1.post(
        `calculate/shipping?cart_key=${cart?.cart_key}`,
        {
          country: activeShippingCountry,
          return_methods: true,
        }
      )) as {
        data: {
          [key: string]: {
            key: string;
            instance_id: string;
            method_id: string;
            label: string;
            cost: number;
            taxes: string[];
          };
        };
      };
      try {
        const sm = Object.values(response.data).map((data) => ({
          id: data.key,
          instanceId: data.instance_id,
          methodId: data.method_id,
          name: data.label,
          price: data.cost,
          taxes: data.taxes,
        }));
        const smToUse = smGQL && smGQL.length > sm.length ? smGQL : sm;
        console.log("SHIPPING METHODS", sm, smGQL, activeShippingCountry);
        if (total < 10000) {
          setShippingMethods(
            smToUse.filter((s) => s.methodId !== "free_shipping")
          );
        } else {
          setShippingMethods(smToUse);
        }
      } catch (e) {
        console.error(e);
        setShippingMethods([]);
      }

      setLoading(false);
    };

    fetchOptions();
  }, [activeShippingCountry, cart?.cart_key, total]);

  return !!shippingMethods && loading ? (
    <Spinner />
  ) : shippingMethods && shippingMethods?.length > 0 ? (
    <Controller
      name="shippingMethod"
      control={control}
      defaultValue={
        shippingMethods.find((s) => s.methodId === "free_shipping")
          ? "free_shipping"
          : "flat_rate"
      }
      render={({ field: { name, onChange, ref, value } }) => {
        const defaultShippingMethodDetails = shippingMethods?.find(
          (method) => method.methodId === (value ?? "none")
        );
        sessionStorage.setItem(
          STRIPE.PAYMENT_METHOD_PRICE,
          JSON.stringify(defaultShippingMethodDetails)
        );
        return (
          <RadioGroup
            name={name}
            value={value}
            onValueChange={async (value) => {
              onChange(value);
              const shippingMethodDetails = shippingMethods?.find(
                (method) => method.methodId === value
              ) as FetchedShippingMethod;
              SS_setShippingMethod(shippingMethodDetails);
            }}
            ref={ref}
          >
            {shippingMethods &&
              shippingMethods.map((method: any) => {
                return (
                  <div key={method.id} className="flex items-center space-x-2">
                    <RadioGroupItem
                      value={method.methodId}
                      id={method.methodId}
                    />
                    <Label htmlFor={method.methodId}>
                      {method.name} -{" "}
                      {formatPrice(
                        `${convertPriceToFrontEnd({
                          price: method.price,
                          taxes: method.taxes,
                        })}`
                      )}
                    </Label>
                  </div>
                );
              })}
          </RadioGroup>
        );
      }}
    />
  ) : loading ? (
    <Spinner />
  ) : (
    "Keine Versandmethoden verfügbar"
  );
}
