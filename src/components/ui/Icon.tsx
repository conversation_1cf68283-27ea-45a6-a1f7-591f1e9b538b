import { cn } from "@lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";
// ! THIS IS NOT A SHADCN COMPONENT
const iconVariants = cva(
  "m-auto flex items-center select-none cursor-pointer  transition-all    p-0 ",
  {
    variants: {
      intent: {
        default: "text-black",
        primary: "text-primary",
        secondary: " text-secondary ",
        destructive: "  text-destructive",
        success: " text-success ",
        disabled: " text-gray-light light  ",
      },
      size: {
        small: "h-6 w-6",
        normal: "h-8 w-8",
        medium: "h-12 w-12 ",
        large: "h-16 w-16",
        normalToSmall: "lg:h-4 lg:w-4 h-8 w-8",
      },
    },
    compoundVariants: [
      {
        intent: "disabled",
        size: ["small", "normal", "medium", "large"],
        className: " cursor-not-allowed",
      },
    ],
    defaultVariants: {
      intent: "default",
      size: "normal",
    },
  }
);

export interface iconProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof iconVariants> {}

function Icon({ className, intent, size, ...props }: iconProps) {
  return (
    <div className={cn(iconVariants({ intent, size }), className)} {...props} />
  );
}

export { Icon, iconVariants };
