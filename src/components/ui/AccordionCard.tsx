"use client";
import "../../app/algolia.css";

import ClothsizeRefinement from "@components/refinements/clothsize";
import DecksizeRefinement from "@components/refinements/decksize";
import PantSizeRefinement from "@components/refinements/pantsize";
import ShoesizeRefinement from "@components/refinements/shoesize";
import {
  CurrentRefinements,
  HierarchicalMenu,
  Index,
  RangeInput,
  RefinementList,
} from "react-instantsearch";

// import { Checkbox } from "../../components/ui/checkbox";
import CustomeAccoridion from "./CustomeAccoridion";
const AccordionCard = () => {
  return (
    <div className="w-full ">
      <div className="mt-6">
        <CurrentRefinements
          className="pt-4"
          excludedAttributes={["category.lvl0"]}
          transformItems={(items: any[]) => {
            const separatedItems: any[] = [];
            items.forEach((item) => {
              if (Array.isArray(item.refinements)) {
                item.refinements.forEach((refinement: any) => {
                  const key = refinement.id;
                  separatedItems.push({
                    ...item,
                    refinements: [refinement],
                    key,
                  });
                });
              } else {
                separatedItems.push({
                  ...item,
                  key: `${item.objectID}_${Index}`,
                });
              }
            });

            return separatedItems;
          }}
          classNames={{
            label: "hidden",
          }}
        />
      </div>
      <CustomeAccoridion
        title={"KATEGORIE"}
        content={
          <HierarchicalMenu
            attributes={["category.lvl0", "category.lvl1", "category.lvl2"]}
            limit={50}
            classNames={{
              root: "-ml-4",
              list: "ml-4 block space-y-4 lg:space-y-3",
              item: "space-y-4 lg:space-y-3",
              link: "block text-sm text-gray-600 cursor-pointer",
              count:
                "ml-1.5 rounded bg-gray-200 py-0.5 px-1.5 text-xs font-semibold tabular-nums text-gray-700",
            }}
          />
        }
      />
      <CustomeAccoridion
        title={"HERSTELLER"}
        content={
          <RefinementList
            attribute="brands.name"
            limit={13}
            showMore
            showMoreLimit={20}
            searchable
            searchablePlaceholder="Finde deinen Marken"
          />
        }
      />
      <CustomeAccoridion
        title={"PREIS"}
        content={
          <RangeInput
            attribute="price"
            translations={{
              separatorElementText: "-",
              submitButtonText: "Los",
            }}
            className="mb-4 [&_button]:rounded-none [&_button]:border-foreground [&_input]:rounded-none [&_input]:border-foreground [&_input]:shadow-none"
          />
        }
      />
      <DecksizeRefinement />
      <ShoesizeRefinement />
      <PantSizeRefinement />
      <ClothsizeRefinement />
    </div>
  );
};
export default AccordionCard;
