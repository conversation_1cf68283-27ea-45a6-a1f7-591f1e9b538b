import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@components/ui/accordion";

import { Button } from "./Button";

const FaqAccordion = ({
  faqData,
  handleFaq,
  selectedQuestion,
  selectedTitle,
}: {
  faqData: any;
  handleFaq: any;
  selectedQuestion: string;
  selectedTitle: string;
}) => {
  return (
    <div className="mt-8 w-full">
      <Accordion type="multiple">
        {Array.from({ length: faqData?.faq_items ?? 0 }, (_, item) => (
          <AccordionItem value={faqData[`faq_items_${item}_title`]} key={item}>
            <AccordionTrigger
              className={` ${
                selectedTitle === faqData[`faq_items_${item}_title`]
                  ? "text-destructive"
                  : "hover:text-destructive"
              }  `}
            >
              {faqData[`faq_items_${item}_title`]}
            </AccordionTrigger>
            {Array.from(
              { length: faqData[`faq_items_${item}_faq_questions_answer`] },
              (_, index) => (
                <AccordionContent key={index}>
                  <Button
                    className={`
                    bg-background text-black hover:bg-background
                     ${
                       selectedQuestion ===
                       faqData[
                         `faq_items_${item}_faq_questions_answer_${index}_question`
                       ]
                         ? "text-destructive"
                         : ""
                     } `}
                    onClick={() =>
                      handleFaq(
                        faqData[
                          `faq_items_${item}_faq_questions_answer_${index}_answer`
                        ],
                        faqData[
                          `faq_items_${item}_faq_questions_answer_${index}_question`
                        ],
                        faqData[`faq_items_${item}_title`]
                      )
                    }
                  >
                    {
                      faqData[
                        `faq_items_${item}_faq_questions_answer_${index}_question`
                      ]
                    }
                  </Button>
                </AccordionContent>
              )
            )}
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};
export default FaqAccordion;
