import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@components/ui/accordion";
import ReactHtmlParser from "html-react-parser";

const AccordionDescription = ({ description }: { description: string }) => {
  return (
    <div className="mt-8 w-full">
      <Accordion type="multiple">
        <AccordionItem value="Details">
          <AccordionTrigger>Details</AccordionTrigger>
          <AccordionContent>{ReactHtmlParser(description)}</AccordionContent>
        </AccordionItem>
        <AccordionItem value="Versand">
          <AccordionTrigger>Versand</AccordionTrigger>
          <AccordionContent>
            <p>Info:</p>
            <p>
              Der Versand erfolgt in Deutschland und in die EU. Ab 100 €
              versenden wir kostenfrei.
            </p>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="Rückgabe">
          <AccordionTrigger>Rückgabe</AccordionTrigger>
          <AccordionContent>
            <p>Info:</p>
            <p>
              Für die Aufgabe einer Retoure schreibe uns einfach eine kurze
              Nachricht.
            </p>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};
export default AccordionDescription;
