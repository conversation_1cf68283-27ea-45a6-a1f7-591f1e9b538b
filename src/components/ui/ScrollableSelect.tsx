"use client";
import Spinner from "@components/general/Spinner";
import { cn } from "@lib/utils";
import { scrollableSelectProps } from "@typesDeclarations/GenericUITypes/InputTypes";
import { FunctionComponent } from "react";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./select";

/**
 * @description Custom (using ShadCN primitives) Select component
 * @usage mainly for Cart-related UIs  (Cart modal - Cart table)
 */
const ScrollableSelect: FunctionComponent<scrollableSelectProps> = ({
  className,
  options,
  loading,
  disabled,
  onValueChange,
  value,
  ...selectProps
}) => {
  return loading ? (
    <Spinner />
  ) : (
    <Select
      onValueChange={onValueChange}
      disabled={disabled}
      value={value}
      {...selectProps}
    >
      <SelectTrigger
        className={cn("w-full min-w-[4rem] max-w-full", className)}
      >
        <SelectValue defaultValue={value} />
      </SelectTrigger>
      <SelectContent className="max-h-60 overflow-y-auto">
        {options.map(({ label, value }) => (
          <SelectItem key={value} value={value}>
            {label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default ScrollableSelect;
