import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";
import React from "react";

const ContactSelect = ({ onChange }: any) => {
  return (
    <div>
      <Select onValueChange={onChange}>
        <SelectTrigger className="h-12 max-sm:w-full md:w-3/4  lg:w-3/4 xl:w-1/2">
          <SelectValue placeholder="Betreff" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectLabel placeholder="Betreff">Betreff</SelectLabel>
            <SelectItem value="Sonstges">Sonstges</SelectItem>
            <SelectItem value="Retoure/Reklamation">
              Retoure/Reklamation
            </SelectItem>
            <SelectItem value="Lieferung">Lieferung</SelectItem>
            <SelectItem value="Zahlung">Zahlung</SelectItem>
            <SelectItem value="Bestellung">Bestellung</SelectItem>
            <SelectItem value="Feedback Onlineshop">
              Feedback Onlineshop
            </SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
};

export default ContactSelect;
