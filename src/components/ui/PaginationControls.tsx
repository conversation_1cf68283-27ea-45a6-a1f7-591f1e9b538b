"use client";

import {
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";
import { useRouter, useSearchParams } from "next/navigation";
import React from "react";
import { FC } from "react";
interface PaginationControlsProps {
  hasNextPage: boolean;
  hasPrevPage: boolean;
}
const PaginationControls: FC<PaginationControlsProps> = ({
  hasNextPage,
  hasPrevPage,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const page = searchParams.get("page") ?? "1";
  const per_page = searchParams.get("per_page") ?? "1";
  const third_page = searchParams.get("third_page") ?? "2";

  return (
    <div className="flex flex-wrap">
      <div>
        <button
          className=" p-1 "
          disabled={!hasPrevPage}
          onClick={() => {
            router.push(`/?page=${Number(page) - 1}& per_page={per_page}`);
          }}
        >
          <div className="flex">
            <ChevronDoubleLeftIcon className="w-4" />
            <ChevronLeftIcon className="w-4" />
          </div>
        </button>
      </div>
      <div>
        {page} {Math.ceil(1 + Number(per_page))}{" "}
        {Math.ceil(1 + Number(third_page))}
      </div>
      <button
        className=" p-1 "
        disabled={!hasNextPage}
        onClick={() => {
          router.push(`/?page=${Number(page) + 1}&per_page={per_page}`);
        }}
      >
        <div className="flex">
          <ChevronRightIcon className="w-4" />
          <ChevronDoubleRightIcon className="w-4" />
        </div>
      </button>
    </div>
  );
};

export default PaginationControls;
