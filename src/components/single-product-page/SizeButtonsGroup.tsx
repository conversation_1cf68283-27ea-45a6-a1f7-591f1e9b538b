"use client";

import { FormatVariationsType } from "@lib/utilities/singleProduct";
import React, { useEffect } from "react";

import { useScreenSizeFlag } from "../../hooks/useScreenSizeFlag";
import { OptionState } from "./ProductInformation";
import SizeButton from "./sizeResponsive/SizeButton";
import SizeSelect from "./sizeResponsive/SizeSelect";

const SizeButtonsGroup = ({
  variations,
  handler,
  selectedSize,
  setSelectedSize,
  brand,
  category,
}: {
  variations: FormatVariationsType;
  handler: any;
  selectedSize: OptionState | null;
  // eslint-disable-next-line no-unused-vars
  setSelectedSize: (arg0: OptionState) => void;
  brand: string | null;
  category: string[];
}) => {
  const handleSizeClick = async (size: OptionState, databaseId: any) => {
    handler(databaseId);
    setSelectedSize(size);
  };

  const isSmallScreen = useScreenSizeFlag();

  useEffect(() => {
    if (variations && variations.length > 0) {
      const inStockVariation = variations.find(
        (variation) => variation.stock.is_in_stock
      );

      const defaultSize = inStockVariation ? inStockVariation : variations[0];

      handleSizeClick(
        {
          unitValue: defaultSize.unitValue,
          unitValueSlug: defaultSize.unitValueSlug,
        },
        defaultSize?.id
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [variations]);

  return (
    <div>
      {isSmallScreen ? (
        <SizeButton
          variations={variations}
          handleSizeClick={handleSizeClick}
          selectedSize={selectedSize}
          brand={brand}
          category={category}
        />
      ) : (
        <SizeSelect
          variations={variations}
          handleSizeClick={handleSizeClick}
          selectedSize={selectedSize}
        />
      )}
    </div>
  );
};

export default SizeButtonsGroup;
