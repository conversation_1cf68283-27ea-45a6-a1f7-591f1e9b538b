import { FormatVariationsType } from "@lib/utilities/singleProduct";
import { getPriceFormatter } from "@lib/utils";

import { OptionState } from "./ProductInformation";
//import Image from "next/image";

const ProductDetail = ({
  price,
  name,
  //  brandThumbnailUrl,
  //  brandName,
  regularPrice,
  salePrice,
  variations,
  selectedSize,
}: {
  price: number;
  name: string;
  brandThumbnailUrl?: string | null;
  brandName: string;
  regularPrice: number | null;
  salePrice: number | null;
  variations: FormatVariationsType;
  selectedSize: OptionState | null;
}) => {
  const { deutschFormatter } = getPriceFormatter(true);

  return (
    <div className="grid">
      <div className="col-start-1">
        <p className="text-wrap text-xl font-bold">{name}</p>
        <div className="mb-6 text-2xl">
          {selectedSize ? (
            variations?.map((variation) => (
              <div key={variation.id}>
                {selectedSize &&
                  variation.unitValue === selectedSize.unitValue && (
                    <div>
                      {!variation.prices.on_sale ||
                      !variation.prices.regular_price ||
                      !variation.prices.sale_price ? (
                        <div className="flex h-10 w-fit flex-wrap justify-center overflow-hidden py-2 ">
                          <p className="font-medium">
                            {deutschFormatter(variation.prices.price)}
                          </p>
                        </div>
                      ) : (
                        <div className="  flex h-10 w-fit flex-wrap  justify-center overflow-hidden py-2 ">
                          <p className=" mr-2 line-through">
                            {deutschFormatter(variation.prices.regular_price)}
                          </p>
                          <p className="font-medium text-destructive ">
                            - {deutschFormatter(variation.prices.price)}
                          </p>
                        </div>
                      )}
                    </div>
                  )}
              </div>
            ))
          ) : regularPrice === price || !regularPrice || !salePrice ? (
            <div className="flex h-10 w-fit flex-wrap justify-center overflow-hidden px-3 py-2 ">
              <p className=" font-medium">{deutschFormatter(price * 100)}</p>
            </div>
          ) : (
            <div className="  flex h-10 w-fit flex-wrap  justify-center overflow-hidden px-3 py-2 ">
              <p className=" mr-2 line-through">
                {deutschFormatter(regularPrice * 100)}
              </p>
              <p className="font-medium text-destructive ">
                - {deutschFormatter(price * 100)}
              </p>
            </div>
          )}
        </div>
        <p className="text-xs font-thin">inkl. MwSt. zzgl. Versandkosten</p>
      </div>
      {/*}
      <div className="col-start-2 m-2 mt-4">
        <Image src={brandThumbnailUrl} alt={brandName} width={80} height={80} />
      </div>
      {*/}
    </div>
  );
};

export default ProductDetail;
