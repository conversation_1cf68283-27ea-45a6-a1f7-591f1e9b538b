import { Button } from "@components/ui/Button";
import { FormatVariationsType } from "@lib/utilities/singleProduct";
//import { getPriceFormatter } from "@lib/utils";
import React, { useState } from "react";

import { getClothSizeString } from "../../../getClothSize";
import { OptionState } from "../ProductInformation";

const SizeButton = ({
  variations,
  handleSizeClick,
  selectedSize,
  brand,
  category,
}: {
  variations: FormatVariationsType;
  // eslint-disable-next-line no-unused-vars
  handleSizeClick: (arg0: OptionState, arg1: any) => void;
  selectedSize: OptionState | null;
  brand: string | null;
  category: string[];
}) => {
  const [hoveredButton, setHoveredButton] = useState<number | null>(null);

  //const { deutschFormatter } = getPriceFormatter(true);

  const handleMouseEnter = (buttonId: number) => {
    setHoveredButton(buttonId);
  };

  const handleMouseLeave = () => {
    setHoveredButton(null);
  };

  return (
    <div className="md:grid md:grid-cols-3 md:gap-1 lg:grid lg:grid-cols-3 lg:gap-1">
      {variations?.map((variation) => {
        const isOutOfStock = !variation.stock.is_in_stock;

        const eusize = getClothSizeString(brand, category, variation.unitValue);
        const ussize = variation.unitValue;

        const buttonText =
          hoveredButton === variation.id
            ? isOutOfStock
              ? "out of stock"
              : eusize
              ? `EU ${eusize}`
              : ussize
            : ussize;
        /*
            const buttonText = hoveredButton === node.databaseId
              ? isOutOfStock
                ? "out of stock"
                : deutschFormatter(node.price)
              : node.attributes.edges[0]?.node.value;
              */

        return (
          <div key={variation.id} className="my-1 grid w-full">
            <Button
              className={`w-full border-2 border-black px-2 py-1 text-center text-xs ${
                selectedSize?.unitValue === variation.unitValue
                  ? "bg-black text-white"
                  : isOutOfStock
                  ? "cursor-not-allowed border-gray-400 bg-gray-200 text-gray-400"
                  : "bg-white text-black hover:bg-black hover:text-white"
              }`}
              onClick={() =>
                handleSizeClick(
                  {
                    unitValue: variation.unitValue,
                    unitValueSlug: variation.unitValueSlug,
                  },
                  variation.id
                )
              }
              onMouseEnter={() => handleMouseEnter(variation.id)}
              onMouseLeave={handleMouseLeave}
            >
              {buttonText}
            </Button>
          </div>
        );
      })}
    </div>
  );
};

export default SizeButton;
