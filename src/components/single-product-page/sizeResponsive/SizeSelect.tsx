import { FormatVariationsType } from "@lib/utilities/singleProduct";
import { getPriceFormatter } from "@lib/utils";

import { OptionState } from "../ProductInformation";

const SizeSelect = ({
  variations,
  handleSizeClick,
  selectedSize,
}: {
  variations: FormatVariationsType;
  handleSizeClick: any;
  selectedSize: OptionState | null;
}) => {
  const { deutschFormatter } = getPriceFormatter(true);
  return (
    <div className="my-6">
      <div className="relative">
        <select
          className="h-11 w-full border border-black px-4 py-2 shadow-sm focus:border-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-600"
          onChange={(event) => {
            const { value } = event.target;
            const label = event.target.options[event.target.selectedIndex].text;
            handleSizeClick(label, value);
          }}
          placeholder={selectedSize?.unitValue || "size"}
        >
          <option className="hover:bg-black!">Select a size</option>
          {variations?.map((variation) => {
            const isOutOfStock = !variation.stock.is_in_stock;
            const buttonText = variation.id
              ? isOutOfStock
                ? "Out of Stock"
                : deutschFormatter(variation.prices.price)
              : variation.unitValue;

            return (
              <option
                key={variation.id}
                value={variation.id}
                label={`${variation.unitValue}  -  ${buttonText}`}
                className={`w-full ${
                  selectedSize?.unitValue === variation.unitValue
                    ? "bg-white text-black hover:bg-black hover:text-white"
                    : isOutOfStock
                    ? "cursor-not-allowed bg-gray-200 text-gray-400"
                    : "bg-white text-black hover:bg-black hover:text-white"
                }`}
              >
                {variation.unitValue}
              </option>
            );
          })}
        </select>
      </div>
    </div>
  );
};

export default SizeSelect;
