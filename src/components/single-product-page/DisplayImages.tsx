import { formattedProduct } from "@lib/utilities/singleProduct";
import React from "react";

import ImageSection from "./ImageSection";

const DisplayImage = ({
  formattedProduct,
}: {
  formattedProduct: formattedProduct;
}) => {
  return (
    <div>
      <ImageSection
        databaseId={formattedProduct!.id}
        link={formattedProduct!.galleryImages[0]}
        name={formattedProduct!.name}
        galleryImages={formattedProduct!.galleryImages}
      />
    </div>
  );
};

export default DisplayImage;
