"use client";

import { Button } from "@components/ui/Button";
import { FormatVariationsType } from "@lib/utilities/singleProduct";
import React from "react";

import { getClothSizeString } from "../../getClothSize";
import { OptionState } from "./ProductInformation";

const SizeModals = ({
  size,
  handleSizeSelection,
  selectedSize,
  setSelectedSize,
  brand,
  category,
}: {
  name: string;
  size: FormatVariationsType;
  // eslint-disable-next-line no-unused-vars
  handleSizeSelection: (arg0: any) => void;
  selectedSize: OptionState | null;
  // eslint-disable-next-line no-unused-vars
  setSelectedSize: (arg0: OptionState) => void;
  brand: string | null;
  category: string[];
}) => {
  return (
    <div className="flex max-h-[80vh] w-full flex-col overflow-x-auto md:max-h-none">
      <table className="w-full border-collapse border-2 border-gray-300">
        <caption className="w-full bg-black p-4 pr-12 text-left text-lg uppercase text-white md:table-caption">
          GRÖSSENTABELLE FÜR {brand}
        </caption>
        <tbody className="flex w-full flex-row md:block">
          <tr className="flex w-1/2 flex-col border-b-2 md:table-row md:w-auto">
            <td className="border-r-2 border-black p-4 text-center font-semibold md:text-left">
              US
            </td>
            {size?.map((variation, index: number) => (
              <td
                key={variation.id}
                className={`p-4 font-normal ${
                  index % 2 === 0 ? "bg-white" : "bg-gray-200"
                }`}
              >
                <Button
                  className={
                    selectedSize?.unitValue === variation.unitValue
                      ? "w-full min-w-[100px] whitespace-nowrap border-2 border-black bg-black text-white"
                      : "w-full min-w-[100px] whitespace-nowrap border-2 border-black bg-white text-black hover:bg-black hover:text-white"
                  }
                  // className="bg-transparent text-black hover:bg-transparent hover:scale-125"
                  onClick={() => {
                    setSelectedSize({
                      unitValue: variation.unitValue,
                      unitValueSlug: variation.unitValueSlug,
                    });
                    handleSizeSelection(variation.id);
                  }}
                >
                  {variation.unitValue}
                </Button>
              </td>
            ))}
          </tr>
          <tr className="flex w-1/2 flex-col border-b-2 md:table-row md:w-auto">
            <td className="border-r-2 border-black p-4 text-center font-semibold md:text-left">
              EU
            </td>
            {size?.map((variation, index: number) => {
              const eusize = getClothSizeString(
                brand,
                category,
                variation.unitValue
              );
              return (
                <td
                  key={variation.id}
                  className={`p-4 font-normal ${
                    index % 2 === 0 ? "bg-white" : "bg-gray-200"
                  }`}
                >
                  <Button
                    className={
                      selectedSize?.unitValue === variation.unitValue
                        ? "w-full whitespace-nowrap border-2 border-black bg-black text-white"
                        : "w-full whitespace-nowrap border-2 border-black bg-white text-black hover:bg-black hover:text-white"
                    }
                    // className="bg-transparent text-black hover:bg-transparent hover:scale-125"
                    onClick={() => {
                      setSelectedSize({
                        unitValue: variation.unitValue,
                        unitValueSlug: variation.unitValueSlug,
                      });
                      handleSizeSelection(variation.id);
                    }}
                  >
                    {eusize ? `EU ${eusize}` : "-"}
                  </Button>
                </td>
              );
            })}
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default SizeModals;
