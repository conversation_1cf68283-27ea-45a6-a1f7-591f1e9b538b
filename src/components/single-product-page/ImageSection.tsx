"use client";
import AddToWishListButton from "@components/wishlist/AddToWishListButton";
import Image from "next/image";
import { useState } from "react";

import { useScreenSizeFlag } from "../../hooks/useScreenSizeFlag";
import GroupImages from "./ImageResponsive/GroupImages";
import ImageEffect from "./ImageResponsive/ImageEffect";
import SwiperImage from "./ImageResponsive/SwiperImage";

const ImageSection = ({
  link,
  name,
  galleryImages,
  databaseId,
}: {
  link: string;
  name: string;
  galleryImages: string[] | null;
  databaseId: string | number;
}) => {
  // State to store the selected image
  const [selectedImage, setSelectedImage] = useState("");

  const isSmallScreen = useScreenSizeFlag();

  return (
    <div>
      {isSmallScreen ? (
        <div>
          <ImageEffect
            databaseId={databaseId}
            link={link}
            name={name}
            selectedImage={selectedImage}
          />
          <GroupImages
            galleryImages={galleryImages}
            setSelectedImage={setSelectedImage}
          />
        </div>
      ) : (
        <div>
          <div className="relative flex items-center justify-center">
            <AddToWishListButton
              className="absolute left-2 top-2"
              name={name}
              databaseId={databaseId}
            />
          </div>

          {galleryImages && galleryImages.length > 0 ? (
            <SwiperImage galleryImages={galleryImages} />
          ) : (
            <Image
              className="z-10 h-auto w-auto items-center justify-center border object-cover"
              src={link}
              alt={name}
              width={0}
              height={0}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default ImageSection;
