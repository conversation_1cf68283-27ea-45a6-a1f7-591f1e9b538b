import AddToWishListButton from "@components/wishlist/AddToWishListButton";
import { cn } from "@lib/utils";
import Image from "next/image";
import { MouseEvent, useState } from "react";

// Constants for magnifier size and zoom level
const ZOOM_LEVEL = 2.5;

const ImageEffect = ({
  link,
  name,
  selectedImage,
  databaseId,
}: {
  link: string;
  name: string;
  selectedImage: string | any;
  databaseId: string | number;
}) => {
  // State variables
  const [zoomable, setZoomable] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });

  // Event handlers
  const handleMouseEnter = () => {
    setZoomable(true);
  };

  const handleMouseLeave = () => {
    setZoomable(false);
  };

  const handleMouseMove = (e: MouseEvent) => {
    const { left, top, width, height } =
      e.currentTarget.getBoundingClientRect();
    const x = e.clientX - left;
    const y = e.clientY - top;
    setPosition({
      x: (x / width) * 100,
      y: (y / height) * 100,
    });
  };

  // Render method
  return (
    <div className="relative flex items-center justify-center">
      <div
        onMouseLeave={handleMouseLeave}
        onMouseEnter={handleMouseEnter}
        onMouseMove={handleMouseMove}
        className="group relative overflow-hidden"
      >
        <AddToWishListButton
          className={cn("absolute left-0 top-0", {
            block: zoomable,
            hidden: !zoomable,
          })}
          name={name}
          databaseId={databaseId}
        />
        <Image
          className="z-10 h-auto w-auto items-center justify-center object-cover group-hover:cursor-crosshair"
          src={selectedImage || link}
          alt={name}
          width={400}
          height={400}
          priority
        />
        {zoomable && (
          <div
            style={{
              backgroundPosition: `${position.x}% ${position.y}%`,
              backgroundImage: `url(${selectedImage || link})`,
              backgroundSize: `${100 * ZOOM_LEVEL}% ${100 * ZOOM_LEVEL}%`,
            }}
            className="pointer-events-none absolute left-0 top-0 z-0 h-full w-full"
          />
        )}
      </div>
    </div>
  );
};

export default ImageEffect;
