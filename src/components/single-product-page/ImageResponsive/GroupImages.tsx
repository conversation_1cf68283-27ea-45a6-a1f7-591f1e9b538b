import Image from "next/image";
import React, { Key } from "react";

const GroupImages = ({
  galleryImages,
  setSelectedImage,
}: {
  galleryImages: string | any | string[];
  setSelectedImage: any | string;
}) => {
  return (
    <div className="mt-2 md:grid md:grid-cols-3 md:gap-2 lg:grid lg:grid-cols-3 lg:gap-2">
      {galleryImages.map((ImageURL: string | any, index: Key | null) => (
        <div key={index} className="relative aspect-square bg-white">
          <Image
            src={ImageURL}
            alt={ImageURL}
            onClick={() => setSelectedImage(ImageURL)} // Set the selected image when clicked
            fill
            sizes="300px"
            className="object-contain"
          />
        </div>
      ))}
    </div>
  );
};

export default GroupImages;
