import "swiper/css";
import "swiper/css/pagination";
import "../../../app/ImageSwiper.css";

import Image from "next/image";
import React, { Key, useState } from "react";
import { A11y, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

const SwiperImage = ({ galleryImages }: { galleryImages: string[] | null }) => {
  const [activeSlide, setActiveSlide] = useState(0); // State to store the active slide index
  return (
    <div className="mt-4 grid h-full w-full grid-cols-1">
      <Swiper
        centeredSlides={true}
        pagination={{
          clickable: true,
        }}
        slidesPerView={1}
        modules={[Pagination, A11y]}
        className="m-auto h-full w-full"
        onSlideChange={(swiper) => setActiveSlide(swiper.activeIndex)} // Update active slide index when slide changes
      >
        {galleryImages?.map((ImageURL: string, index: Key | null) => (
          <SwiperSlide
            key={index}
            className="relative aspect-square w-full bg-white"
          >
            <Image
              alt={ImageURL}
              src={ImageURL}
              fill
              style={{ objectFit: "contain" }}
              sizes="300px"
              className={`m-auto  h-full w-full object-cover object-center
                ${index === activeSlide ? "block" : "hidden"}`}
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default SwiperImage;
