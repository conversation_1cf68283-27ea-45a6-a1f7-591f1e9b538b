"use client";

import { clothSizeVal } from "@components/refinements/clothsize";
import AddToCartButton from "@components/shoppingCart/AddToCartButton";
import AccordionDescription from "@components/ui/AccordionDescription";
import { ChevronRightIcon } from "@heroicons/react/24/outline";
import {
  formattedProduct,
  FormatVariationsType,
} from "@lib/utilities/singleProduct";
import React, { useEffect, useMemo, useState } from "react";

import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from "../../components/ui/dialog";
import { DropForm } from "./DropForm";
import ProductDetail from "./ProductDetail";
import SizeButtonsGroup from "./SizeButtonsGroup";
import SizeModals from "./SizeModals";

export type OptionState = {
  unitValue: string;
  unitValueSlug: string;
};

const ProductInformation = ({
  formattedProduct,
  variations,
}: {
  formattedProduct: formattedProduct;
  variations: FormatVariationsType;
}) => {
  const [databaseId, setDatabaseId] = useState<any>(null);
  const [selectedSize, setSelectedSize] = useState<OptionState | null>(null);
  const [isOutOfStock, setIsOutOfStock] = useState(false);

  const handler = (databaseId: any) => {
    setDatabaseId(databaseId);
  };

  useEffect(() => {
    if (selectedSize) {
      const selectedVariation = variations.find(
        (variation) => variation.unitValue === selectedSize.unitValue
      );

      if (selectedVariation) {
        setDatabaseId(selectedVariation.id);
        setIsOutOfStock(!selectedVariation.stock.is_in_stock);
      } else {
        setIsOutOfStock(false);
      }
    } else {
      setDatabaseId(formattedProduct.id);
      setIsOutOfStock(
        !variations || variations.length === 0
          ? formattedProduct.stockStatus === "outofstock"
          : variations?.every((variation) => !variation.stock.is_in_stock)
      );
    }
  }, [
    selectedSize,
    variations,
    formattedProduct.id,
    formattedProduct.stockStatus,
  ]);

  const sortedVariations = useMemo(() => {
    return variations?.sort((a, b) => {
      if (a?.unit === "attribute_pa_shoesize") {
        return (
          parseFloat(
            a.unitValue.replace("US ", "").replace("-", ".").replace(",", ".")
          ) -
          parseFloat(
            b.unitValue.replace("US ", "").replace("-", ".").replace(",", ".")
          )
        );
      }
      return clothSizeVal(a?.unitValue) - clothSizeVal(b?.unitValue);
    });
  }, [variations]);

  return (
    <div>
      {isOutOfStock && (
        <div className="bg-red-500 p-4 text-center text-white">
          Diese Ware ist nicht mehr auf Lager.
        </div>
      )}
      <div className="mx-4 my-2 lg:col-start-3">
        <ProductDetail
          brandThumbnailUrl={formattedProduct!.brands[0]?.thumbnailUrl}
          brandName={formattedProduct!.brands[0]?.name}
          name={formattedProduct!.name}
          price={formattedProduct!.price}
          regularPrice={formattedProduct!.regularPrice}
          salePrice={formattedProduct!.salePrice}
          variations={sortedVariations}
          selectedSize={selectedSize}
        />

        <p className="my-6 text-xs text-success">
          An Werktagen wird die Bestellung bis 16 Uhr noch am selben Tag
          verschickt.
        </p>
        {variations && variations.length > 0 && (
          <div className="flex w-full items-start justify-between">
            <p className="text-xs">
              {!formattedProduct.dropItem
                ? "Wähle deine Größe"
                : "Für den Drop anmelden"}
            </p>
            <div className="h-auto w-auto">
              {formattedProduct.productCategories
                .map((cat: string) => cat.toLowerCase())
                .includes("shoes") && (
                <Dialog>
                  <DialogTrigger>
                    <div className="flex w-auto justify-end text-xs text-primary">
                      <ChevronRightIcon className="w-4" /> Größentabelle
                    </div>
                  </DialogTrigger>
                  <DialogContent className="!h-auto px-0 pb-0 pt-0 [&_.icon]:h-7 [&_.icon]:w-7 [&_.icon]:text-white">
                    <SizeModals
                      name={formattedProduct!.name}
                      size={sortedVariations}
                      handleSizeSelection={handler}
                      selectedSize={selectedSize}
                      setSelectedSize={setSelectedSize}
                      brand={formattedProduct!.brands?.[0]?.name ?? null}
                      category={formattedProduct.productCategories}
                    />
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>
        )}

        {!formattedProduct.dropItem ? (
          <>
            <div className="w-full">
              <SizeButtonsGroup
                handler={handler}
                variations={sortedVariations}
                selectedSize={selectedSize}
                setSelectedSize={setSelectedSize}
                brand={formattedProduct!.brands?.[0]?.name ?? null}
                category={formattedProduct.productCategories}
              />
            </div>
            <div className="w-full">
              <AddToCartButton
                databaseId={databaseId}
                stockQuantity={formattedProduct.stockQuantity}
                stockStatus={isOutOfStock ? "OUT_OF_STOCK" : "IN_STOCK"}
                variation={
                  variations[0]?.unit
                    ? {
                        [variations[0]?.unit]: selectedSize?.unitValueSlug,
                        ...(formattedProduct.fuer
                          ? { ["attribute_pa_fuer"]: formattedProduct.fuer }
                          : {}),
                      }
                    : null
                }
              />
            </div>
          </>
        ) : (
          <DropForm
            id={formattedProduct.id}
            slug={formattedProduct.slug}
            productName={formattedProduct.name}
          />
        )}
        <div className="mx-4 my-10">
          <AccordionDescription description={formattedProduct!.description} />
        </div>
      </div>
    </div>
  );
};

export default ProductInformation;
