"use client";
import { searchClient } from "@lib/typesense/typesenseClient";
import React from "react";
import { InstantSearchNext } from "react-instantsearch-nextjs";

import { SwiperRecommendations } from "./SwiperRecommendations";

const Recommendations = ({
  objectID,
}: //objectID
{
  objectID: any;
}) => {
  return (
    <div>
      <InstantSearchNext
        indexName={process.env.ALGOLIA_INDEX}
        searchClient={searchClient}
      >
        <SwiperRecommendations objectID={objectID} />
      </InstantSearchNext>
    </div>
  );
};

export default Recommendations;
