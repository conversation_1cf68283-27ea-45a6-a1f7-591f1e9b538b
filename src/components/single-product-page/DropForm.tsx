"use client";
import { Button } from "@components/ui/Button";
import { Checkbox } from "@components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@components/ui/form";
import { Input } from "@components/ui/input";
import { Label } from "@components/ui/label";
import { useToast } from "@components/ui/use-toast";
import { ChevronRightIcon } from "@heroicons/react/24/outline";
import { zodResolver } from "@hookform/resolvers/zod";
import { sendEmail } from "@lib/restAPI/helpers/sendEmailHelper";
import { emailRegex, phoneNumberRegex } from "@lib/utilities/accountHelper";
import { FieldValues, useForm } from "react-hook-form";
import * as z from "zod";

const FormSchema = z.object({
  id: z.number(),
  slug: z.string(),
  productName: z.string(),
  vorname: z.string().nonempty({ message: "Vorname ist erforderlich" }),
  nachname: z.string().nonempty({ message: "Nachname ist erforderlich" }),
  email: z.string().regex(emailRegex, {
    message: "ungültige E-Mail-Adresse",
  }),
  telephone: z.string().regex(phoneNumberRegex, {
    message: "Bitte Nummer eingeben",
  }),
  politik: z.boolean().refine((val) => val),
});

export const DropForm: React.FC<{
  id: number;
  slug: string;
  productName: string;
}> = ({ id, slug, productName }) => {
  const { toast } = useToast();
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    mode: "onChange",
    defaultValues: {
      id: id,
      slug: slug,
      productName: productName,
      email: "",
      vorname: "",
      nachname: "",
      telephone: "",
      politik: false,
    },
  });
  const TOnSubmit = async (data: FieldValues) => {
    try {
      const response = await sendEmail(data, "CONTACT");
      if (response && response.code === 200) {
        toast({
          duration: 600,
          title: "Erfolg",
          description: "Sie wurden erfolgreich auf der Drop-Liste eingetragen.",
          variant: "success",
        });
      }
    } catch (e) {
      toast({
        duration: 600,
        title: "fehlgeschlagen",
        variant: "destructive",
        description: "Ihre Anmeldung konnte nicht abgeschickt werden.",
      });
    }
  };
  const isValid = form.formState.isValid;
  const { handleSubmit, control } = form;

  return (
    <div className="">
      <Form {...form}>
        <form onSubmit={handleSubmit(TOnSubmit)} className="space-y-2">
          <FormField
            control={control}
            name="vorname"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    type="text"
                    placeholder="Vorname*"
                    {...field}
                    className="flex h-12 w-full border"
                  />
                </FormControl>
                <FormMessage>
                  {form.formState.errors?.vorname?.message}
                </FormMessage>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="nachname"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    type="text"
                    placeholder="Nachname*"
                    {...field}
                    className="h-12 w-full"
                  />
                </FormControl>

                <FormMessage>
                  {form.formState.errors?.nachname?.message}
                </FormMessage>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="E-mail*"
                    {...field}
                    className="flex h-12 w-full border"
                  />
                </FormControl>
                <FormMessage>
                  {form.formState.errors?.email?.message}
                </FormMessage>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="telephone"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    type="text"
                    placeholder="Telefonnummer"
                    {...field}
                    className="flex h-12 w-full border"
                  />
                </FormControl>
                <FormMessage>
                  {form.formState.errors?.telephone?.message}
                </FormMessage>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="politik"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div {...field} className="mt-5 flex flex-row">
                    <Checkbox
                      id="policy"
                      className="h-6 w-6 border-gray-400 data-[state=checked]:bg-success"
                      form="text-white"
                      slot="h-5 w-5 bg-success"
                    />
                    <Label
                      htmlFor="policy"
                      className="ml-2 flex flex-wrap text-base font-bold max-md:flex-col"
                    >
                      <div className="flex flex-row">
                        <span>
                          Ich habe die Datenschutzbestimmungen zur Kenntnis
                          genommen.
                        </span>
                      </div>
                    </Label>
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
          <Button
            type="submit"
            disabled={!isValid}
            className="group relative mb-2 mt-5 h-10 bg-primary  hover:bg-primary disabled:bg-gray-500 max-md:mb-16 max-sm:ml-1 max-sm:mr-1 max-sm:h-12 max-sm:w-full sm:w-72"
          >
            <p className="text-base font-light uppercase animate-in group-hover:-translate-x-3">
              ABSENDEN
            </p>
            <ChevronRightIcon className="absolute right-2 hidden w-8 text-white animate-in group-hover:block group-hover:-translate-x-3 max-sm:w-12" />
          </Button>
        </form>
      </Form>
    </div>
  );
};
