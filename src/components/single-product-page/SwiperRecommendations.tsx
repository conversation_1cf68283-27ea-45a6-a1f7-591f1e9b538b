"use client";
import "swiper/css";
import "swiper/css/navigation";

import {
  InlineSwiperButtons,
  TopSwiperButtons,
} from "@components/homepage/SliderButtons";
import { getChangePercentage, getPriceFormatter } from "@lib/utils";
import Image from "next/image";
import Link from "next/link";
import { useLookingSimilar } from "react-instantsearch";
import { A11y, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

export const SwiperRecommendations = ({ objectID }: { objectID: any }) => {
  const recommendations = useLookingSimilar({ objectIDs: [objectID] });
  console.log("recommendations", objectID, recommendations);
  const { deutschFormatter } = getPriceFormatter(true, false);
  return (
    <div className="">
      <Swiper
        modules={[Navigation, A11y]}
        slidesPerView={3}
        spaceBetween={5}
        className=" relative flex pt-16 max-sm:w-5/6 md:w-3/4 lg:w-4/6 "
        breakpoints={{
          320: {
            slidesPerView: 1,
            spaceBetween: 20,
          },
          640: {
            slidesPerView: 2,
            spaceBetween: 20,
          },
          768: {
            slidesPerView: 2,
            spaceBetween: 40,
          },
          1024: {
            slidesPerView: 3,
            spaceBetween: 10,
          },
        }}
      >
        {recommendations.items.map((hit: any) => (
          <SwiperSlide className="translate-y-12 pt-12" key={hit.name}>
            <Link href={`/products/${hit.slug}`}>
              <div className="   mb-8  mt-16 flex  flex-col bg-white px-10">
                <div className="flex justify-end ">
                  <h1 className="self-start">
                    {hit.regular_price === hit.price ||
                    hit.regular_price === "" ||
                    hit.sale_price === "" ? (
                      ""
                    ) : (
                      <div className="flex max-w-sm">
                        <h1 className="absolute right-2 top-1 z-10">
                          {`-${getChangePercentage(
                            hit.price,
                            hit.regular_price
                          ).toString()}`}
                          %
                        </h1>
                      </div>
                    )}
                  </h1>
                </div>
                <Image
                  src={hit.images[0]}
                  alt={hit.name}
                  width={370}
                  height={370}
                  className=" bg-Plaze-white h-fit w-fit  "
                  priority={true}
                />
              </div>
              <div className=" h-auto ">
                <p className="xxl:py-4 overflow-hidden px-4 py-3 ">
                  {hit.name}
                </p>
              </div>
              <div className="px-4 py-1  ">
                <div className="flex h-10 w-fit flex-wrap justify-center  overflow-hidden bg-input py-2 ">
                  {hit.regular_price === hit.price ||
                  hit.regular_price === "" ||
                  hit.sale_price === "" ? (
                    <div className="flex h-10 w-fit flex-wrap justify-center bg-input px-3 py-0 ">
                      <p className=" font-medium ">
                        {deutschFormatter(hit.price)}
                      </p>
                    </div>
                  ) : (
                    <div className="  flex h-10 w-fit flex-wrap  justify-center  bg-input px-3 py-0 ">
                      <p className=" mr-2 line-through">
                        {deutschFormatter(hit.regular_price)}
                      </p>
                      <p className="font-medium text-destructive ">
                        {deutschFormatter(hit.price)}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </Link>
          </SwiperSlide>
        ))}

        <div className=" absolute top-0 z-10 flex  w-full  items-center   py-2">
          <p className="  mx-auto flex justify-center border-b-2 border-solid border-foreground px-4 text-center uppercase  max-sm:w-72 sm:max-w-fit ">
            EMPFEHLUNGEN ({recommendations.items.length})
          </p>
          <TopSwiperButtons />
        </div>
        <InlineSwiperButtons />
      </Swiper>
    </div>
  );
};
