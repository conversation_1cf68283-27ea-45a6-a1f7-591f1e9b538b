"use client";
import { Checkbox } from "@components/ui/checkbox";
import CustomeAccoridion from "@components/ui/CustomeAccoridion";
import React from "react";
import { useRefinementList } from "react-instantsearch";

const DecksizeRefinement = () => {
  const { items, refine } = useRefinementList({
    attribute: "pa_decksize",
    limit: 100,
    showMore: false,
    operator: "or",
  });
  if (items.length === 0) {
    return null;
  }
  return (
    <CustomeAccoridion
      title={"DECK GRÖSSE"}
      content={
        <div className="mb-4 space-y-0.5">
          {items
            .sort((a, b) => parseFloat(b.value) - parseFloat(a.value))
            .map((item, i) => (
              <label key={i} className="block cursor-pointer text-base">
                <Checkbox
                  onCheckedChange={() => refine(item.value)}
                  checked={item.isRefined}
                  className="border-foreground"
                />{" "}
                {item.label}
              </label>
            ))}
        </div>
      }
    />
  );
};

export default DecksizeRefinement;
