"use client";
import { Checkbox } from "@components/ui/checkbox";
import CustomeAccoridion from "@components/ui/CustomeAccoridion";
import React from "react";
import { useRefinementList } from "react-instantsearch";

export const clothSizeVal = (str: string): number => {
  if (!str) {
    return 0;
  }
  switch (str.toUpperCase()) {
    case "3XS":
    case "XXXS":
      return 0;
    case "2XS":
    case "XXS":
      return 1;
    case "XS":
      return 2;
    case "S":
      return 3;
    case "M":
      return 4;
    case "L":
      return 5;
    case "XL":
      return 6;
    case "XXL":
    case "2XL":
      return 7;
    case "XXXL":
    case "3XL":
      return 8;
    case "XXXXL":
    case "4XL":
      return 9;
    case "XXXXXL":
    case "5XL":
      return 10;
  }
  return 0;
};

const ClothsizeRefinement = () => {
  const { items, refine } = useRefinementList({
    attribute: "pa_clothsize",
    limit: 100,
    showMore: false,
    operator: "or",
  });
  if (items.length === 0) {
    return null;
  }
  return (
    <CustomeAccoridion
      title={"KLEIDUNG GRÖSSE"}
      content={
        <div className="mb-4 space-y-0.5">
          {items
            .sort((a, b) => {
              return clothSizeVal(a.value) - clothSizeVal(b.value);
            })
            .map((item, i) => (
              <label key={i} className="block cursor-pointer text-base">
                <Checkbox
                  onCheckedChange={() => refine(item.value)}
                  checked={item.isRefined}
                  className="border-foreground"
                />{" "}
                {item.label}
              </label>
            ))}
        </div>
      }
    />
  );
};

export default ClothsizeRefinement;
