"use client";

import { getFacetOrder } from "@lib/typesense/facetConfig";
import React from "react";
import { useCurrentRefinements } from "react-instantsearch";

interface OrderedFacetsProps {
  children: (_facets: any[]) => React.ReactNode;
}

/**
 * A component that orders facets according to the configuration
 * This is used to replicate Algolia's renderingContent.facetOrdering functionality
 */
const OrderedFacets: React.FC<OrderedFacetsProps> = ({ children }) => {
  const { items: facets } = useCurrentRefinements();

  // Sort facets according to the order defined in facetOrdering
  const orderedFacets = React.useMemo(() => {
    if (!facets) return [];

    return [...facets].sort((a, b) => {
      const orderA = getFacetOrder(a.attribute);
      const orderB = getFacetOrder(b.attribute);
      return orderA - orderB;
    });
  }, [facets]);

  return <>{children(orderedFacets)}</>;
};

export default OrderedFacets;
