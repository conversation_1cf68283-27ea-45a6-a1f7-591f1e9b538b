import { Button } from "@components/ui/Button";
import { Sheet, SheetTrigger } from "@components/ui/sheet";
import { Bars3Icon } from "@heroicons/react/24/outline";
import { navbarItem } from "@lib/utilities/navbarHelpers";
import { FC } from "react";

import { SideNavbarContent } from "./SideNavbar.client";

/**
 *
 * @unkownAs mobile navbar - side modal - dialog
 * @returns
 */
export const SideNavbar: FC<{ navbarEntries: navbarItem[] }> = async ({
  navbarEntries,
}) => {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button
          variant={"ghost"}
          size={"icon"}
          className="mr-auto h-12 w-12 p-2 md:hidden "
        >
          <Bars3Icon className="w-full" />
        </Button>
      </SheetTrigger>
      <SideNavbarContent entries={navbarEntries} />
    </Sheet>
  );
};
