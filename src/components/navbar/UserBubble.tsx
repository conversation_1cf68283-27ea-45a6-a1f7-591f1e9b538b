"use client";
import { Button } from "@components/ui/Button";
//import { useSessionContext } from "@contexts/userSessionProvider";
import { UserIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";

const UserBubble = () => {
  //const { state } = useSessionContext();
  const route = usePathname();
  //const username = state.customer?.displayName;
  return (
    <Button
      asChild
      variant={"ghost"}
      size={"icon"}
      className="p-2"
      onClick={() => {
        sessionStorage.setItem("prevRoute", route);
      }}
    >
      <Link
        href={
          //username ? "/dashboard/main" : "/login"
          1 + 1 === 1 ? "/dashboard/main" : "/login"
        }
      >
        <UserIcon className="w-full" />
      </Link>
    </Button>
  );
};

export default UserBubble;
