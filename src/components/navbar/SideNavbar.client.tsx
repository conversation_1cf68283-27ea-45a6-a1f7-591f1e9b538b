"use client";
import { But<PERSON> } from "@components/ui/Button";
import { Separator } from "@components/ui/separator";
import {
  SheetClose,
  She<PERSON><PERSON>onte<PERSON>,
  She<PERSON><PERSON>ooter,
  SheetHeader,
} from "@components/ui/sheet";
import {
  ChatBubbleLeftRightIcon,
  ChevronLeftIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import type { navbarItem } from "@lib/utilities/navbarHelpers";
import Link from "next/link";
import { FC, Fragment, useState } from "react";

/**
 *
 * @unkownAs mobile navbar - side modal - dialog
 * @returns
 */
export const SideNavbarContent: FC<{ entries: navbarItem[] }> = ({
  entries,
}) => {
  const [currentCategory, setCurrentCategory] = useState<
    (typeof entries)[0]["name"] | undefined
  >(undefined);

  const categoryEntries = entries.find(
    (entry) => entry.name === currentCategory
  );
  return (
    <SheetContent className="flex w-full flex-col bg-gray" side={"right"}>
      <SheetHeader className="gap-x-2 bg-gray-dark pl-2">
        <SheetClose asChild>
          <Button
            asChild
            className="flex w-fit flex-col text-white"
            variant={"icon"}
            size={"icon"}
          >
            <Link href="/contact">
              <ChatBubbleLeftRightIcon className="h-full w-auto" />
              <span className="block text-[10px] font-semibold">Kontakt</span>
            </Link>
          </Button>
        </SheetClose>

        <div className="flex-grow"></div>
        <SheetClose asChild>
          <Button
            onClick={() => setCurrentCategory(undefined)}
            variant={"outline"}
            size={"icon"}
            className="h-14 w-14 border-0 bg-primary p-2 text-white "
          >
            <XMarkIcon className="w-full" />
          </Button>
        </SheetClose>
      </SheetHeader>

      {/* MENU ENTRIES */}

      <div className="flex w-full  flex-grow flex-col gap-y-2 overflow-y-auto px-4 py-2">
        {/* If a category is not chosen */}
        {currentCategory ? (
          <section>
            <h3 className="font-semibold capitalize text-white">
              {currentCategory}
            </h3>
            <Button
              variant={"link"}
              className="p-0 capitalize text-white "
              onClick={() => {
                setCurrentCategory(undefined);
              }}
            >
              <ChevronLeftIcon className="h-4 w-4" />
              Zurück
            </Button>
            {categoryEntries?.columns?.map((col) => {
              const lowerCaseColName = col.columnName.toLowerCase();
              return (
                <ul className="my-8 list-none" key={col.columnName}>
                  {col.columnName.toLowerCase() !== "schuhe" && (
                    <>
                      <li className="my-3  " key={`${col.columnName}-name`}>
                        <h3 className="font-semibold  capitalize  text-white">
                          {lowerCaseColName}
                        </h3>
                      </li>
                      <li
                        className="my-3 "
                        key={`${lowerCaseColName} separator`}
                        role="hidden"
                      >
                        <Separator className="bg-accent-foreground opacity-75" />
                      </li>
                    </>
                  )}
                  {col.columnEntries.map((childCategory, index) => {
                    return (
                      <>
                        <li className="my-3" key={childCategory.url}>
                          <SheetClose asChild>
                            <Link
                              className=" capitalize text-white"
                              href={`/products/?hierarchicalMenu[category.lvl0][0]=${categoryEntries?.href}&hierarchicalMenu[category.lvl0][1]=${childCategory.url}`}
                            >
                              {childCategory.name}
                            </Link>
                          </SheetClose>
                        </li>
                        {Boolean(col.columnEntries.length - index > 1) && (
                          <li
                            key={`${childCategory.url}-separator`}
                            className="my-3 "
                            role="hidden"
                          >
                            <Separator className="bg-accent-foreground opacity-75" />
                          </li>
                        )}
                      </>
                    );
                  })}
                </ul>
              );
            })}
          </section>
        ) : (
          <>
            <section>
              <h3 className="text-white">Kategorien</h3>

              <ul className="my-3 flex list-none flex-col gap-y-2 ">
                {entries.map((category) => {
                  return (
                    <Fragment key={`${category.name}-fragment`}>
                      <li
                        role="navigation"
                        key={category.name}
                        className="  p-0"
                      >
                        {category.columns?.[0]?.columnEntries?.length &&
                        category.columns?.[0]?.columnEntries?.length > 0 ? (
                          <Button
                            className="p- capitalize text-white"
                            variant={"link"}
                            onClick={() => {
                              setCurrentCategory(category.name);
                            }}
                          >
                            {category.name}
                          </Button>
                        ) : (
                          <SheetClose asChild>
                            <Button
                              asChild
                              className="p- capitalize text-white"
                              variant={"link"}
                            >
                              <Link
                                href={`/products/?hierarchicalMenu[category.lvl0][0]=${category?.href}`}
                              >
                                {category.name}
                              </Link>
                            </Button>
                          </SheetClose>
                        )}
                      </li>

                      <li key={`${category.name}-separator`} role="none">
                        <Separator className="bg-accent-foreground opacity-75" />
                      </li>
                    </Fragment>
                  );
                })}
                {/*
                <li role="navigation" key="sale" className="  p-0">
                  <SheetClose asChild>
                    <Button className="p-0" asChild variant={"link"}>
                      <Link className="text-white" href={"sale"}>
                        sale
                      </Link>
                    </Button>
                  </SheetClose>
                </li>
                */}
              </ul>
            </section>
            <section id="service-et-support" className="my-4">
              <h3 className="text-white">Service & Support</h3>
              <ul
                className="my-3  flex
             flex-col gap-y-2 "
              >
                <li key="kontakt" className="capitalize text-white">
                  <SheetClose asChild>
                    <Button className="p-0" asChild variant={"link"}>
                      <Link className="text-white" href="/contact">
                        kontakt
                      </Link>
                    </Button>
                  </SheetClose>
                </li>
                <li key="kontakt-separator">
                  <Separator className="bg-accent-foreground opacity-75" />
                </li>
                {/*}
                <li key="Hilfecenter" className="  capitalize text-white">
                  <SheetClose asChild>
                    <Button className="p-0" asChild variant={"link"}>
                      <Link className="text-white" href="/faq">
                        Hilfecenter
                      </Link>
                    </Button>
                  </SheetClose>
                </li>
                {*/}
              </ul>
            </section>
          </>
        )}
        {/* IF a category is chosen */}
      </div>
      {currentCategory && (
        <SheetFooter className=" justify-center  p-6">
          <SheetClose asChild>
            <Button
              onClick={() => setCurrentCategory(undefined)}
              asChild
              variant="outline"
              className="border-white bg-transparent uppercase text-white "
            >
              <Link
                href={`/products/?hierarchicalMenu[category.lvl0][0]=${categoryEntries?.href}`}
              >
                ZUR KATEGORIE
              </Link>
            </Button>
          </SheetClose>
        </SheetFooter>
      )}
    </SheetContent>
  );
};
