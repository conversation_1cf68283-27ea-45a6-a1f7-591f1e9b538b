import { ChevronRightIcon } from "@heroicons/react/24/outline";
import type { navbarItem } from "@lib/utilities/navbarHelpers";
import * as NavigationMenu from "@radix-ui/react-navigation-menu";
import Image from "next/image";
import Link from "next/link";
import { FC } from "react";

export const MenuItem: FC<navbarItem> = ({
  name,
  href,
  columns,
  hisAndHers,
}) => {
  return (
    <>
      <NavigationMenu.Item className="border-b-4 border-solid border-transparent   hover:border-accent-foreground">
        <NavigationMenu.Trigger asChild className="m-1 p-4 uppercase">
          {href === "alle-marken" ? (
            <Link className="block" href={`/alle-marken`}>
              {name}
            </Link>
          ) : (
            <Link
              className="block"
              href={`/products?hierarchicalMenu[category.lvl0][0]=${href}`}
            >
              {name}
            </Link>
          )}
        </NavigationMenu.Trigger>
        {columns && (
          <NavigationMenu.Content className="absolute left-0 top-14 mt-3 flex w-full items-start justify-between overflow-x-auto border-t-[1px] border-accent-foreground bg-background p-8 px-24 shadow-lg">
            {columns?.map((col) => {
              return (
                <section
                  key={col.columnName}
                  className="flex min-w-fit select-none flex-col gap-x-10 gap-y-3"
                >
                  <h3 className="font-semibold uppercase">{col.columnName}</h3>

                  <div
                    className={`flex h-full max-h-full flex-col flex-wrap gap-y-2 ${
                      name === "marken" && col !== columns[0] ? "mt-6" : ""
                    }`}
                  >
                    {col.columnEntries.map((entry) => {
                      return (
                        <Link
                          key={entry.url}
                          href={
                            href === "alle-marken"
                              ? `/products?refinementList[brands.name][0]=${entry.url}`
                              : `/products?hierarchicalMenu[category.lvl0][0]=${href}&hierarchicalMenu[category.lvl0][1]=${entry.url}`
                          }
                          className="mr-8 hover:text-primary hover:underline"
                        >
                          {entry.name}
                        </Link>
                      );
                    })}
                  </div>
                </section>
              );
            })}
            {hisAndHers && (
              <section className="flex gap-x-2">
                {hisAndHers.map((entry) => {
                  return (
                    <Link
                      key={entry.title}
                      href={`/products?refinementList[attributes.options][0]=${entry.link}`}
                    >
                      <div className=" group/imageLink min-h-64 min-w-64 relative h-64  w-64">
                        <Image
                          src={entry.imageURL}
                          alt={entry.title}
                          width={0}
                          height={0}
                          priority
                          sizes="100vw"
                          className="h-full w-full"
                        />
                        <div className="absolute top-0  h-full w-full  bg-gradient-to-t from-accent-foreground opacity-25  transition-all group-hover/imageLink:to-accent-foreground group-hover/imageLink:opacity-90" />
                        <p className="absolute bottom-5 left-5 text-lg font-semibold capitalize text-white">
                          {entry.title}
                        </p>
                      </div>
                    </Link>
                  );
                })}
              </section>
            )}
            <Link
              key={href}
              href={
                href === "alle-marken"
                  ? "/alle-marken"
                  : `/products?hierarchicalMenu[category.lvl0][0]=${href}`
              }
              className="ml-8 flex gap-x-1 whitespace-nowrap  font-semibold text-primary hover:underline"
            >
              Zur Kategorie
              <ChevronRightIcon className="h-6 w-6" />
            </Link>
          </NavigationMenu.Content>
        )}
      </NavigationMenu.Item>
    </>
  );
};
