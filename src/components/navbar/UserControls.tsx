import AlgoliaSearchBoxMobile from "@components/algolia-search/AlgoliaSearchBoxMobile";
import ShoppingCartModal from "@components/shoppingCart/ShoppingCartModal";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@components/ui/tooltip";
import WishlistModal from "@components/wishlist/WishlistModal";
import { navbarItem } from "@lib/utilities/navbarHelpers";
import { FC } from "react";

import { SideNavbar } from "./SideNavbar";
import UserBubble from "./UserBubble";
const UserControls: FC<{ navbarEntries: navbarItem[] }> = ({
  navbarEntries,
}) => {
  return (
    <div className=" flex flex-grow  items-center justify-end gap-x-2 px-4 shadow-md md:shadow-none">
      <SideNavbar navbarEntries={navbarEntries} />
      <AlgoliaSearchBoxMobile />

      <WishlistModal />

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger>
            <UserBubble />
          </TooltipTrigger>
          <TooltipContent>Dein Profil</TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <ShoppingCartModal />
    </div>
  );
};

export default UserControls;
