import { navbarItem } from "@lib/utilities/navbarHelpers";
import * as NavigationMenu from "@radix-ui/react-navigation-menu";
import Link from "next/link";
import { FC } from "react";

import { MenuItem } from "./Navbar.client";

export const Navbar: FC<{ navbarEntries: navbarItem[] }> = async ({
  navbarEntries,
}) => {
  return (
    <nav className="sticky -top-1 z-30 ">
      <NavigationMenu.Root
        asChild
        orientation="horizontal"
        className="  hidden w-full items-center justify-center md:flex"
      >
        <NavigationMenu.List className=" center z-0 m-0 flex w-full list-none  justify-around bg-background shadow-lg md:px-[18%] lg:px-[25%]">
          {navbarEntries.map((item) => (
            <MenuItem
              key={item.name}
              name={item.name}
              href={item.href}
              columns={item.columns}
              hisAndHers={item.hisAndHers}
            />
          ))}
          <NavigationMenu.Item className="border-b-4 border-solid border-transparent   hover:border-accent-foreground">
            <NavigationMenu.Trigger asChild className="m-1 p-4 uppercase">
              <Link className="block" href={`/sale`}>
                Sale
              </Link>
            </NavigationMenu.Trigger>
          </NavigationMenu.Item>
        </NavigationMenu.List>
      </NavigationMenu.Root>
    </nav>
  );
};
