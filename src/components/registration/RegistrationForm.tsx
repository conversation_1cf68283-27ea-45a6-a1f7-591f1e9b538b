"use client";
import PasswordStrength from "@components/account-helpers/PasswordStrength";
import { Button } from "@components/ui/Button";
import { Checkbox } from "@components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@components/ui/form";
import { Input } from "@components/ui/input";
import { Label } from "@components/ui/label";
import {
  ChevronRightIcon,
  EyeIcon,
  EyeSlashIcon,
} from "@heroicons/react/24/outline";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import * as z from "zod";

import { useRegister } from "../../hooks/graphql/mutations/authHooks";
import { FormSchema } from "./FormSchema";

export const RegistrationForm: React.FC = () => {
  const { register, loading } = useRegister();

  const TOnSubmit = async (data: FieldValues) => {
    const {
      email,
      passwort: password,
      vorname: firstName,
      nachname: lastName,
    } = data;
    await register({
      variables: { email, password, firstName, lastName },
    });
  };

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    mode: "onChange",
    defaultValues: {
      email: "",
      passwort: "",
      vorname: "",
      nachname: "",
      politik: false,
    },
  });
  const isValid = form.formState.isValid;
  const [showPassword, setShowPassword] = useState(false);
  const password = form.getValues().passwort;
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(TOnSubmit)}>
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="email"
                  placeholder="E-mail"
                  {...field}
                  className="flex h-10 border sm:w-96"
                />
              </FormControl>
              <FormMessage>{form.formState.errors?.email?.message}</FormMessage>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="passwort"
          render={({ field }) => (
            <FormItem>
              <div className="sm:flex sm:flex-row">
                <FormControl>
                  <Input
                    type={showPassword ? "text" : "password"}
                    placeholder="Passwort"
                    {...field}
                    className="mt-5 h-10 sm:w-96"
                  />
                </FormControl>

                <button
                  type="button"
                  className="flex max-sm:ml-auto max-sm:mr-2 max-sm:-translate-y-7 sm:ml-2 sm:mt-8 sm:-translate-x-10 "
                  onClick={() => {
                    setShowPassword((prev) => !prev);
                  }}
                >
                  {showPassword ? (
                    <EyeSlashIcon className="w-5" />
                  ) : (
                    <EyeIcon className="w-5" />
                  )}
                </button>
              </div>
              <FormMessage>
                {form.formState.errors?.passwort?.message}
              </FormMessage>
            </FormItem>
          )}
        />
        {password && <PasswordStrength password={password} />}
        <FormField
          control={form.control}
          name="vorname"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Vorname"
                  {...field}
                  className="mt-5 flex h-10 border sm:w-96 "
                />
              </FormControl>
              <FormMessage>
                {form.formState.errors?.vorname?.message}
              </FormMessage>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="nachname"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Nachname"
                  {...field}
                  className="mt-5 flex h-10 border sm:w-96"
                />
              </FormControl>
              <FormMessage>
                {form.formState.errors?.nachname?.message}
              </FormMessage>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="politik"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div {...field} className="mt-5 flex flex-row">
                  <Checkbox
                    id="policy"
                    className="h-6 w-6 border-gray-400 data-[state=checked]:bg-success"
                    form="text-white"
                    slot="h-5 w-5 bg-success"
                  />
                  <Label
                    htmlFor="policy"
                    className="ml-2 flex flex-wrap text-base font-bold max-md:flex-col"
                  >
                    <div className="flex flex-row">
                      <span>Ich habe die</span>
                      <span className="min-lg:underline mx-1">
                        Datenschutzbestimmungen
                      </span>
                    </div>
                    <span>zur Kenntnis genommen.</span>
                  </Label>
                </div>
              </FormControl>
              <FormMessage>
                {form.formState.errors?.politik?.message}
              </FormMessage>
            </FormItem>
          )}
        />
        <Button
          type="submit"
          disabled={!isValid || loading}
          className="group relative mb-2 mt-5 h-10 bg-primary  hover:bg-primary disabled:bg-gray-500 max-md:mb-16 max-sm:ml-1 max-sm:mr-1 max-sm:h-12 max-sm:w-full sm:w-72"
        >
          <p className="text-base font-light uppercase animate-in group-hover:-translate-x-3">
            Jetzt registrieren
          </p>
          <ChevronRightIcon className="absolute right-2 hidden w-8 text-white animate-in group-hover:block group-hover:-translate-x-3 max-sm:w-12" />
        </Button>
        <p className=" mb-12 text-base font-light text-primary">
          Du bist bereits Kunde? Hier geht's zum
          <Link href="./login" className="pl-2 font-medium text-primary">
            Anmeldung.
          </Link>
        </p>
      </form>
    </Form>
  );
};
