import { emailRegex, validatePassword } from "@lib/utilities/accountHelper";
import * as z from "zod";

/**
 * @description
 * FormSchema to validate and check if the current data have been following these conditions.
 *  - email: a string that must match a regular expression for a valid email address, otherwise it will show the message "ungültige E-Mail-Adresse".
 *  - passwort: a string that must have a high level of security according to the zxcvbn library.
 *  - vorname: a string that must not be empty, otherwise it will show the message "Vorname ist erforderlich".
 *  - nachname: a string that must not be empty, otherwise it will show the message "Nachname ist erforderlich".
 *  - politik: a boolean that must be true, otherwise it will show the message "Bitte lesen Sie die Richtlinien".
 */
export const FormSchema = z.object({
  email: z.string().regex(emailRegex, {
    message: "ungültige E-Mail-Adresse",
  }),
  passwort: z
    .string()
    .nonempty({ message: "Passwort ist erforderlich" })
    .superRefine((val, ctx) => {
      validatePassword(val, ctx);
    }),
  vorname: z.string().nonempty({ message: "Vorname ist erforderlich" }),
  nachname: z.string().nonempty({ message: "Nachname ist erforderlich" }),
  politik: z.boolean().refine((val) => val, {
    message: "Bitte lesen Sie die Richtlinien",
  }),
});
