import "server-only";

import type { groupedBrands } from "@lib/utilities/brandsHelpers";
import { cn } from "@lib/utils";
import Link from "next/link";
import { FC } from "react";


/**
 *
 * @param list of brands to extract their initials
 * @description Render a list of alphabets (and 0-9 entry)
 * that act as indexers for brands
 * each index consists of a letter and a count of how many brands' start with said letter
 * @returns {React.ReactNode}
 */
export const BrandsIndex: FC<{ brands: groupedBrands; className?: string }> = ({
  brands,
  className,
}): React.ReactNode => {
  const indices = [..."abcdefghijklmnopqrstuvwxyz".split(""), "0-9"];
  return (
    <div
      // className="flex-cols mx-auto mt-10 flex"
      className={cn("mx-auto mt-10 flex  w-full  flex-col", className)}
    >
      <div className="mx-auto mb-4 w-full border-b-2 border-solid border-gray text-center">
        <span className=" border-b-2  border-solid border-foreground font-medium uppercase">
          ALLE MARKEN
        </span>
      </div>
      <ol className="flex min-w-[10rem] flex-wrap justify-center  gap-y-6 px-2  ">
        {indices.map((initial) => {
          const current = brands[initial] ?? [];
          const count = current.length;
          const enabled = Boolean(count);
          return (
            <li
              key={initial}
              aria-roledescription="button"
              className={cn(
                enabled ? "cursor-pointer" : "cursor-not-allowed",
                " min-w-[4rem]"
              )}
            >
              <Link
                title={`${count} Marken beginnend mit ${initial.toUpperCase()}`}
                href={`#${initial}`}
                key={initial}
                className={` wrap group/item flex select-none gap-x-1 p-1   ring-black  hover:ring-1
           ${
             enabled
               ? "is-enabled group/enabled text-black"
               : " pointer-events-none   text-gray-500/50 "
           } `}
              >
                <div className="text-2xl font-medium uppercase  group-[.is-enabled]:text-gray-400">
                  {initial}
                </div>
                <span className=" right-0 top-0 text-xs uppercase group-[.is-enabled]:text-gray-400">
                  {count}
                </span>
              </Link>
            </li>
          );
        })}
      </ol>
    </div>
  );
};
