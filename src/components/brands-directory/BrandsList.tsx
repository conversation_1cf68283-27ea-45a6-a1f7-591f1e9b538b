import type { brand, groupedBrands } from "@lib/utilities/brandsHelpers";
import Link from "next/link";
import { FC } from "react";

const Section: FC<{
  initial: string;
  brands: brand[];
}> = ({ initial, brands }) => {
  return (
    <li className=" w-full py-2 ">
      <Link href={`#${initial}`} key={initial}>
        <h3 id={initial} className=" font-medium uppercase">
          {initial}
        </h3>
      </Link>
      <ol className="flex w-full flex-wrap gap-y-3 mx-1">
        {brands?.map((brand) => {
          return (
            <li key={brand.slug} className="w-1/3 ">
              <Link key={brand.slug} href={`products/?refinementList[brands.name][0]=${brand.name}`}>
                <div className="overflow-hidden mx-1  max-sm:text-base">
                  {brand.name}
                </div>
              </Link>
            </li>
          );
        })} 
      </ol>
    </li>
  );
};

export const BrandsList: FC<{ brandsGroup: groupedBrands }> = ({
  brandsGroup,
}) => {
  return (
    <ol className="  mx-auto my-2 flex w-full flex-col gap-10 py-4">
      {Object.keys(brandsGroup).map((initial) => {
        return (
          <Section
            brands={brandsGroup[initial]}
            initial={initial}
            key={initial}
          />
        );
      })}
    </ol>
  );
};
