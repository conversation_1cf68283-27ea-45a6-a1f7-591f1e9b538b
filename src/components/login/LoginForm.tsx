"use client";
import { But<PERSON> } from "@components/ui/Button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@components/ui/form";
import { Input } from "@components/ui/input";
import { useToast } from "@components/ui/use-toast";
import {
  ChevronRightIcon,
  EyeIcon,
  EyeSlashIcon,
} from "@heroicons/react/24/outline";
import { zodResolver } from "@hookform/resolvers/zod";
import { sendEmail } from "@lib/restAPI/helpers/sendEmailHelper";
import { emailRegex } from "@lib/utilities/accountHelper";
import Link from "next/link";
import { useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import * as z from "zod";

import { useLogin } from "../../hooks/graphql/mutations/authHooks";

interface LoginFormProps {
  className: string;
}
const FormSchema = z.object({
  email: z.string().regex(emailRegex, {
    message: "ungültige E-Mail-Adresse",
  }),
  passwort: z.string().min(2, { message: "ungültige passwort" }),
});

export const LoginForm: React.FC<LoginFormProps> = () => {
  // mutation for submission [mutate, state]
  const { login, loading } = useLogin();
  const { toast } = useToast();

  const TOnSubmit = async (data: FieldValues) => {
    await login({
      variables: {
        username: data.email,
        password: data.passwort,
      },
    });
    // await setupUser();
  };
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    mode: "onChange",
    defaultValues: {
      email: "",
      passwort: "",
    },
  });
  const isValid = form.formState.isValid;
  const [showPassword, setShowPassword] = useState(false);

  const sendForgotPassEmail = async () => {
    try {
      const response = await sendEmail(
        {
          email: form.getValues().email,
          subject: "FORGOT PASSWORD",
        },
        "CONTACT"
      );
      if (response && response.code === 200) {
        toast({
          duration: 600,
          title: "Erfolg",
          description:
            "Wir haben Ihre E-Mail erhalten, wir werden uns so schnell wie möglich mit Ihnen in Verbindung setzen",
          variant: "success",
        });
      }
    } catch (e) {
      toast({
        duration: 600,
        title: "fehlgeschlagen",
        variant: "destructive",
        description: "E-Mail konnte nicht gesendet werden",
      });
    }
  };
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(TOnSubmit)}>
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  disabled={loading}
                  type="email"
                  placeholder="E-mail"
                  {...field}
                  className="flex h-10 border   sm:w-96"
                />
              </FormControl>
              <FormMessage>{form.formState.errors?.email?.message}</FormMessage>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="passwort"
          render={({ field }) => (
            <FormItem>
              <div className="sm:flex sm:flex-row">
                <FormControl>
                  <Input
                    disabled={loading}
                    type={showPassword ? "text" : "password"}
                    placeholder="Passwort"
                    {...field}
                    className="mt-5 h-10   sm:w-96 "
                  />
                </FormControl>

                <button
                  type="button"
                  className="flex max-sm:ml-auto max-sm:mr-2 max-sm:-translate-y-7 sm:ml-2 sm:mt-8 sm:-translate-x-10 "
                  onClick={() => {
                    setShowPassword((prev) => !prev);
                  }}
                >
                  {showPassword ? (
                    <EyeSlashIcon className="w-5" />
                  ) : (
                    <EyeIcon className="w-5" />
                  )}
                </button>
              </div>
              <FormMessage>
                {form.formState.errors?.passwort?.message}
              </FormMessage>
            </FormItem>
          )}
        />

        <Button
          type="button"
          disabled={!(form.getValues().email && !form.formState.errors.email)}
          onClick={sendForgotPassEmail}
          className=" flex bg-background text-sm text-primary hover:bg-background max-sm:justify-end"
        >
          <ChevronRightIcon className="w-4  text-primary lg:-translate-x-5 " />{" "}
          <p className="text-sm lg:-translate-x-5">Passwort vergessen?</p>
        </Button>
        <Button
          type="submit"
          disabled={!isValid || loading}
          className="group relative mb-2 mt-3 h-10 bg-primary  hover:bg-primary disabled:bg-gray-500 max-md:mb-16 max-sm:ml-1 max-sm:mr-1 max-sm:h-12 max-sm:w-full sm:w-72"
        >
          <p className="animate-in group-hover:-translate-x-3">Anmeldung</p>
          <ChevronRightIcon className="absolute right-0 hidden w-8 text-white animate-in group-hover:block group-hover:-translate-x-3 max-sm:w-12" />
        </Button>
        <p className=" mb-12 text-primary">
          Du bist neu möchtest dich
          <Link href="./register" className="pl-2 font-semibold text-primary">
            Registrieren?
          </Link>
        </p>
      </form>
    </Form>
  );
};
