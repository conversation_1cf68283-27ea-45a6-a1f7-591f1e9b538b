import { Separator } from "@components/ui/separator";
import { FunctionComponent } from "react";
import { useFormContext } from "react-hook-form";

import Card from "./CardOverview";
import { OverviewShoppingCartList } from "./overview-checkout/OverviewShoppingCartList";
import OverviewShoppingCartDescriptionCard from "./overview-checkout/OverviewShoppingCartSideCard";
import { OverviewShoppingCartTable } from "./overview-checkout/OverviewShoppingCartTable";

const regionNamesGerman = new Intl.DisplayNames(["de"], { type: "region" });
const CheckoutOverview: FunctionComponent = () => {
  const { getValues } = useFormContext();
  const values = getValues();

  const shippingAddress = values.shippingSameAsBilling
    ? values.billingAddress
    : values.shippingAddress;
  const shippingPostalCode = values.shippingSameAsBilling
    ? values.billingPostalCode
    : values.shippingPostalCode;
  const shippingCity = values.shippingSameAsBilling
    ? values.billingCity
    : values.shippingCity;
  const shippingCountry = values.shippingSameAsBilling
    ? values.billingCountry
    : values.shippingCountry;

  return (
    <div className="flex flex-col">
      <Separator className="my-4 py-2" />
      <div className="flex flex-col space-y-4 lg:flex-row lg:space-x-4 lg:space-y-0">
        <Card
          className="w-full lg:w-1/3"
          title="Persönliche Angaben"
          description="Ihre Persönlichen Details"
        >
          <p>
            <strong>First Name:</strong> {values.firstName}
          </p>
          <p>
            <strong>Last Name:</strong> {values.lastName}
          </p>
          <p>
            <strong>Phone:</strong> {values.phone}
          </p>
          <p>
            <strong>Email:</strong> {values.email}
          </p>
        </Card>
        <Card className="w-full lg:w-1/3" title="Abrechnungsdaten">
          <p>
            <strong>Address:</strong> {values.billingAddress}
          </p>
          <p>
            <strong>Postal Code:</strong> {values.billingPostalCode}
          </p>
          <p>
            <strong>City:</strong> {values.billingCity}
          </p>
          <p>
            <strong>Country:</strong>
            {regionNamesGerman.of(values.billingCountry)}
          </p>
        </Card>
        <Card
          className="w-full lg:w-1/3"
          title="Versandinformationen"
          description="Ihre Lieferadresse"
        >
          <p>
            <strong>Address:</strong> {shippingAddress}
          </p>
          <p>
            <strong>Postal Code:</strong> {shippingPostalCode}
          </p>
          <p>
            <strong>City:</strong> {shippingCity}
          </p>
          <p>
            <strong>Country:</strong> {regionNamesGerman.of(shippingCountry)}
          </p>
        </Card>
      </div>
      <div className="my-4 rounded-md border p-4 shadow-md">
        <div className="mx-auto flex h-full min-w-full max-w-full flex-row flex-wrap justify-end gap-2  md:flex-row">
          <OverviewShoppingCartList />
          <OverviewShoppingCartTable />
          <OverviewShoppingCartDescriptionCard />
        </div>
      </div>
    </div>
  );
};

export default CheckoutOverview;
