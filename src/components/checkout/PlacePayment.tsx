import Spinner from "@components/general/Spinner";
import {
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@components/ui/card";
import { PaymentElement } from "@stripe/react-stripe-js";
import { FunctionComponent } from "react";
import { useFormContext } from "react-hook-form";

interface PlacePaymentProps {
  shouldShow: boolean;
}

const paymentElementOptions = {
  layout: "tabs" as "tabs",
};
const PlacePayment: FunctionComponent<PlacePaymentProps> = ({
  shouldShow = true,
}) => {
  const {
    formState: { isSubmitting },
  } = useFormContext();
  return (
    <div className={shouldShow ? "" : "hidden"}>
      <CardHeader>
        <CardTitle>Zahlungsdetails</CardTitle>
        <CardDescription>
          Wählen Sie Ihre bevorzugte Zahlungsmethode
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="flex flex-col">
          <PaymentElement
            id="payment-element"
            options={paymentElementOptions}
          />
        </div>
      </CardContent>
      {isSubmitting && (
        <div className="flex h-[50dvh] w-full  items-center justify-center gap-x-2  ">
          <div className="m-0 p-0">
            <Spinner />
          </div>
        </div>
      )}
    </div>
  );
};

export default PlacePayment;
