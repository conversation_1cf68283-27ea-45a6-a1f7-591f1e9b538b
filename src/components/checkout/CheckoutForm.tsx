"use client";

import { Button } from "@components/ui/Button";
import { countryCodes } from "@constants/sessionConstants";
import { Separator } from "@radix-ui/react-select";
import {
  AddressElement,
  PaymentElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { StripeElements } from "@stripe/stripe-js";
import { useSearchParams } from "next/navigation";
import { useRef, useState } from "react";

export default function CheckoutForm() {
  const stripe = useStripe();
  const elements = useElements() as StripeElements;
  const searchParams = useSearchParams();
  const formValuesRef = useRef<any>(null);

  const [isLoading, setIsLoading] = useState(false);

  const [message, setMessage] = useState("");

  const handleSubmit = async (e: any) => {
    try {
      e.preventDefault();
      if (!stripe) throw Error("stripe wasn't initialized");
      setIsLoading(true);

      const orderId = searchParams.get("orderId");
      if (!orderId) throw Error("order id was not provided");

      const updatedUserData = {
        firstName: formValuesRef.current.name.split(" ")[0],
        lastName: formValuesRef.current.name.split(" ")[1],
        ...formValuesRef.current.address,
      };
      const updatedOrderResponse = await fetch("/update-order", {
        method: "PUT",
        mode: "cors",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          orderId,
          updatedUserData,
        }),
      });

      await updatedOrderResponse.json();

      const confirmPaymentResponse = await stripe.confirmPayment({
        elements,
        confirmParams: {
          // TODO setup proper return_url
          return_url: "https://example.com/success",
          // return_url: `${process.env.NEXT_PUBLIC_BASE_URL}/checkout-complete`,
        },
      });
      const { error } = confirmPaymentResponse;
      if (
        error.message &&
        (error.type === "card_error" || error.type === "validation_error")
      ) {
        setMessage(error.message);
      } else {
        setMessage("An unexpected error occurred.");
      }
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };

  const paymentElementOptions = {
    layout: "tabs" as "tabs",
  };

  return (
    <form
      id="payment-form"
      onSubmit={handleSubmit}
      className="flex flex-col py-3"
    >
      <PaymentElement id="payment-element" options={paymentElementOptions} />
      <Separator />
      <div className="">shipping address</div>
      <AddressElement
        onChange={(e) => {
          formValuesRef.current = e.value;
        }}
        options={{ mode: "shipping", allowedCountries: countryCodes }}
      />
      <div className="flex min-h-[4rem] w-full items-center">
        <Button
          className="w-full"
          disabled={isLoading || !stripe || !elements}
          id="submit"
        >
          Pay now
        </Button>
      </div>
      {/* Show any error or success messages */}
      {message && (
        <div
          id="payment-message"
          className="w-full text-center font-bold text-destructive"
        >
          {message}
        </div>
      )}
    </form>
  );
}
