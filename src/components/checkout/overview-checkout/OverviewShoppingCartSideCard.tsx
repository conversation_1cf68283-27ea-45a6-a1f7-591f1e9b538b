"use client";
import { Separator } from "@components/ui/separator";
import {
  convertPriceToFrontEnd,
  SS_getShippingMethod,
} from "@lib/utilities/StripeHelpers/checkoutUtils";
import { getPriceFormatter } from "@lib/utils";
import { FunctionComponent } from "react";

import { useUserStore } from "../../../state/user";
const { deutschFormatter: formatPrice } = getPriceFormatter();
const OverviewShoppingCartDescriptionCard: FunctionComponent = () => {
  const cart = useUserStore((s) => s.cart);

  const paymentMethod = SS_getShippingMethod();
  const discountTotal = cart?.totals.discount_total,
    shippingTotal = `${convertPriceToFrontEnd({
      price: `${paymentMethod.price}`,
      taxes: paymentMethod.taxes,
    })}`,
    subTotal = cart?.totals.subtotal,
    totalTax = cart?.totals.total_tax,
    total = cart?.totals.total;
  return (
    <section
      tabIndex={0}
      className="sticky top-72 mx-auto flex h-fit w-auto min-w-[fit] flex-col justify-between max-md:w-full"
    >
      <dl
        tabIndex={0}
        className="flex flex-col gap-y-7 bg-gray-bright max-md:w-full max-md:bg-white md:w-52 md:min-w-[20rem]  md:p-4 "
      >
        <div className="flex flex-row justify-between ">
          <dt className="text-sm font-light" tabIndex={0}>
            Zwischensumme
          </dt>
          <dd className="font-medium" tabIndex={0}>
            {formatPrice(subTotal ?? "")}
          </dd>
        </div>
        <div className="flex flex-row justify-between ">
          <dt className="text-sm font-light" tabIndex={0}>
            Steuer
          </dt>
          <dd className="font-medium" tabIndex={0}>
            {formatPrice(totalTax ?? "")}
          </dd>
        </div>
        <div className="flex flex-row justify-between">
          <dt className="text-sm font-light" tabIndex={0}>
            Versandkosten
          </dt>
          <dd tabIndex={0}> {formatPrice(Number(shippingTotal) * 100)}</dd>
        </div>
        <div className="flex flex-row justify-between">
          <dt className="text-sm font-light" tabIndex={0}>
            Rabatt
          </dt>
          <dd tabIndex={0}> -{formatPrice(discountTotal ?? "0")}</dd>
        </div>
        <Separator className=" bg-gray-middle" />
        <div className="flex flex-row justify-between">
          <dt className="text-sm font-light" tabIndex={0}>
            Gesamtsumme
          </dt>
          <dd tabIndex={0}>
            {formatPrice(Number(total) + Number(shippingTotal) * 100) ?? ""}
          </dd>
        </div>
        <Separator className=" bg-gray-middle" />
        <span className="mx-auto text-sm font-light text-gray-middle ">
          inkl. gesetzlich geltender MwSt.
        </span>
      </dl>
    </section>
  );
};
export default OverviewShoppingCartDescriptionCard;
