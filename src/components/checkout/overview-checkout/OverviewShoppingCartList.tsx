import { getPriceFormatter } from "@lib/utils";
import { values } from "lodash";
import Image from "next/image";
import Link from "next/link";
import { FunctionComponent } from "react";

import { CartProduct } from "../../../state/schema";
import { useUserStore } from "../../../state/user";
import { PriceCellWithCalculation } from "./OverviewShoppingCartTable";

/**
 *  @description single product in cart modal / cart page for small screens
 */
export const ShoppingCartProductCard: FunctionComponent<{
  product: CartProduct;
}> = ({ product }) => {
  const { deutschFormatter } = getPriceFormatter(true, true);
  return (
    <article className="h-50 relative grid w-full max-w-full grid-cols-2 overflow-hidden border-b-2 border-gray-light bg-white sm:grid-cols-[30%_45%_25%]">
      <div className="col-start-1 col-end-2 flex items-center justify-center sm:col-auto sm:row-auto">
        <Link href={`/products/${product.slug}`}>
          <Image
            priority
            src={product.featured_image}
            alt={`image of ${product.name}`}
            width={110}
            height={110}
            sizes="110px"
            className=" hover:brightness-95"
          />
        </Link>
      </div>
      <div className="col-span-2 flex h-full flex-grow flex-col justify-between px-4 sm:col-auto sm:row-auto ">
        <div className="  flex flex-col gap-y-2 overflow-hidden text-ellipsis  whitespace-pre-wrap py-2 data-[force-show=in-modal]:max-w-[8rem]">
          <h4 className=" font-medium">{product.name}</h4>
          <span>
            {values(product.meta.variation)[0] &&
              `Größe: ${values(product.meta.variation)[0]}`}
          </span>
        </div>
      </div>
      <div className="col-start-2 col-end-3 row-start-1 flex flex-col items-end justify-center sm:col-auto sm:row-auto">
        <PriceCellWithCalculation
          quantity={product.quantity.value}
          manualTotalPrice={deutschFormatter(product.price)}
          regularPrice={deutschFormatter(product.price_regular)}
          salePrice={
            product.price_sale === "0"
              ? undefined
              : deutschFormatter(product.price_sale)
          }
        />
      </div>
    </article>
  );
};

export const OverviewShoppingCartList: FunctionComponent<{
  /** @description forces the list to be rendered regardless of current screen size */
  forceShow?: boolean;
}> = ({ forceShow = false }) => {
  const cart = useUserStore((s) => s.cart);

  return (
    <ul
      className={`${forceShow ? "flex flex-col" : "md:hidden "} w-full gap-y-2`}
    >
      {cart?.items.map((product) => (
        <li key={product.id}>
          <ShoppingCartProductCard product={product} />
        </li>
      ))}
    </ul>
  );
};
