"use client";
import { Input } from "@components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@components/ui/popover";
import { CheckIcon } from "@heroicons/react/24/outline";
import { cn } from "@lib/utils";
import { FunctionComponent, useState } from "react";

import { useGetCountries } from "../../hooks/graphql/queries/countriesHooks";

interface WithContextComboboxCountrySelector {
  name: string;
  disabled: boolean;
  onChange: any;
  value: string;
}

const WithContextComboboxCountrySelector: FunctionComponent<
  WithContextComboboxCountrySelector
> = ({ name, value, onChange, disabled }) => {
  const { countries, loading } = useGetCountries();

  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const filteredCountries = countries?.filter((country) =>
    country.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Input
          disabled={loading}
          autoComplete="off"
          type="button"
          className=" w-full  px-4  text-left hover:bg-gray-100"
          value={
            loading
              ? "Länderliste wird geladen"
              : value
              ? countries?.find((country) => country.id === value)?.name
              : "Wählen Sie Ihr Land..."
          }
        />
      </PopoverTrigger>
      <PopoverContent className="max-h-48 w-64 overflow-y-auto p-0">
        <Input
          disabled={disabled}
          type="text"
          value={searchTerm}
          placeholder="Durchsuchen Sie Ihr Land..."
          className="h-9 w-full px-2"
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <div className="space-y-1">
          {filteredCountries?.length === 0 && <p>No country found</p>}
          {filteredCountries?.map((country) => (
            <button
              name={name}
              key={country.id}
              className="flex w-full items-center px-4 py-2 text-left hover:bg-gray-100"
              onClick={() => {
                onChange(country.id);
                setOpen(false);
              }}
            >
              {country.name}
              <CheckIcon
                className={cn(
                  "ml-auto h-4 w-4",
                  value === country.id ? "opacity-100" : "opacity-0"
                )}
              />
            </button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};

export { WithContextComboboxCountrySelector };
