import {
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@components/ui/card";
import { Input } from "@components/ui/input";
import { Label } from "@components/ui/label";
import { emailRegex, phoneNumberRegex } from "@lib/utilities/accountHelper";
import { FunctionComponent } from "react";
import { Controller, useFormContext } from "react-hook-form";

const CheckoutPersonalInformation: FunctionComponent = () => {
  const {
    control,
    formState: { isSubmitting },
  } = useFormContext();

  const defaultValues = {
    firstName: "",
    lastName: "",
    phone: "",
    email: "",
  };

  return (
    <>
      <CardHeader>
        <CardTitle>Persönliche Informationen</CardTitle>
        <CardDescription>
          Bitte geben Sie Ihre persönlichen Informationen ein.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Controller
              name="firstName"
              control={control}
              defaultValue={defaultValues.firstName}
              rules={{
                required: "Vorname ist erforderlich",
                pattern: {
                  value: /^[A-Za-z]{3,50}$/,
                  message: "muss mindestens 3 Zeichen lang sein",
                },
              }}
              render={({
                field: { name, onChange, onBlur, ref, value },
                fieldState: { invalid, error },
              }) => (
                <div>
                  <Label htmlFor="firstName">Vorname</Label>
                  <div className="flex flex-col">
                    <Input
                      className={`${
                        (invalid || error) && "ring-4 ring-destructive"
                      }`}
                      ref={ref}
                      name={name}
                      disabled={isSubmitting}
                      onBlur={onBlur}
                      onChange={onChange}
                      value={value}
                    />
                    {invalid && error && (
                      <span className="pt-1 text-sm font-medium text-destructive">
                        {error.message}
                      </span>
                    )}
                  </div>
                </div>
              )}
            />
            <Controller
              name="lastName"
              control={control}
              defaultValue={defaultValues.lastName}
              rules={{
                required: "Nachname ist erforderlich",
                pattern: {
                  value: /^[A-Za-z]{3,50}$/,
                  message: "muss mindestens 3 Zeichen lang sein",
                },
              }}
              render={({
                field: { name, onChange, onBlur, ref, value },
                fieldState: { invalid, error },
              }) => (
                <div>
                  <Label htmlFor="lastName">Nachname</Label>
                  <div className="flex flex-col">
                    <Input
                      className={`${
                        (invalid || error) && "ring-4 ring-destructive"
                      }`}
                      ref={ref}
                      name={name}
                      disabled={isSubmitting}
                      onBlur={onBlur}
                      onChange={onChange}
                      value={value}
                    />
                    {invalid && error && (
                      <span className="pt-1 text-sm font-medium text-destructive">
                        {error.message}
                      </span>
                    )}
                  </div>
                </div>
              )}
            />
          </div>

          <Controller
            name="phone"
            control={control}
            defaultValue={defaultValues.phone}
            rules={{
              required: false,
              pattern: {
                value: phoneNumberRegex,
                message: "Ungültiges Telefonnummernformat",
              },
            }}
            render={({
              field: { name, onChange, onBlur, ref, value },
              fieldState: { invalid, error },
            }) => (
              <div>
                <Label htmlFor="phone">Telefonnummer</Label>
                <div className="flex flex-col">
                  <Input
                    type="tel"
                    className={`${
                      (invalid || error) && "ring-4 ring-destructive"
                    }`}
                    ref={ref}
                    name={name}
                    disabled={isSubmitting}
                    onBlur={onBlur}
                    onChange={onChange}
                    value={value}
                  />
                  {invalid && error?.message && (
                    <span className="pt-1 text-sm font-medium text-destructive">
                      {error.message}
                    </span>
                  )}
                </div>
              </div>
            )}
          />
          <Controller
            name="email"
            control={control}
            defaultValue={defaultValues.email}
            rules={{
              required: "E-Mailadresse wird benötigt",
              pattern: {
                value: emailRegex,
                message: "Ungültiges Email-Format.",
              },
            }}
            render={({
              field: { name, onChange, onBlur, ref, value },
              fieldState: { invalid, error },
            }) => (
              <div>
                <Label htmlFor="email">E-Mail-Adresse</Label>
                <div className="flex flex-col">
                  <Input
                    type="email"
                    className={`${
                      (invalid || error) && "ring-4 ring-destructive"
                    }`}
                    ref={ref}
                    name={name}
                    disabled={isSubmitting}
                    onBlur={onBlur}
                    onChange={onChange}
                    value={value}
                  />
                  {invalid && error && (
                    <span className="pt-1 text-sm font-medium text-destructive">
                      {error.message}
                    </span>
                  )}
                </div>
              </div>
            )}
          />
        </div>
      </CardContent>
    </>
  );
};

export default CheckoutPersonalInformation;
