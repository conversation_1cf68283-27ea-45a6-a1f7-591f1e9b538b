import {
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@components/ui/card";
import { Checkbox } from "@components/ui/checkbox";
import { Input } from "@components/ui/input";
import { Label } from "@components/ui/label";
import { ShippingRadioGroupDemo } from "@components/ui/ShippingRadio-Group";
import { FunctionComponent } from "react";
import { Controller, useFormContext } from "react-hook-form";

import { WithContextComboboxCountrySelector } from "./WithContextComboboxCountrySelector";

const CheckoutPersonalInformation: FunctionComponent = () => {
  const {
    control,
    formState: { isSubmitting },
    watch,
  } = useFormContext();

  return (
    <>
      <CardHeader>
        <CardTitle>Zahlungsinformationen</CardTitle>
        <CardDescription>
          Geben Sie Rechnungs- und Versandinformationen an
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-4">
          <h2 className="text-xl font-medium">Rechnungsadresse</h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Controller
              name="billingAddress"
              control={control}
              rules={{ required: "Adresse ist erforderlich" }}
              render={({
                field: { name, onChange, onBlur, ref, value },
                fieldState: { invalid, error },
              }) => (
                <div>
                  <Label htmlFor="billingAddress">Adresse</Label>
                  <div className="flex flex-col">
                    <Input
                      className={`${
                        (invalid || error) && "ring-4 ring-destructive"
                      }`}
                      ref={ref}
                      name={name}
                      disabled={isSubmitting}
                      onBlur={onBlur}
                      onChange={onChange}
                      value={value}
                    />
                    {invalid && error && (
                      <span className="pt-1 text-sm font-medium text-destructive">
                        {error.message}
                      </span>
                    )}
                  </div>
                </div>
              )}
            />
            <Controller
              name="billingPostalCode"
              control={control}
              rules={{
                required: "Postleitzahl ist erforderlich",
                pattern: {
                  value: /^[A-Za-z0-9]{3,10}$/,
                  message: "Ungültige PLZ",
                },
              }}
              render={({
                field: { name, onChange, onBlur, ref, value },
                fieldState: { invalid, error },
              }) => (
                <div>
                  <Label htmlFor="billingPostalCode">PLZ</Label>
                  <div className="flex flex-col">
                    <Input
                      className={`${
                        (invalid || error) && "ring-4 ring-destructive"
                      }`}
                      ref={ref}
                      name={name}
                      disabled={isSubmitting}
                      onBlur={onBlur}
                      onChange={onChange}
                      value={value}
                    />
                    {invalid && error && (
                      <span className="pt-1 text-sm font-medium text-destructive">
                        {error.message}
                      </span>
                    )}
                  </div>
                </div>
              )}
            />
            <Controller
              name="billingCity"
              control={control}
              rules={{ required: "Stadt ist erforderlich" }}
              render={({
                field: { name, onChange, onBlur, ref, value },
                fieldState: { invalid, error },
              }) => (
                <div>
                  <Label htmlFor="billingCity">Stadt</Label>
                  <div className="flex flex-col">
                    <Input
                      className={`${
                        (invalid || error) && "ring-4 ring-destructive"
                      }`}
                      ref={ref}
                      name={name}
                      disabled={isSubmitting}
                      onBlur={onBlur}
                      onChange={onChange}
                      value={value}
                    />
                    {invalid && error && (
                      <span className="text-destructive">{error.message}</span>
                    )}
                  </div>
                </div>
              )}
            />
            <Controller
              name="billingCountry"
              control={control}
              rules={{ required: "Land ist erforderlich" }}
              render={({ field: { name, onChange, value } }) => (
                <div>
                  <Label htmlFor="billingCountry">Land</Label>
                  <div className="flex flex-col">
                    <WithContextComboboxCountrySelector
                      name={name}
                      disabled={isSubmitting}
                      onChange={onChange}
                      value={value}
                    />
                  </div>
                </div>
              )}
            />
          </div>

          <Controller
            name="shippingSameAsBilling"
            control={control}
            render={({ field: { name, onChange, ref, value } }) => (
              <div className="flex gap-x-2">
                <Checkbox
                  ref={ref}
                  id={name}
                  name={name}
                  checked={value}
                  onCheckedChange={onChange}
                />
                <Label htmlFor="shippingSameAsBilling">
                  Lieferadresse ist gleich wie Rechnungsadresse
                </Label>
              </div>
            )}
          />

          {!watch("shippingSameAsBilling") && (
            <div className="pt-6">
              <h2 className="text-xl font-medium">Versandadresse</h2>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <Controller
                  name="shippingAddress"
                  control={control}
                  rules={{ required: "Adresse ist erforderlich" }}
                  render={({
                    field: { name, onChange, onBlur, ref, value },
                    fieldState: { invalid, error },
                  }) => (
                    <div>
                      <Label htmlFor="shippingAddress">Adresse</Label>
                      <div className="flex flex-col">
                        <Input
                          className={`${
                            (invalid || error) && "ring-4 ring-destructive"
                          }`}
                          ref={ref}
                          name={name}
                          disabled={isSubmitting}
                          onBlur={onBlur}
                          onChange={onChange}
                          value={value}
                        />
                        {invalid && error && (
                          <span className="pt-1 text-sm font-medium text-destructive">
                            {error.message}
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                />
                <Controller
                  name="shippingPostalCode"
                  control={control}
                  rules={{
                    required: "Postleitzahl ist erforderlich",
                    pattern: {
                      value: /^[A-Za-z0-9]{3,10}$/,
                      message: "Invalid postal code format.",
                    },
                  }}
                  render={({
                    field: { name, onChange, onBlur, ref, value },
                    fieldState: { invalid, error },
                  }) => (
                    <div>
                      <Label htmlFor="shippingPostalCode">PLZ</Label>
                      <div className="flex flex-col">
                        <Input
                          className={`${
                            (invalid || error) && "ring-4 ring-destructive"
                          }`}
                          ref={ref}
                          name={name}
                          disabled={isSubmitting}
                          onBlur={onBlur}
                          onChange={onChange}
                          value={value}
                        />
                        {invalid && error && (
                          <span className="pt-1 text-sm font-medium text-destructive">
                            {error.message}
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                />
                <Controller
                  name="shippingCity"
                  control={control}
                  rules={{ required: "Stadt ist erforderlich" }}
                  render={({
                    field: { name, onChange, onBlur, ref, value },
                    fieldState: { invalid, error },
                  }) => (
                    <div>
                      <Label htmlFor="shippingCity">Stadt</Label>
                      <div className="flex flex-col">
                        <Input
                          className={`${
                            (invalid || error) && "ring-4 ring-destructive"
                          }`}
                          ref={ref}
                          name={name}
                          disabled={isSubmitting}
                          onBlur={onBlur}
                          onChange={onChange}
                          value={value}
                        />
                        {invalid && error && (
                          <span className="pt-1 text-sm font-medium text-destructive">
                            {error.message}
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                />
                <Controller
                  name="shippingCountry"
                  control={control}
                  rules={{ required: "Land ist erforderlich" }}
                  render={({ field: { name, onChange, value } }) => (
                    <div>
                      <Label htmlFor="shippingCountry">Land</Label>
                      <div className="flex flex-col">
                        <WithContextComboboxCountrySelector
                          name={name}
                          disabled={isSubmitting}
                          onChange={onChange}
                          value={value}
                        />
                      </div>
                    </div>
                  )}
                />
              </div>
            </div>
          )}
        </div>
        <div className="mt-5">
          <p className="mb-4 text-xl">Versandarten</p>
          <ShippingRadioGroupDemo />
        </div>
      </CardContent>
    </>
  );
};

export default CheckoutPersonalInformation;
