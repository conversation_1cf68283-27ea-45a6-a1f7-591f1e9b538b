import { FunctionComponent } from "react";

interface CardProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

const Card: FunctionComponent<CardProps> = ({ title, description, children, className }) => {
  return (
    <div className={`border rounded-md p-4 shadow-md ${className ?? ""}`}>
      <h2 className="text-lg font-bold">{title}</h2>
      {description && <p className="text-sm text-gray-600">{description}</p>}
      <div className="mt-4">{children}</div>
    </div>
  );
};

export default Card;