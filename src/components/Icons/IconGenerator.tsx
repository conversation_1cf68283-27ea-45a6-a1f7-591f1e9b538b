import Image from "next/image";
import { FC } from "react";

interface svgIconsGeneratorProps {
  className?: string;
  icon: any;
}

/**
 *
 * @param className - additional styling wheen needed
 * @param icon - svg icon path
 * @description used to generate a React Component (with default styles) given a certain SVG Icon
 */
const IconGenerator: FC<svgIconsGeneratorProps> = ({ className, icon }) => {
  return (
    <div className={className}>
      <Image
        alt="Bestellungen icon"
        className="h-full w-full"
        src={icon}
        width={40}
        height={40}
        sizes="40px"
      />
    </div>
  );
};
export default IconGenerator;
