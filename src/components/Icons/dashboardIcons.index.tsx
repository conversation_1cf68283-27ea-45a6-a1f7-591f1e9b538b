import Bestellungen from "@public/icons/Bestellungen.svg";
import Ebene from "@public/icons/Ebene_3.svg";
import FAQ from "@public/icons/FAQ.svg";
import Formular from "@public/icons/Formular.svg";
import Kontakt from "@public/icons/Kontakt.svg";

import IconGenerator from "./IconGenerator";

const BestellungenIcon = ({ className }: { className?: string }) => (
  <IconGenerator icon={Bestellungen} className={className} />
);
const FAQIcon = ({ className }: { className?: string }) => (
  <IconGenerator icon={FAQ} className={className} />
);

const LocationPinIcon = ({ className }: { className?: string }) => (
  <IconGenerator icon={Ebene} className={className} />
);
const FormularIcon = ({ className }: { className?: string }) => (
  <IconGenerator icon={Formular} className={className} />
);
const ContactIcon = ({ className }: { className?: string }) => (
  <IconGenerator icon={Kontakt} className={className} />
);

export {
  BestellungenIcon,
  FAQIcon,
  ContactIcon,
  LocationPinIcon,
  FormularIcon,
};
