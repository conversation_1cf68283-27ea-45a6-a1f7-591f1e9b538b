import * as z from "zod";

const addressFormSchema = {
  firstName: z
    .string({
      required_error: "Vorname ist erforderlich",
    })
    .nonempty(),
  lastName: z
    .string({
      required_error: "Nachname ist erforderlich",
    })
    .nonempty(),
  addressSupplement: z.string({
    required_error: "Adresszusatz ist erforderlich",
  }),
  street: z
    .string({
      required_error: "Straße ist erforderlich",
    })
    .nonempty(),
  nr: z
    .string({
      required_error: "Nr ist erforderlich",
    })
    .nonempty(),
  postcode: z
    .string({
      required_error: "PLZ ist erforderlich",
    })
    .nonempty(),
  city: z
    .string({
      required_error: "Stadt ist erforderlich",
    })
    .nonempty(),
  country: z.string(),
};

const addressFormDefaultValues = {
  firstName: "",
  lastName: "",
  addressSupplement: "",
  street: "",
  nr: "",
  postcode: "",
  city: "",
  country: "",
};
export { addressFormSchema, addressFormDefaultValues };
