"use client";
import Spinner from "@components/general/Spinner";
import { Button } from "@components/ui/Button";
import { Icon } from "@components/ui/Icon";
import { useSessionContext } from "@contexts/userSessionProvider";
import { PowerIcon } from "@heroicons/react/24/outline";
import { apolloClient } from "@lib/apollo/client";
import { clearCredentials } from "@lib/utilities/authUtils";
import { useRouter } from "next/navigation";
import { FC } from "react";

import { useLogout } from "../../hooks/graphql/mutations/authHooks";
const LogoutButton: FC = () => {
  const { logout, loggingOut } = useLogout();
  const { dispatch } = useSessionContext();
  const router = useRouter();
  const performLogout = async () => {
    await logout();
    clearCredentials();
    dispatch({ type: "RESET_STATE", payload: {} });
    apolloClient.clearStore();
    router.replace("/");
  };
  return (
    <Button
      onClick={performLogout}
      disabled={loggingOut}
      className="group-hover/logout-button:fill-text-destructive-foreground flex w-full justify-between border-0 py-2 pl-0 pr-2 text-sm capitalize transition-all group-hover/logout-button:bg-destructive group-hover/logout-button:pl-4  group-hover/logout-button:text-destructive-foreground"
      variant={"outline"}
    >
      Ausloggen
      <Icon
        intent={"default"}
        size={"small"}
        className="mx-0 my-auto transition-all  group-hover/logout-button:scale-110 group-hover/logout-button:text-destructive-foreground "
      >
        {loggingOut ? (
          <div role="status">
            <Spinner />
          </div>
        ) : (
          <PowerIcon />
        )}
      </Icon>
    </Button>
  );
};

export default LogoutButton;
