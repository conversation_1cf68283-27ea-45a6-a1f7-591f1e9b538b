"use client";

import { useMutation } from "@apollo/client";
import PasswordStrength from "@components/account-helpers/PasswordStrength";
import { Button } from "@components/ui/Button";
import { Form, FormField, FormItem, FormMessage } from "@components/ui/form";
import { useSessionContext } from "@contexts/userSessionProvider";
import { login } from "@gql-mutations/login.customer";
import { zodResolver } from "@hookform/resolvers/zod";
import { validatePassword } from "@lib/utilities/accountHelper";
import { FieldValues, useForm } from "react-hook-form";
import * as z from "zod";

import { useSendPasswordPersonalData } from "../../hooks/graphql/mutations/passwordPersonalDataHooks";
import ShowHidePassword from "./ShowHidePassword";
interface EditPassworddashProps {
  className: string;
  handleClose: () => void;
}
const FormSchema = z
  .object({
    passwort: z.string().nonempty({ message: "Passwort ist erforderlich" }),
    Neues_Passwort: z
      .string()
      .nonempty({ message: "Passwort ist erforderlich" })
      .superRefine((val, ctx) => validatePassword(val, ctx)),
    Neues_Passwort_wiederholen: z
      .string()
      .nonempty({ message: "Passwort ist erforderlich" }),
  })
  .superRefine(({ Neues_Passwort, Neues_Passwort_wiederholen }, ctx) => {
    if (Neues_Passwort !== Neues_Passwort_wiederholen) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Die Passwörter stimmten nicht überein",
        path: ["Neues_Passwort_wiederholen"],
      });
    }
  });
export const EditPasswordDash: React.FC<EditPassworddashProps> = ({
  handleClose,
}) => {
  const [loginFunc] = useMutation(login);
  const { state } = useSessionContext();
  const { sendPasswordPersonalData } = useSendPasswordPersonalData();
  const TOnSubmit = async (data: FieldValues) => {
    const isOldPasswordCorrect = await loginFunc({
      variables: { username: state.customer?.email, password: data.passwort },
    });
    if (
      !isOldPasswordCorrect.data ||
      !isOldPasswordCorrect.data.login?.customer?.jwtAuthToken
    ) {
      form.setError("passwort", { message: "Falsches altes Passwort" });
      return;
    }
    await sendPasswordPersonalData({
      variables: { password: data.Neues_Passwort },
    });
    handleClose();
  };

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    mode: "onChange",
    defaultValues: {
      passwort: "",
      Neues_Passwort_wiederholen: "",
      Neues_Passwort: "",
    },
  });
  const isValid = form.formState.isValid;
  const newPassword = form.getValues().Neues_Passwort;
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(TOnSubmit)}>
        <FormField
          control={form.control}
          name="passwort"
          render={({ field }) => (
            <FormItem>
              <ShowHidePassword
                field={{ ...field }}
                placeholder="Altes Passwort*"
              />
              <FormMessage>
                {form.formState.errors?.passwort?.message}
              </FormMessage>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="Neues_Passwort"
          render={({ field }) => (
            <FormItem>
              <ShowHidePassword
                field={{ ...field }}
                placeholder="Neues Passwort*"
              />
              <FormMessage>
                {form.formState.errors?.Neues_Passwort?.message}
              </FormMessage>
              {newPassword && <PasswordStrength password={newPassword} />}
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="Neues_Passwort_wiederholen"
          render={({ field }) => (
            <FormItem>
              <ShowHidePassword
                field={{ ...field }}
                placeholder="Neues Passwort wiederholen*"
              />
              <FormMessage>
                {form.formState.errors?.Neues_Passwort_wiederholen?.message}
              </FormMessage>
            </FormItem>
          )}
        />

        <Button
          type="submit"
          disabled={isValid ? false : true}
          className="group relative mb-2 mt-5 h-10 bg-primary  disabled:bg-black max-md:mb-16 max-sm:ml-1 max-sm:mr-1 max-sm:h-12 max-sm:w-full sm:w-72"
        >
          <p className="uppercase ">SPEICHERN</p>
        </Button>
      </form>
    </Form>
  );
};

export default EditPasswordDash;
