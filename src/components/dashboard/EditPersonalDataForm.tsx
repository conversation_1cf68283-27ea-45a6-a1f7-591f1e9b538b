"use client";
import Spinner from "@components/general/Spinner";
import { Button } from "@components/ui/Button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@components/ui/form";
import { Input } from "@components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { emailRegex, phoneNumberRegex } from "@lib/utilities/accountHelper";
import { FieldValues, useForm } from "react-hook-form";
import * as z from "zod";

import { useSendPersonalData } from "../../hooks/graphql/mutations/personalDataHooks";

const EditPersonalDataForm: React.FC<{ formData: any; handleClose: any }> = ({
  formData,
  handleClose,
}) => {
  const FormSchema = z.object({
    firstName: z.string({
      required_error: "Vorname ist erforderlich",
    }),
    surname: z.string({
      required_error: "Nachname ist erforderlich",
    }),
    email: z.string().regex(emailRegex, {
      message: "ungültige E-Mail-Adresse",
    }),
    phoneNumber: z.string().regex(phoneNumberRegex, {
      message: "ungültige Telefonnumme",
    }),
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    mode: "onChange",
    defaultValues: {
      firstName: formData?.firstName ?? "",
      surname: formData?.lastName ?? "",
      email: formData?.email ?? "",
      phoneNumber: formData?.billing?.phone ?? "",
    },
  });

  const { sendPersonalData, loading } = useSendPersonalData();
  const TOnSubmit = async (data: FieldValues) => {
    await sendPersonalData({
      variables: {
        firstName: data.firstName,
        lastName: data.surname,
        email: data.email,
        phone: data.phoneNumber,
      },
    });
    handleClose();
  };
  const { handleSubmit, control, formState } = form;
  const isValid = formState.isValid;

  return (
    <div className="w-full">
      <Form {...form}>
        <form onSubmit={handleSubmit(TOnSubmit)}>
          <FormField
            control={control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormControl className="mt-4 w-full">
                  <Input
                    type="text"
                    placeholder="Vorname*"
                    {...field}
                    className="flex   h-14 border"
                  />
                </FormControl>
                <FormMessage>
                  {formState.errors?.firstName?.message}
                </FormMessage>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="surname"
            render={({ field }) => (
              <FormItem>
                <FormControl className="mt-4 w-full">
                  <Input
                    type="text"
                    placeholder="Nachname*"
                    {...field}
                    className="flex   h-14 border"
                  />
                </FormControl>
                <FormMessage>{formState.errors?.surname?.message}</FormMessage>
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormControl className="mt-4 w-full">
                  <Input
                    type="text"
                    placeholder="E-Mail*"
                    {...field}
                    className="flex   h-14 border"
                  />
                </FormControl>
                <FormMessage>{formState.errors?.email?.message}</FormMessage>
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormControl className="mb-4  mt-4 w-full">
                  <Input
                    type="text"
                    placeholder="Telefonnumme"
                    {...field}
                    className="flex   h-14 border"
                  />
                </FormControl>
                <FormMessage>
                  {formState.errors?.phoneNumber?.message}
                </FormMessage>
              </FormItem>
            )}
          />
          <p className="text-gray-middle">*pflichtfeld</p>
          <Button
            type="submit"
            disabled={isValid || loading ? false : true}
            className="relative mb-2 mt-5 h-10 bg-black  hover:bg-primary disabled:bg-gray-500 max-md:mb-16 max-sm:ml-1 max-sm:mr-1 max-sm:h-12 max-sm:w-full sm:w-72"
          >
            {loading ? <Spinner /> : <p>SPEICHERN</p>}
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default EditPersonalDataForm;
