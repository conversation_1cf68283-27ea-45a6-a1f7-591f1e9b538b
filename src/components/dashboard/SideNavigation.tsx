import { Icon } from "@components/ui/Icon";
import { useSessionContext } from "@contexts/userSessionProvider";
import {
  ArrowRightIcon,
  ArrowTopRightOnSquareIcon,
  PowerIcon,
} from "@heroicons/react/24/outline";
import { cn } from "@lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { FC } from "react";

import LogoutButton from "./LogoutButton";

type sideNavMenuItem = {
  name: string;
  route: string;
  icon: typeof PowerIcon;
};

const mainContentMenu: Array<sideNavMenuItem> = [
  {
    icon: ArrowRightIcon,
    name: "Persönliche Daten",
    route: "personal-data",
  },
  {
    icon: ArrowRightIcon,
    name: "<PERSON><PERSON><PERSON>",
    route: "address",
  },
  {
    icon: ArrowRightIcon,
    name: "Alle Bestellungen",
    route: "orders",
  },
];

const helpMenu: Array<sideNavMenuItem> = [
  /*
  {
    icon: ArrowTopRightOnSquareIcon,
    name: "Hilfecenter",
    route: "/faq",
  },
  */
  {
    icon: ArrowTopRightOnSquareIcon,
    name: "Kontaktformular",
    route: "/contact",
  },
];

/**
 *
 * Loads the side navigation links in the user dashboard page
 * @param userName to display in the header when the user is on the main route
 * Otherwise it will display the name for the active item link
 */
const SideNavigation: FC = () => {
  const {
    state: { customer },
  } = useSessionContext();
  const userName = customer?.displayName;

  const pathname = usePathname().split("/");
  const activeRoute = pathname.at(-1);
  // Check for the active link-item based on the current route if the user on dashboard/main display the userName else display the name of the current active route
  const activeItem =
    activeRoute === "main"
      ? userName
        ? `Hallo ${userName}.`
        : "\n"
      : mainContentMenu.find((item) => item.route === `${activeRoute}`)?.name ??
        "";

  return (
    <section className=" my-8 flex min-w-[240px] flex-col gap-5 md:w-auto lg:my-0">
      <div className=" absolute top-52 lg:static ">
        <p className="mb-4 w-fit text-3xl" tabIndex={0}>
          {activeItem}
        </p>
      </div>
      <ul className="flex w-full flex-col gap-y-4 lg:max-w-[240px]">
        <li key="Mein-Konto" tabIndex={-1}>
          <h3 className="text-md select-none font-medium capitalize">
            Mein Konto
          </h3>
        </li>

        {mainContentMenu.map((item) => {
          const ItemIcon = item.icon;
          const isActive = item.route === activeRoute;
          return (
            <li key={item.route} className="group/logout-button w-full">
              <Link
                href={item.route}
                className={cn(
                  "group-hover/logout-button:fill-text-destructive-foregrounds flex w-full  justify-between py-2 pl-0  pr-2 text-sm transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 group-hover/logout-button:bg-primary group-hover/logout-button:pl-4 group-hover/logout-button:text-primary-foreground",
                  isActive ? "underline underline-offset-2" : ""
                )}
              >
                <p>{item.name}</p>
                <Icon
                  intent={"default"}
                  size={"small"}
                  className=" mx-0 my-auto transition-all group-hover/logout-button:scale-110 group-hover/logout-button:text-destructive-foreground"
                >
                  <ItemIcon />
                </Icon>
              </Link>
            </li>
          );
        })}
      </ul>

      {/* BRAUCHST DU HILFE */}
      <ul className=" mt-4 flex w-full flex-col gap-y-4 lg:max-w-[240px]">
        <li key="Brauchst-du-Hilfe" tabIndex={-1}>
          <h3 className="text-md select-none font-medium capitalize">
            Brauchst du Hilfe?
          </h3>
        </li>

        {helpMenu.map((item) => {
          const ItemIcon = item.icon;
          return (
            <li key={item.route} className="group/logout-button  w-full">
              <Link
                target="_blank"
                href={item.route}
                className="group-hover/logout-button:fill-text-destructive-foregrounds flex w-full  justify-between py-2 pl-0   pr-2 text-sm transition-all focus-visible:outline-none  
                focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 group-hover/logout-button:bg-primary
                group-hover/logout-button:pl-4 group-hover/logout-button:text-primary-foreground
                "
              >
                <p>{item.name}</p>
                <Icon
                  intent={"default"}
                  size={"small"}
                  className="mx-0 my-auto transition-all group-hover/logout-button:scale-110 group-hover/logout-button:text-destructive-foreground "
                >
                  <ItemIcon />
                </Icon>
              </Link>
            </li>
          );
        })}
        <li key={"logout"} className=" group/logout-button w-full ">
          <LogoutButton />
        </li>
      </ul>
    </section>
  );
};

export default SideNavigation;
