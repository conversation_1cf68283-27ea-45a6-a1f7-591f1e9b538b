"use client";

import { Button } from "@components/ui/Button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@components/ui/popover";
import { CheckIcon } from "@heroicons/react/24/outline";
import { cn } from "@lib/utils";
import * as React from "react";

import { useGetCountries } from "../../hooks/graphql/queries/countriesHooks";

const ComboxCountry = ({
  field,
  form,
}: {
  className?: string;
  field: any;
  form: any;
}) => {
  const { countries } = useGetCountries();

  const [open, setOpen] = React.useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          className="mt-4 w-full justify-between"
        >
          <span className="capitalize">
            {field.value
              ? countries?.find((country) => country.id === field.value)?.name
              : "Wählen Sie Ihr Land..."}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="max-h-48 w-64 overflow-y-auto p-0">
        <Command>
          <CommandInput
            placeholder="Durchsuchen Sie Ihr Land..."
            className="h-9 "
          />
          <CommandEmpty>No country found</CommandEmpty>
          <CommandGroup>
            {countries?.map((country) => (
              <CommandItem
                key={country.id}
                value={country.id as string}
                onSelect={() => {
                  form.setValue("country", country.id);
                  setOpen((prev) => !prev);
                }}
              >
                {country.name}
                <CheckIcon
                  className={cn(
                    "ml-auto h-4 w-4",
                    field.value === country.name ? "opacity-100" : "opacity-0"
                  )}
                />
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export { ComboxCountry };
