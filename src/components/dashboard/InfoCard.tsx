"use client";

import { Button } from "@components/ui/Button";
import { useState } from "react";

type InfoCardType = {
  viewHeader?: string;
  editHeader?: string;
  addButtonLabel?: string;
  editButtonLabel?: string;
  deleteButtonLabel?: string;
  viewButtonLabel?: string;
  EditForm?: React.ElementType;
  deleteHandler?: () => void;
  isLastStep?: boolean;
  children?: React.ReactNode;
  formData?: any;
};
/**
 * This component will basically loads each section in the Dashboard view
 * The component accepts as a props the edit form and passes to it a handler to be used when the submit is done to return to view mode and as children it takes the view component.
 * @param viewHeader The header that should be displayed when in view-mode
 * @param editHeader The header that should be displayed when in edit-mode
 * @param addButtonLabel The label that will be displayed when the data is deleted
 * @param editButtonLabel The label that will be displayed for the button when in edit mode.
 * @param deleteButtonLabel @optional A label for the delete button.
 * @param editButton The label for the delete button
 * @deleteButton The label for the delete button @optional  since not all sections have delete functionality.
 * @param deleteHandler  @optional If there a specific behavior after the delete operation is done we can pass a function to execute any logic required.
 * when it's needed a third label will be added incase we need to show a different label between edit and view mode.
 * @param EditForm The form component responsible for the editing functionality the variable name was capitalized to indicated that a component should be passed as props.
 * @param isLastStep indicates if the card is rendered as the last step so no need to add some styling like the bottom border.
 * @param children Basically the view-fields for the section.
 */
const InfoCard = ({
  viewHeader,
  editHeader,
  addButtonLabel,
  editButtonLabel,
  deleteButtonLabel,
  viewButtonLabel,
  EditForm,
  deleteHandler,
  isLastStep = false,
  children,
  formData,
}: InfoCardType) => {
  const [isEditMode, setIsEditMode] = useState(false);

  //   Move back and forward between view and edit mode also pass it to the edit form to handle moving back to view mode when the submit is finished
  const handleModeChange = () => {
    setIsEditMode((prevState) => !prevState);
  };
  return (
    <section className={`mb-3 pb-6 ${!isLastStep ? "border-b-2" : ""}`}>
      <div className="mb-1 flex items-center justify-between">
        <h3 className="font-medium">
          {isEditMode ? (editHeader ? editHeader : viewHeader) : viewHeader}
        </h3>
        {addButtonLabel ? (
          <div className="flex gap-4">
            <Button
              onClick={handleModeChange}
              className="bg-transparent p-0  normal-case text-black underline  hover:bg-transparent"
            >
              {isEditMode ? viewButtonLabel : addButtonLabel}
            </Button>
          </div>
        ) : (
          <div className="flex gap-4">
            {deleteButtonLabel && !isEditMode && (
              <Button
                onClick={deleteHandler}
                className="bg-transparent p-0 normal-case text-black  underline hover:bg-transparent  hover:text-destructive"
              >
                {deleteButtonLabel}
              </Button>
            )}
            <Button
              onClick={handleModeChange}
              className="bg-transparent p-0  normal-case text-black underline  hover:bg-transparent"
            >
              {isEditMode ? viewButtonLabel : editButtonLabel}
            </Button>
          </div>
        )}
      </div>
      {isEditMode ? (
        EditForm && (
          <EditForm handleClose={handleModeChange} formData={formData} />
        )
      ) : (
        <div className="w-full min-w-full">{children}</div>
      )}
    </section>
  );
};
export default InfoCard;
