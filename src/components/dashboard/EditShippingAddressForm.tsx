"use client";
import { ComboxCountry } from "@components/dashboard/ComboxCountry";
import Spinner from "@components/general/Spinner";
import { Button } from "@components/ui/Button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@components/ui/form";
import { Input } from "@components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { FieldValues, useForm } from "react-hook-form";
import * as z from "zod";

import { useUpdateShippingAddress } from "../../hooks/graphql/mutations/addressDashboardHooks";
import { addressFormSchema } from "./addressFormSchema";

const EditShippingAddressForm: React.FC<{
  formData: any;
  handleClose: any;
}> = ({ formData, handleClose }) => {
  const FormSchema = z.object(addressFormSchema);
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    mode: "onChange",
    defaultValues: {
      firstName: formData?.firstName ?? "",
      lastName: formData?.lastName ?? "",
      addressSupplement: formData?.address1 ?? "",
      street: "",
      nr: "",
      postcode: formData?.postcode ?? "",
      city: formData?.city ?? "",
      country: formData?.country ?? "",
    },
  });
  const { sendShippingAddress, loading } = useUpdateShippingAddress();
  const TOnSubmit = async (data: FieldValues) => {
    await sendShippingAddress({
      variables: {
        firstName: data.firstName,
        lastName: data.lastName,
        address1: data.addressSupplement,
        postcode: data.postcode,
        city: data.city,
        country: data.country,
      },
    });
    handleClose();
  };
  const { handleSubmit, control, formState } = form;
  const isValid = formState.isValid;
  return (
    <div className="max-w-full">
      <Form {...form}>
        <form onSubmit={handleSubmit(TOnSubmit)}>
          <FormField
            control={control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormControl className="mt-4 w-full">
                  <Input
                    type="text"
                    placeholder="Vorname*"
                    {...field}
                    className="flex   h-14 border"
                  />
                </FormControl>
                <FormMessage>
                  {formState.errors?.firstName?.message}
                </FormMessage>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormControl className="mt-4 w-full">
                  <Input
                    type="text"
                    placeholder="Nachname*"
                    {...field}
                    className="flex   h-14 border"
                  />
                </FormControl>
                <FormMessage>{formState.errors?.lastName?.message}</FormMessage>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="addressSupplement"
            render={({ field }) => (
              <FormItem>
                <FormControl className="mt-4 w-full">
                  <Input
                    type="text"
                    placeholder="Adresszusatz"
                    {...field}
                    className="flex   h-14 border "
                  />
                </FormControl>
                <FormMessage>
                  {formState.errors?.addressSupplement?.message}
                </FormMessage>
              </FormItem>
            )}
          />
          <div className="flex gap-6">
            <div className="flex-[3]">
              <FormField
                control={control}
                name="street"
                render={({ field }) => (
                  <FormItem>
                    <FormControl className="mt-4">
                      <Input
                        type="text"
                        placeholder="Straße*"
                        {...field}
                        className="flex   h-14 border"
                      />
                    </FormControl>
                    <FormMessage>
                      {formState.errors?.street?.message}
                    </FormMessage>
                  </FormItem>
                )}
              />
            </div>
            <div className="flex-[1]">
              <FormField
                control={control}
                name="nr"
                render={({ field }) => (
                  <FormItem>
                    <FormControl className="mt-4">
                      <Input
                        type="text"
                        placeholder="Nr*"
                        {...field}
                        className="flex   h-14 border"
                      />
                    </FormControl>
                    <FormMessage>{formState.errors?.nr?.message}</FormMessage>
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="flex gap-6">
            <div className="flex-[2]">
              <FormField
                control={control}
                name="postcode"
                render={({ field }) => (
                  <FormItem>
                    <FormControl className="mt-4">
                      <Input
                        type="text"
                        placeholder="PLZ*"
                        {...field}
                        className="flex   h-14 border"
                      />
                    </FormControl>
                    <FormMessage>
                      {formState.errors?.postcode?.message}
                    </FormMessage>
                  </FormItem>
                )}
              />
            </div>
            <div className="flex-[3]">
              <FormField
                control={control}
                name="city"
                render={({ field }) => (
                  <FormItem>
                    <FormControl className="mt-4">
                      <Input
                        type="text"
                        placeholder="Stadt*"
                        {...field}
                        className="flex   h-14 border"
                      />
                    </FormControl>
                    <FormMessage>{formState.errors?.city?.message}</FormMessage>
                  </FormItem>
                )}
              />
            </div>
          </div>

          <FormField
            control={control}
            name="country"
            render={({ field }) => (
              <FormItem>
                <FormControl className="mt-4">
                  <ComboxCountry field={field} form={form} />
                </FormControl>
                <FormMessage>{formState.errors?.country?.message}</FormMessage>
              </FormItem>
            )}
          />
          <p className="mt-2 text-gray-middle">*pflichtfeld</p>
          <p>{isValid}</p>
          <Button
            type="submit"
            disabled={!isValid || loading}
            className="relative mb-2 mt-5 h-10 bg-black  hover:bg-primary disabled:bg-gray-500 max-md:mb-16 max-sm:ml-1 max-sm:mr-1 max-sm:h-12 max-sm:w-full sm:w-72 md:w-96"
          >
            {loading ? (
              <Spinner />
            ) : (
              <p className="animate-in group-hover:-translate-x-3">
                ÄNDERUNGEN SPEICHERN
              </p>
            )}
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default EditShippingAddressForm;
