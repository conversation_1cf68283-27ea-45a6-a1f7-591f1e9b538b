import { FormControl } from "@components/ui/form";
import { Input } from "@components/ui/input";
import { EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import { ControllerRenderProps } from "react-hook-form";

const ShowHidePassword = ({
  field,
  placeholder,
}: {
  field: ControllerRenderProps;
  placeholder: string;
}) => {
  const [showPassword, setShowPassword] = useState(false);
  return (
    <div className="sm:flex sm:flex-row">
      <FormControl>
        <Input
          type={showPassword ? "text" : "password"}
          placeholder={placeholder}
          {...field}
          className="mt-5 h-10 sm:w-96 "
        />
      </FormControl>

      <button
        type="button"
        className="flex max-sm:ml-auto max-sm:mr-2 max-sm:-translate-y-7 sm:ml-2 sm:mt-8 sm:-translate-x-10 "
        onClick={() => {
          setShowPassword((prev) => !prev);
        }}
      >
        {showPassword ? (
          <EyeSlashIcon className="w-5" />
        ) : (
          <EyeIcon className="w-5" />
        )}
      </button>
    </div>
  );
};

export default ShowHidePassword;
