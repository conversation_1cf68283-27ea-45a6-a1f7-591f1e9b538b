"use client";
import "instantsearch.css/themes/satellite.css";
import "../../app/algolia.css";

import { ScrollArea } from "@components/ui/scroll-area";
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { searchClient } from "@lib/typesense/typesenseClient";
import { useState } from "react";
import { Hits, InstantSearch, SearchBox } from "react-instantsearch";

import { Dialog, DialogContent, DialogTrigger } from "../ui/dialog";
import SearchResults from "./SearchResults";

const AlgoliaSearchBoxMobile = () => {
  const [search, setSearch] = useState<string | null>(null);
  const [hasFocus, setHasFocus] = useState<boolean>(false);

  const [open, setOpen] = useState(false);

  const handleSearchChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    const query = event.target.value.trim();

    if (query.length > 0) {
      setHasFocus(true);
      setSearch(query);
    } else {
      setSearch(null);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <div className="w-10  p-2 md:hidden ">
          <MagnifyingGlassIcon />
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-[580px] overflow-clip p-2 pt-8 sm:p-4">
        <div
          className="flex h-full w-full flex-col justify-start"
          data-page="1"
        >
          <InstantSearch searchClient={searchClient} indexName="products">
            <SearchBox
              searchAsYouType={true}
              autoFocus={true}
              placeholder="Finde deinen Artikel..."
              className={`w-full flex-shrink-0 bg-white text-base ${
                hasFocus ? "border-black" : "border-gray-400"
              }`}
              onInput={handleSearchChange}
            />
            <ScrollArea className="h-[calc(100vh-130px)] md:h-[calc(100vh-180px)] lg:h-[calc(100vh-260px)]">
              {search && (
                <Hits
                  hitComponent={(props) => (
                    <HitComponent {...props} searchQuery={search} />
                  )}
                  onClick={() => {
                    setSearch(null);
                    setOpen(false);
                  }}
                />
              )}
            </ScrollArea>
          </InstantSearch>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AlgoliaSearchBoxMobile;

const HitComponent = ({ hit, onClick }: any) => {
  return <SearchResults hit={hit} onClick={onClick} />;
};
