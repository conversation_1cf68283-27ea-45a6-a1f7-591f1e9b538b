import "instantsearch.css/themes/satellite.css";

import Image from "next/image";
import Link from "next/link";

const SearchResults = ({ hit }: any) => {
  return (
    <div className="flex w-full flex-col">
      <Link href={`products/[_slug]`} as={`/products/${hit.slug}`}>
        <div className="m-auto flex items-center justify-between gap-4">
          <div className="hit-image-container h-15 w-15 max-sm:h-15 flex shrink-0 items-center justify-center max-sm:w-20 sm:h-12 sm:w-12">
            <Image
              src={hit.images[0]}
              alt={hit?.name}
              width={200}
              height={200}
              layout="fixed"
              sizes="80px"
              priority={true}
            />
          </div>
          <div className="w-full grow text-left">
            {hit.name && (
              <span className="mx-auto line-clamp-2 text-left text-xl max-sm:text-xs sm:text-sm">
                {hit.name}
              </span>
            )}
          </div>
          <div className="flex">
            <strong>{hit.price}€</strong>
          </div>
        </div>
      </Link>
    </div>
  );
};

export default SearchResults;
