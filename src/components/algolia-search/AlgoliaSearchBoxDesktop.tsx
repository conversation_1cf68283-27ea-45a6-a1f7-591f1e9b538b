"use client";
import "../../app/algolia.css";
import "instantsearch.css/themes/satellite.css";

import { searchClient } from "@lib/typesense/typesenseClient";
import { useEffect, useRef, useState } from "react";
import { Hits, InstantSearch, SearchBox } from "react-instantsearch";

import SearchResults from "./SearchResults";

const HitComponent = ({ hit, onClick }: any) => {
  return <SearchResults hit={hit} onClick={onClick} />;
};

const AlgoliaSearchBoxDesktop = () => {
  const [search, setSearch] = useState<string | null>(null);
  const [hasFocus, setHasFocus] = useState<boolean>(false);

  const useClickOutside = (handler: () => void) => {
    const ref = useRef<HTMLDivElement>(null);

    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (ref.current && !ref.current.contains(event.target as Node)) {
          handler();
          setHasFocus(false);
        }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }, [ref, handler]);

    return ref;
  };

  const handleSearchChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    const query = event.target.value.trim();

    if (query.length > 0) {
      setHasFocus(true);
      setSearch(query);
    } else {
      setSearch(null);
    }
  };

  const searchRef = useClickOutside(() => {
    setSearch(null);
  });

  return (
    <div className="hidden h-full w-full items-center justify-center md:flex">
      <div
        className="relative mx-auto w-1/3 min-w-[20rem] lg:min-w-[25rem]"
        data-page="1"
      >
        <InstantSearch searchClient={searchClient} indexName="products">
          <SearchBox
            placeholder="Finde dein Produkt..."
            className={`bg-white text-base ${
              hasFocus ? "border-black" : "border-gray-400"
            } rounded-md shadow-sm`}
            onInput={handleSearchChange}
          />
          {search && (
            <div
              className="absolute z-10 h-[1600%] w-full overflow-y-auto rounded-lg bg-white shadow-lg"
              ref={searchRef}
            >
              <Hits
                hitComponent={(props) => (
                  <HitComponent {...props} searchQuery={search} />
                )}
                onClick={() => setSearch(null)}
              />
            </div>
          )}
        </InstantSearch>
      </div>
    </div>
  );
};

export default AlgoliaSearchBoxDesktop;
