import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { useEffect, useMemo, useRef, useState } from "react";

export const SearchBar = ({
  data,
  handleFaq,
}: {
  data: any;
  handleFaq: any;
}) => {
  // Preserve formattedData calculation between re-renders if data is the same
  const formattedData = useMemo(
    () => (data ? mapDataToObjects(data) : []),
    [data]
  );

  const [filteredData, setFilteredData] = useState(formattedData);
  const [showFilteredList, setShowFilteredList] = useState(false);

  const handleFilter = (query: string) => {
    setShowFilteredList(true);
    // The underscore is a convention I use to distinguish variables that will be visible and used inside the scope they are defined in and get's destroyed on every function call.
    const _filteredData = formattedData.filter(
      (faq) =>
        faq.title.toLowerCase().includes(query) ||
        faq.question.toLowerCase().includes(query)
    );
    setFilteredData((_) => _filteredData);
  };
  // Capture when the user clicks on outside of the input and the ul and close the ul
  // I tried to use the onBlur event but the event propagation made the behavior closing the drop down instead of selecting a question So I used the event listeners approach
  // !TODO Probably needs refactoring in the future but for now it will do.
  const searchBarRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLUListElement>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchBarRef.current &&
        !searchBarRef.current.contains(event.target as Node) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowFilteredList(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  });

  return (
    <div className="relative sm:w-1/3">
      <div className="flex ">
        <input
          ref={searchBarRef}
          type="text"
          placeholder="Gib hier dein Suchbegriff ein..."
          className="w-full rounded-md border border-gray-300 px-4 py-2"
          onChange={(e) => {
            handleFilter(e.target.value.toLowerCase());
          }}
          onFocus={() => {
            setShowFilteredList(true);
          }}
        />
        <MagnifyingGlassIcon className="w-5 -translate-x-7" />
      </div>
      {showFilteredList && (
        <ul
          ref={dropdownRef}
          className="absolute z-10 mt-1 w-full list-inside list-disc rounded-lg bg-white p-4 shadow"
        >
          {!!filteredData.length ? (
            filteredData.map((data, index) => (
              <li
                key={index}
                className="cursor-pointer border-b border-gray-200 py-2 last:border-b-0 hover:bg-gray-100"
                onClick={() => {
                  handleFaq(data.answer, data.question, data.title);
                  setShowFilteredList(false);
                }}
              >
                {data.question}
              </li>
            ))
          ) : (
            <p className="cursor-pointer border-b border-gray-200 py-2 last:border-b-0 hover:bg-gray-100">
              Nichts gefunden
            </p>
          )}
        </ul>
      )}
    </div>
  );
};

/**
 * @param data The Query result from the backend.
 * @returns { title: string; question: string; answer: string }[].
 * Since filtering is only happening on the front end it will be much easier to change data-shape an filter the data.
 * A helper function that transforms FAQ data to make filtering more easy specially when filtering on the question text or on a specific category in tis case all question related to this category will be listed.
 */

function mapDataToObjects(
  data: any
): { title: string; question: string; answer: string }[] {
  const mappedData: { title: string; answer: string; question: string }[] = [];

  // Loop through each key in the data object
  Object.keys(data).forEach((key) => {
    // Discard keys starting with "_"
    if (key.startsWith("_")) {
      return;
    }

    // Check if the key represents a question
    if (key.endsWith("_question")) {
      // Extract the category index and question index from the key
      const [, , categoryIndex] = key.split("_");

      // Construct keys for the corresponding title and answer
      const titleKey = `faq_items_${categoryIndex}_title`;
      const answerArray = key.split("_");
      answerArray[answerArray.length - 1] = "answer";
      const answerKey = answerArray.join("_");
      // Extract the title and answer
      const title = data[titleKey];
      const question = data[key];
      const answer = data[answerKey];
      // Push an object containing title, question, and answer into mappedData
      mappedData.push({ title, question, answer });
    }
  });

  return mappedData;
}
