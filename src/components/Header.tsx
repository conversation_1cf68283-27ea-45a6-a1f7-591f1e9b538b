import { Banner } from "@components/homepage";
import { fetchNavbarEntries } from "@lib/actions/navbar-actions";
import { getHomePage } from "@lib/api";
import { processHomePageData } from "@lib/utilities/homePageHelpers";
import Image from "next/image";
import Link from "next/link";
import React, { FC } from "react";

import plaze<PERSON>ogo from "../../public/assets/plaze_logo.png";
import { Navbar } from "./navbar/Navbar";
import { SearchBar } from "./navbar/SearchBar";
import UserControls from "./navbar/UserControls";
/**
 * @description the header component is a server-side rendered component
 * that performs all data fetching then provides props to client-side components
 * (the navbar, userControls, and SearchAlgolia)
 * @see https://nextjs.org/docs/app/building-your-application/rendering/composition-patterns#supported-pattern-passing-server-components-to-client-components-as-props
 */

const Header: FC = async () => {
  // await fetch data
  try {
    const response = await getHomePage();

    const data = await processHomePageData(response.blocks);
    const navbarEntries = await fetchNavbarEntries();
    return (
      <header className="  max-w-screen contents flex-col bg-background md:justify-between ">
        <Banner data={data.banner} />
        <div className=" sticky top-0 z-[40] flex w-full flex-col bg-background pb-1 md:grid md:grid-cols-[1fr_auto_1fr] md:flex-row lg:pb-0 ">
          <Link href={"/"} className=" ml-3 h-16 w-[10rem]  min-w-[10rem] pt-2">
            <Image
              src={plazeLogo}
              alt="Plaze Logo"
              width={0}
              height={0}
              priority
              sizes="160px"
              className="h-full w-full"
            />
          </Link>
          <SearchBar />
          <UserControls navbarEntries={navbarEntries} />
        </div>
        <Navbar navbarEntries={navbarEntries} />
      </header>
    );
  } catch (e) {
    console.error(e);
  }
  return null;
};

export default Header;
