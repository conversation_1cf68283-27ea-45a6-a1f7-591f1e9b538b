import ApolloClientProvider from "@contexts/ApolloClientProviderWrapper";
import { DialogProvider } from "@contexts/DialogProvider";
import localFont from "next/font/local";
import NextTopLoader from "nextjs-toploader";
import React from "react";

const untitledSans = localFont({
  src: [
    {
      path: "../../public/fonts/untitled-sans-web-light.woff",
      weight: "300",
    },
    {
      path: "../../public/fonts/untitled-sans-web-regular.woff",
      weight: "400",
    },
    {
      path: "../../public/fonts/untitled-sans-web-medium.woff",
      weight: "500",
    },
  ],
  variable: "--font-untitledSans",
});

export const metadata = {
  title: "Plaze Shop - Dein Skateshop aus Chemnitz ",
  description:
    "Bei Plaze findest du neueste Sneaker, Skateboards und Streatwear. Zu günstigen Preisen zum bestellen oder abholen in Chemnitz.",
  keywords:
    "<PERSON><PERSON><PERSON>, Skateboards, Streatwear, Chemnitz, Skate Shop, Skate Shop Chemnitz, Skate Shop in Chemnitz, Skate Shop Chemnitz",

  icons: {
    icon: "/favicon.ico",
  },
};

export const jsonld = {
  "@context": "https://schema.org",
  "@type": "ClothingStore",
  name: "Plaze Chemnitz",
  image: "https://plaze-shop.de/front.jpg",
  "@id": "",
  url: "https://plaze-shop.de",
  telephone: "0371 6660081",
  address: {
    "@type": "PostalAddress",
    streetAddress: "Innere Klosterstraße 13",
    addressLocality: "Chemnitz",
    postalCode: "09111",
    addressCountry: "DE",
  },
  geo: {
    "@type": "GeoCoordinates",
    latitude: 50.8335557,
    longitude: 12.9180208,
  },
  openingHoursSpecification: [
    {
      "@type": "OpeningHoursSpecification",
      dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      opens: "10:00",
      closes: "19:00",
    },
    {
      "@type": "OpeningHoursSpecification",
      dayOfWeek: "Saturday",
      opens: "10:00",
      closes: "18:00",
    },
  ],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="de">
      <body
        className={`${untitledSans.variable} max-w-screen m-0 flex h-screen flex-col font-sans`}
      >
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonld) }}
        />
        <NextTopLoader color="#f97316" showSpinner={false} />
        <ApolloClientProvider>
          <DialogProvider>{children}</DialogProvider>
        </ApolloClientProvider>
      </body>
    </html>
  );
}
