"use client";

import CheckoutOverview from "@components/checkout/CheckoutOverview";
import CheckoutPaymentDetails from "@components/checkout/CheckoutPaymentDetails";
import CheckoutPersonalInformation from "@components/checkout/CheckoutPersonalInformation";
import PlacePayment from "@components/checkout/PlacePayment";
import Spinner from "@components/general/Spinner";
import { Button } from "@components/ui/Button";
import { Card, CardFooter } from "@components/ui/card";
import { Progress } from "@components/ui/progress";
import { useToast } from "@components/ui/use-toast";
import { STRIPE } from "@constants/sessionConstants";
import {
  convertPriceToWoocommerceOrder,
  SS_getShippingMethod,
} from "@lib/utilities/StripeHelpers/checkoutUtils";
import { useElements, useStripe } from "@stripe/react-stripe-js";
import { StripeElements } from "@stripe/stripe-js";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";

import { FetchedShippingMethod } from "../../../hooks/graphql/queries/cartHooks";
import { useUserStore } from "../../../state/user";

export default function Checkout() {
  const { toast } = useToast();

  /** 3 steps: personalInformation - paymentDetails - overview  */
  const stepsCount = 2;
  const [currentStep, setCurrentStep] = useState<number>(0);

  // Stripe-related logic
  let clientSecret: { id: any };
  try {
    clientSecret = JSON.parse(
      sessionStorage.getItem(STRIPE.paymentIntent) as string
    );
  } catch (error) {
    console.error("Error parsing clientSecret:", error);
  }

  const stripe = useStripe();
  const elements = useElements() as StripeElements;

  // Plaze-visitor-related logic

  const cart = useUserStore((data) => data.cart);
  const loading = useUserStore((data) => data.loading);

  /** storing the order makes sure we won't create duplicate `WooCommerce Orders`, since order creation and payment finalization are 2 separate transactions
   * we should only create one order no matter how many **(incomplete)** payments attempts take place
   */
  const [wooCommerceOrder, setWooCommerceOrder] = useState<any>(undefined);
  // Form management
  const methods = useForm({
    mode: "onChange",
    defaultValues: async () => {
      stepsCount;
      return {
        firstName: cart?.customer?.billing_address.billing_first_name ?? "",
        lastName: cart?.customer?.billing_address.billing_last_name ?? "",
        phone: cart?.customer?.billing_address.billing_phone ?? "",
        email: cart?.customer?.billing_address.billing_email ?? "",
        shippingSameAsBilling: true,
        billingAddress: cart?.customer?.billing_address.billing_address_1 ?? "",
        billingPostalCode:
          cart?.customer?.billing_address.billing_postcode ?? "",
        billingCity: cart?.customer?.billing_address.billing_city ?? "",
        billingCountry: cart?.customer?.billing_address.billing_country ?? "",
        shippingAddress:
          cart?.customer?.shipping_address.shipping_address_1 ?? "",
        shippingPostalCode:
          cart?.customer?.shipping_address.shipping_postcode ?? "",
        shippingCity: cart?.customer?.shipping_address.shipping_city ?? "",
        shippingCountry:
          cart?.customer?.shipping_address.shipping_country ?? "DE",
      };
    },
  });

  const { isSubmitting } = methods.formState;

  const onSubmit = async (data: any) => {
    try {
      /** @notice we need to fetch the shippingMethods again since [WooCommerce's API requires providing shipping_lines](https://woocommerce.github.io/woocommerce-rest-api-docs/#create-an-order) providing  to create a new order.
       *
       * `useFormContext's` only stores the methodId (storing the entire paymentMethodObject (instead of only the methodId) will break the `onChange` handler for radioGroup)
       *
       * see `ShippingRadio-Group.tsx` component
       */

      const shippingLines: FetchedShippingMethod = SS_getShippingMethod();
      //?1 ----------------------- BEGIN CREATE WOOCOMMERCE_ORDER ----------------------- //

      // ? reduce the taxes into one number
      const orderDataPayload = {
        // TODO This needs to be done in CoCart
        payment_method_title: "Stripe checkout",
        set_paid: false,
        coupon_lines: cart?.coupons?.map((coupon: any) => {
          return { code: coupon.coupon }; // TODO Check if this is a String. It should be a String
        }),
        billing: {
          first_name: data?.firstName,
          last_name: data?.lastName,
          email: data?.email,
          address_1: data?.billingAddress,
          postcode: data?.billingPostalCode,
          city: data?.billingCity,
          country: data?.billingCountry,
        },
        shipping: {
          first_name: data?.firstName,
          last_name: data?.lastName,
          email: data?.email,
          address_1: data?.shippingSameAsBilling
            ? data?.billingAddress
            : data?.shippingAddress,
          postcode: data?.shippingSameAsBilling
            ? data?.billingPostalCode
            : data?.shippingPostalCode,
          city: data?.shippingSameAsBilling
            ? data?.billingCity
            : data?.shippingCity,
          country: data?.shippingSameAsBilling
            ? data?.billingCountry
            : data?.shippingCountry,
        },
        line_items: cart?.items.map((item) => {
          const reducedItem = {
            quantity: item.quantity.value,
            product_id: item.meta.variation?.parent_id ?? item.id,
            variation_id: item.meta.variation?.parent_id ? item.id : undefined,
          };
          if (!reducedItem.variation_id) delete reducedItem.variation_id;
          return reducedItem;
        }),
        shipping_lines: [
          {
            method_id: shippingLines.methodId,
            method_title: shippingLines.name,
            total: convertPriceToWoocommerceOrder({
              price: `${shippingLines.price}`,
              taxes: shippingLines.taxes,
            }),
          },
        ],
      };

      if (!wooCommerceOrder) {
        const wooOrder = await fetch("/create-order", {
          method: "POST",
          mode: "cors",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            orderDataPayload,
            intentId: clientSecret.id,
          }),
        });
        const wooOrderRes = await wooOrder.json();
        if (!wooOrderRes.orderIsCreated)
          throw new Error("failed to create wooCommerce Order");

        setWooCommerceOrder(wooOrderRes);
      }

      //?1 ----------------------- END CREATE WOOCOMMERCE_ORDER ----------------------- //

      //? ----------------------- BEGIN CONFIRM STRIPE_PAYMENT ----------------------- //
      const confirmPaymentResponse =
        stripe &&
        (await stripe.confirmPayment({
          elements,
          confirmParams: {
            return_url: `https://${process.env.NEXT_PUBLIC_BASE_URL}/checkout-complete`,
            // return_url: "https://example.com/success",
            // return_url: "https://temp-plaze.vercel.app/checkout-complete",
          },
        }));

      if (confirmPaymentResponse?.error) {
        throw Error(confirmPaymentResponse?.error.message);
      } else {
        // clear process residues
        sessionStorage.removeItem(STRIPE.paymentIntent);
        // TODO clear user's cart upon completing checkout
      }

      //? ----------------------- END CONFIRM STRIPE_PAYMENT ----------------------- //
    } catch (error) {
      toast({
        duration: 1000,
        title: "Fehler",
        description: "etwas ist schief gelaufen",
        variant: "destructive",
      });
    }
  };

  return (
    <FormProvider {...methods}>
      <div className="flex min-h-screen flex-col pb-4">
        <div className="bg-background px-4 pt-4 md:px-8 md:pt-8">
          {loading ? (
            <div className="flex h-[70dvh] w-full items-center justify-center align-middle">
              <Spinner />
            </div>
          ) : (
            <Card className="mx-auto min-h-[70dvh] max-w-7xl">
              <div className="w-full">
                <Progress
                  className="h-3 rounded-none"
                  value={(currentStep / stepsCount) * 100}
                />
              </div>

              <div className="flex-1 overflow-auto">
                <div className="mx-auto lg:py-8">
                  <form
                    id="checkout-form"
                    onSubmit={
                      currentStep === stepsCount
                        ? methods.handleSubmit(onSubmit)
                        : undefined
                    }
                  >
                    {isSubmitting && (
                      <div className="flex h-[70dvh] w-full items-center justify-center gap-x-2 align-middle">
                        <div>
                          <Spinner />
                        </div>
                        <p>wird gesendet, bitte warten…</p>
                      </div>
                    )}
                    <PlacePayment
                      shouldShow={!isSubmitting && currentStep === 2}
                    />
                    {(() => {
                      switch (currentStep) {
                        case 0:
                          return <CheckoutPersonalInformation />;
                        case 1:
                          return <CheckoutPaymentDetails />;
                        case 2:
                          return (
                            <div className={isSubmitting ? "hidden" : ""}>
                              <CheckoutOverview />
                            </div>
                          );

                        default:
                          return <div>err</div>;
                      }
                    })()}
                  </form>
                </div>
              </div>
              {!isSubmitting && (
                <CardFooter className="flex flex-wrap justify-end gap-2">
                  {currentStep > 0 && (
                    <Button
                      id="checkout-form-back-button"
                      disabled={isSubmitting}
                      onClick={() => {
                        setCurrentStep((prev) => (prev -= 1));
                      }}
                      type="button"
                      variant="outline"
                    >
                      Zurück
                    </Button>
                  )}
                  {currentStep < stepsCount ? (
                    <Button
                      type="button"
                      id="checkout-form-next-button"
                      onClick={async () => {
                        const isValidStep = await methods.trigger();
                        if (isValidStep) {
                          setCurrentStep(
                            (prev) => (prev += prev < stepsCount ? 1 : 0)
                          );
                        }
                      }}
                    >
                      Weiter
                    </Button>
                  ) : (
                    <Button
                      disabled={isSubmitting}
                      onClick={methods.handleSubmit(onSubmit)}
                      type="submit"
                    >
                      Kostenpflichtig Bestellen
                    </Button>
                  )}
                </CardFooter>
              )}
            </Card>
          )}
        </div>
      </div>
    </FormProvider>
  );
}
