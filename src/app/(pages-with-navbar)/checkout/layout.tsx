"use client";
import { STRIPE } from "@constants/sessionConstants";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { useRouter } from "next/navigation";
import { FunctionComponent, useEffect, useMemo, useState } from "react";

// Make sure to call loadStripe outside of a component’s render to avoid
// recreating the Stripe object on every render.
// This is your test publishable API key.
const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY as string,
  { locale: "de" }
);
const CheckoutLayout: FunctionComponent<{
  children: React.ReactNode;
}> = ({ children }) => {
  const router = useRouter();
  const [clientSecret, setClientSecret] = useState<string | undefined>(
    undefined
  );

  useEffect(() => {
    if (typeof window !== "undefined") {
      const paymentIntent =
        sessionStorage.getItem(STRIPE.paymentIntent) &&
        JSON.parse(sessionStorage.getItem(STRIPE.paymentIntent) as string);
      if (paymentIntent?.client_secret) {
        setClientSecret(paymentIntent.client_secret);
      } else {
        router.push("/cart");
      }
    }
  }, [router]);

  const elementsOptions = useMemo(() => {
    const appearance = {
      theme: "stripe" as "stripe",
      variables: {
        colorPrimary: "#ff7300",
        colorText: "#111113",
      },
    };
    return {
      clientSecret: clientSecret,
      appearance,
    };
  }, [clientSecret]);

  if (!clientSecret) {
    return null;
  } else {
    return (
      <Elements options={elementsOptions} stripe={stripePromise}>
        {children}
      </Elements>
    );
  }
};

export default CheckoutLayout;
