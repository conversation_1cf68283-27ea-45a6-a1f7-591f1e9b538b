import "../../algolia.css";

import { BrandsIndex } from "@components/brands-directory/BrandsIndex";
import { BrandsList } from "@components/brands-directory/BrandsList";
import ScrollToTopButton from "@components/general/ScrollToTopButton";
import BrandsSlider from "@components/homepage/BrandsSlider";
import { BrandData, formatBrands } from "@lib/utilities/brandsHelpers";
import { Metadata } from "next";
//TODO dynamic meta  data
export const metadata: Metadata = {
  title: "Alle Marken - Plaze Shop",
  description:
    "Shoes, Skate & Streatwear - Entdecke alle Marken im Plaze Shop.",
};

export const revalidate = 86400;

export default async function BrandsHome() {
  const brandsData = await BrandData();
  return (
    <div className="mx-auto flex h-fit w-10/12 flex-col items-center pb-8 text-2xl">
      <h1 className="font-medium lg:mt-32">Unsere Brands</h1>
      <BrandsSlider data={brandsData.filter((brand) => !!brand?.imageURL)} />
      <BrandsIndex className="mb-32 mt-0" brands={formatBrands(brandsData)} />
      <BrandsList brandsGroup={formatBrands(brandsData)} />
      <ScrollToTopButton />
    </div>
  );
}
