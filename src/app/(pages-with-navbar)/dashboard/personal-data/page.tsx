"use client";
import EditPasswordDash from "@components/dashboard/EditPasswordDash";
import EditPersonalDataForm from "@components/dashboard/EditPersonalDataForm";
import InfoCard from "@components/dashboard/InfoCard";
import { LoadingSkeleton } from "@components/general/Loading";
import { useToast } from "@components/ui/use-toast";
import { useSessionContext } from "@contexts/userSessionProvider";
import { sendEmail } from "@lib/restAPI/helpers/sendEmailHelper";
import { FC } from "react";

import { useConfirm } from "../../../../hooks/dialogService";
import { GetDataDocument } from "../../../../hooks/graphql/queries/personalDataHooks";

const PersonalDataPage: FC = () => {
  const { data, loading } = GetDataDocument();
  const { toast } = useToast();
  const confirm = useConfirm();
  const {
    state: { customer: customerState },
  } = useSessionContext();

  const deleteUserHandler = async () => {
    const userResponse = await confirm({
      title: "Bist du sicher ?",
      body: "<PERSON><PERSON> <PERSON><PERSON> Benutzer löschen, wird alles in Ihrem Warenkorb und in Ihrer Wunschliste gelöscht",
      actionButton: "Ja",
      cancelButton: "Nein",
    });
    if (!userResponse) {
      return;
    }
    try {
      const response = await sendEmail(
        {
          subject: "Delete User",
          customerName: customerState?.displayName,
          email: customerState?.email,
        },
        "ADMIN"
      );
      if (response && response.code === 200) {
        toast({
          duration: 3500,
          title: "Erfolg",
          description:
            "Wir haben Ihre Anfrage erhalten und werden uns so schnell wie möglich mit Ihnen in Verbindung setzen",
          variant: "success",
        });
      }
    } catch (e) {
      toast({
        duration: 2000,
        title: "fehlgeschlagen",
        variant: "destructive",
        description: "Ihre Anfrage konnte nicht bearbeitet werden",
      });
    }

    return;
  };
  const customer = data?.customer;
  return loading ? (
    <LoadingSkeleton />
  ) : (
    <div className="w-full lg:ml-16  lg:flex xl:ml-24 xl:max-w-4xl">
      <div className="xl:w-7/11 h-full overflow-hidden p-2 lg:w-3/4 lg:min-w-[450px] xl:max-w-[500px]">
        {/* -----------------------Start Contact info View { ------------------------*/}
        <InfoCard
          viewHeader="Kontakt"
          editHeader="Kontakt bearbeiten"
          editButtonLabel="bearbeiten"
          viewButtonLabel="abbrechen"
          EditForm={EditPersonalDataForm}
          formData={customer}
        >
          <div className="font-thin">
            <p>{customer?.firstName}</p>
            <p>{customer?.lastName}</p>
            <p>{customer?.email}</p>
            <p>{customer?.billing?.phone}</p>
          </div>
        </InfoCard>
        {/* -----------------------} End Contact info View ------------------------*/}

        {/* ------------------------Start Password Info View {-------------------- */}
        <InfoCard
          viewHeader="Passwort"
          editHeader="Passwort bearbeiten"
          editButtonLabel="bearbeiten"
          viewButtonLabel="abbrechen"
          EditForm={EditPasswordDash}
        >
          <div>
            <p>*************</p>
          </div>
        </InfoCard>
        {/* ------------------------} End Password Info View -------------------- */}

        {/* ------------------------Start Password Info View {-------------------- */}
        <InfoCard
          viewHeader="Konto"
          deleteButtonLabel="löschen"
          isLastStep={true}
          deleteHandler={deleteUserHandler}
        ></InfoCard>
        {/* ------------------------} End Password Info View -------------------- */}
      </div>
    </div>
  );
};

export default PersonalDataPage;
