"use client";
import SideNavigation from "@components/dashboard/SideNavigation";
import Spinner from "@components/general/Spinner";
import dynamic from "next/dynamic";
import { useEffect } from "react";

import { useGetCustomerData } from "../../../hooks/graphql/queries/customerHooks";

/**
 * @description  the dashboard page is responsible for managing all user-related data
 * including their orders (purchases), personal data, shipping information, logout button, and more...
 * @Rendering Client-side rendering since this page requires user data after login (it is a protected route)
 */

const DashboardLayout = ({ children }: { children: React.ReactNode }) => {
  const {
    setupUser,
    state: { loading },
  } = useGetCustomerData();
  useEffect(() => {
    const getUser = async () => {
      await setupUser();
    };
    getUser();
  }, [setupUser]);

  return (
    <div className=" h-fit  w-full flex-col items-start gap-y-8 pb-20 pt-20 md:px-36 lg:pb-40">
      {loading ? (
        <div className="flex w-full  flex-grow items-center ">
          <Spinner />
        </div>
      ) : (
        <div className="flex w-full flex-col-reverse   px-4 lg:flex-row lg:px-0">
          <SideNavigation />
          <section className="flex-grow pt-16">{children}</section>
        </div>
      )}
    </div>
  );
};

/**
 *  @description to prevent partial server-side pre-rendering
 * @see https://nextjs.org/docs/app/building-your-application/optimizing/lazy-loading#adding-a-custom-loading-component
 */
const noSSRDashboardLayout = dynamic(() => Promise.resolve(DashboardLayout), {
  ssr: false,
});

export default noSSRDashboardLayout;
