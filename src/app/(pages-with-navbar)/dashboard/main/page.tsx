"use client";
import {
  BestellungenIcon,
  ContactIcon,
  FAQIcon,
  FormularIcon,
  LocationPinIcon,
} from "@components/Icons/dashboardIcons.index";
import { Icon } from "@components/ui/Icon";
import Link from "next/link";
import { FC } from "react";

type dashboardMainEntryItem = {
  icon: typeof FormularIcon;
  header: string;
  description: string;
  route: string;
};

// These routes should be open in a new window
const externalWindow = {
  faq: "/faq",
  contact: "/contact",
};

const entries: Array<dashboardMainEntryItem> = [
  {
    header: "Persönliche Daten",
    description: "Kontakt und Passwort",
    icon: FormularIcon,
    route: "personal-data",
  },

  {
    header: "Adressen",
    description: "Liefer- und Rechnungsadressen",
    icon: LocationPinIcon,
    route: "address",
  },
  {
    header: "Alle Bestellungen",
    description: "Ihre Einkäufe auf einen Blick",
    icon: BestellungenIcon,
    route: "orders",
  },

  {
    header: "Kontakt",
    description: "Wie können wir dir weiterhelfen?",
    icon: ContactIcon,
    route: externalWindow.contact,
  },
  {
    header: "FAQ",
    description: "Häufig gestellte Fragen",
    icon: FAQIcon,
    route: externalWindow.faq,
  },
];
const DashboardPage: FC = () => {
  return (
    <ul className="flex w-full  flex-wrap justify-center gap-5  md:flex-grow lg:mx-[10%] lg:justify-start">
      {entries.map((entry) => {
        const ItemIcon = entry.icon;
        return (
          <li key={entry.route} className="contents" tabIndex={1}>
            <Link
              target={
                Object.values(externalWindow).includes(entry.route)
                  ? "_blank"
                  : ""
              }
              title={`${entry.header}: ${entry.description}`}
              href={entry.route}
              className="mx-auto flex min-h-[9rem] min-w-[26rem] flex-col items-center justify-center bg-gray-bright p-4 filter transition-all hover:brightness-95 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 max-md:w-full md:last:mx-0 lg:w-1/5"
            >
              <Icon size={"medium"}>
                <ItemIcon className="h-full w-full" />
              </Icon>
              <h2 className="text-center text-xl font-light">{entry.header}</h2>
              <p className="text-center text-sm font-light ">
                {entry.description}
              </p>
            </Link>
          </li>
        );
      })}
    </ul>
  );
};

export default DashboardPage;
