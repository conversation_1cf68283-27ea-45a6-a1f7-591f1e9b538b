"use client";
import EditBillingAddressForm from "@components/dashboard/EditBillingAddressForm";
import EditShippingAddressForm from "@components/dashboard/EditShippingAddressForm";
import InfoCard from "@components/dashboard/InfoCard";
import { LoadingSkeleton } from "@components/general/Loading";
import { FC } from "react";

import {
  useDeleteBillingAddress,
  useDeleteShippingAddress,
} from "../../../../hooks/graphql/mutations/addressDashboardHooks";
import { useGetAddressData } from "../../../../hooks/graphql/queries/getAddressHooks";

const getAddress = ({
  name,
  address,
}: {
  name: string | null | undefined;
  address: string | null | undefined;
}) => {
  return (
    <div className="font-thin">
      <p>{name}</p>
      <p>{address}</p>
    </div>
  );
};

const AddressPage: FC = () => {
  const { data, loading } = useGetAddressData();
  const billing = data?.customer?.billing;
  const billingFirstName = billing?.firstName;
  const billingLastName = billing?.lastName;
  const billingAddress = billing?.address1;

  const shipping = data?.customer?.shipping;
  const shippingFirstName = shipping?.firstName;
  const shippingLastName = shipping?.lastName;
  const shippingAddress = shipping?.address1;

  const { DeleteBillingAddress } = useDeleteBillingAddress();
  const { DeleteShippingAddress } = useDeleteShippingAddress();
  const handleAddressDelete =
    (target: "Lieferadresse" | "Rechnungsadresse", deletAddress: any) =>
    async () => {
      await deletAddress({
        variables: {
          firstName: "",
          lastName: "",
          address1: "",
          postcode: "",
          city: "",
        },
      });
    };
  return loading ? (
    <LoadingSkeleton />
  ) : (
    <div className="w-full lg:ml-16  lg:flex xl:ml-28 xl:max-w-4xl">
      <div className="xl:w-7/11 h-full overflow-hidden p-2 lg:w-3/4 lg:min-w-[450px] xl:max-w-[500px]">
        <InfoCard
          viewHeader="Lieferadresse"
          editHeader="Lieferadresse bearbeiten"
          editButtonLabel="bearbeiten"
          deleteButtonLabel={
            billingFirstName && billingAddress ? "löschen" : undefined
          }
          addButtonLabel={
            !(billingFirstName && billingAddress) ? "hinzufügen" : undefined
          }
          viewButtonLabel="Abbrechen"
          EditForm={EditBillingAddressForm}
          formData={data?.customer?.billing}
          deleteHandler={handleAddressDelete(
            "Lieferadresse",
            DeleteBillingAddress
          )}
        >
          {billingFirstName && billingAddress ? (
            getAddress({
              name: `${billingFirstName} ${billingLastName}`,
              address: billingAddress,
            })
          ) : (
            <p className="text-gray-middle"> Keine Adresse festgelegt</p>
          )}
        </InfoCard>

        <InfoCard
          viewHeader="Rechnungsadresse"
          editHeader="Rechnungsadresse bearbeiten"
          editButtonLabel="bearbeiten"
          deleteButtonLabel={
            shippingFirstName && shippingAddress ? "löschen" : undefined
          }
          addButtonLabel={
            !(shippingFirstName && shippingAddress) ? "hinzufügen" : undefined
          }
          viewButtonLabel="Abbrechen"
          EditForm={EditShippingAddressForm}
          formData={data?.customer?.shipping}
          deleteHandler={handleAddressDelete(
            "Rechnungsadresse",
            DeleteShippingAddress
          )}
        >
          {shippingFirstName && shippingAddress ? (
            getAddress({
              name: `${shippingFirstName} ${shippingLastName}`,
              address: shippingAddress,
            })
          ) : (
            <p className="text-gray-middle"> Keine Adresse festgelegt</p>
          )}
        </InfoCard>
      </div>
    </div>
  );
};

export default AddressPage;
