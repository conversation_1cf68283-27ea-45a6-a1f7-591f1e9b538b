"use client";

import "../../../globals.css";

import { LoadingSkeleton } from "@components/general/Loading";
import dynamic from "next/dynamic";
import { FC } from "react";

import { GetOrder } from "../../../../hooks/graphql/queries/orderHooks";
type dataField = {
  id: string;
  description: string;
};
const formattedDate = (date: Date) => {
  return `${date.getDate().toString().padStart(2, "0")}.${(date.getMonth() + 1)
    .toString()
    .padStart(2, "0")}.${date.getFullYear()}`;
};
const tableFieldsName: {
  fields: dataField[];
} = {
  fields: [
    {
      id: "bestelldatum",
      description: "Bestelldatum",
    },
    {
      id: "gesamtwert",
      description: "Gesamtwert",
    },
    {
      id: "rechnungsnummer",
      description: "Rechnungsnummer",
    },
    {
      id: "bestellung_tracken",
      description: "Bestellung tracken",
    },
  ],
};

export const OrdersPage: FC = () => {
  const { data, loading } = GetOrder();
  const orders = data?.orders?.nodes.map((order: any) => ({
    id: order.id,
    bestelldatum: formattedDate(new Date(order.date)),
    gesamtwert: `${Number(order.invoices[0]?.product_total).toFixed(2)}$`,
    rechnungsnummer: order.invoices[0]?.order_id,
    bestellung_tracken: order.shipments[0]?.trackingUrl,
  }));
  return loading ? (
    <LoadingSkeleton />
  ) : orders?.length ? (
    <div className="w-auto max-w-[900px] overflow-auto lg:mx-36">
      <table className="w-full overflow-auto border-b-2">
        <thead className="text-left">
          <tr role="row" className="border-b-2">
            {tableFieldsName.fields.map((item) => (
              <th
                className="px-4 py-2 max-md:hidden max-sm:hidden"
                key={item.id}
              >
                {item.description}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {orders?.map((order: any, index: any) => {
            return (
              <tr className="border-b-2" key={index}>
                {tableFieldsName.fields.map((field) => (
                  <td
                    key={field.id}
                    className={`px-4 py-6 text-left max-md:grid max-md:px-0 max-md:py-2 max-md:font-light max-md:data-th max-sm:grid max-sm:px-0 max-sm:py-2 max-sm:font-light max-sm:data-th ${
                      field.id === "bestellung_tracken" ? "underline" : ""
                    }`}
                    data-th={field.description}
                  >
                    {order[field.id]}
                  </td>
                ))}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  ) : (
    <div className="w-auto max-w-[900px] overflow-auto lg:mx-36">
      <table className="mt-10 h-16 w-full overflow-auto border-b-2">
        <tr className=" border-t-2">
          <td>
            <p className="my-4">Keine Bestellungen</p>
          </td>
        </tr>
      </table>
    </div>
  );
};
const noSSROrderPage = dynamic(() => Promise.resolve(OrdersPage), {
  ssr: false,
});
export default noSSROrderPage;
