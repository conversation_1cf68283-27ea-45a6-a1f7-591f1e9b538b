import "../../../algolia.css";

//import ProductInformation from "@components/single-product-page/ProductInformation";
//import Recommendations from "@components/single-product-page/Recommendations";
import { EmptyComponent } from "@components/algolia-search/NoResultsFound";
import ScrollToTopButton from "@components/general/ScrollToTopButton";
import DisplayImage from "@components/single-product-page/DisplayImages";
import { Button } from "@components/ui/Button";
import { ChevronLeftIcon } from "@heroicons/react/24/outline";
import { getAllProducts } from "@lib/api";
import {
  formatProductNew,
  formattedProduct,
} from "@lib/utilities/singleProduct";
import { Metadata, ResolvingMetadata } from "next";
import Link from "next/link";
import { Suspense } from "react";

import ProductInformationWrapper, {
  ProductInformationPlaceholder,
} from "./suspended";

interface SingleProductProps {
  params: Promise<{
    _slug: string;
  }>;
}

export async function generateMetadata(
  { params }: SingleProductProps,
  _parent: ResolvingMetadata
): Promise<Metadata> {
  const { _slug } = await params;

  const formattedProduct = await formatProductNew(_slug);

  return {
    title: `${formattedProduct?.name} - Plaze Shop`,
    description: formattedProduct?.description,
  };
}

export const revalidate = 3600;

export const generateStaticParams = async () => {
  try {
    const products = await getAllProducts();
    console.log(`GOT ${products?.length} products`);
    return products?.map((prod: any) => ({
      _slug: prod.slug,
    }));
  } catch (error) {
    console.error("Error generating static params:", error);
    return [];
  }
};

// Separate Komponente für Produktinformationen
export function ProductContent({
  formattedProduct,
}: {
  formattedProduct: formattedProduct;
}) {
  const jsonld = {
    "@context": "https://schema.org/",
    "@type": "Product",
    name: formattedProduct.name,
    image: formattedProduct.galleryImages[0],
    description: formattedProduct.description,
    brand: formattedProduct.brands[0]?.name
      ? {
          "@type": "Brand",
          name: formattedProduct.brands[0].name,
        }
      : undefined,
    offers: {
      "@type": "Offer",
      priceCurrency: "EUR",
      price: formattedProduct.price,
      availability: "https://schema.org/InStock",
      itemCondition: "https://schema.org/NewCondition",
    },
  };
  return (
    <div className="grid h-full w-full grid-cols-1 md:m-auto md:mt-6 md:grid md:h-[75%] md:w-[75%] md:grid-cols-3">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonld) }}
      />
      <Suspense
        fallback={
          <ProductInformationPlaceholder formattedProduct={formattedProduct} />
        }
      >
        <ProductInformationWrapper formattedProduct={formattedProduct} />
      </Suspense>

      <div className="row-start-1 row-end-2 md:col-start-1 md:col-end-3 md:row-start-1 md:mx-10 md:grid">
        <DisplayImage formattedProduct={formattedProduct} />
      </div>
      <ScrollToTopButton />
    </div>
  );
}

const SingleProduct = async ({ params }: SingleProductProps) => {
  const { _slug } = await params;

  //const formattedProduct = await formatProduct(_slug);
  const formattedProduct = await formatProductNew(_slug);

  if (!formattedProduct) {
    return (
      <div>
        <Link href="/products" className="m-10">
          <Button className="m-10 h-auto w-auto">
            <ChevronLeftIcon className="h-4 w-4" />
            zurück
          </Button>
        </Link>
        <EmptyComponent />
      </div>
    );
  }

  return <ProductContent formattedProduct={formattedProduct} />;
};

export default SingleProduct;
