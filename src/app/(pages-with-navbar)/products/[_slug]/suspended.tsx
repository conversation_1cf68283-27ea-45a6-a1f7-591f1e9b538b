import Spinner from "@components/general/Spinner";
import ProductInformation from "@components/single-product-page/ProductInformation";
import AccordionDescription from "@components/ui/AccordionDescription";
import { Button } from "@components/ui/Button";
import {
  formattedProduct,
  FormatVariationsType,
  newFormatVariations,
} from "@lib/utilities/singleProduct";
import { cn, getPriceFormatter } from "@lib/utils";
import { ChevronRightIcon } from "lucide-react";
import React from "react";

const ProductInformationWrapper = async ({
  formattedProduct,
}: {
  formattedProduct: formattedProduct;
}) => {
  let variations: FormatVariationsType = [];
  if (formattedProduct.variations) {
    variations = (await newFormatVariations(formattedProduct.id)) ?? [];
  }

  return (
    <ProductInformation
      formattedProduct={formattedProduct}
      variations={variations}
    />
  );
};

export const ProductInformationPlaceholder = ({
  formattedProduct,
}: {
  formattedProduct: formattedProduct;
}) => {
  const { deutschFormatter } = getPriceFormatter(true);
  return (
    <div>
      <div className="mx-4 my-2 lg:col-start-3">
        <div className="grid">
          <div className="col-start-1">
            <p className="text-wrap text-xl font-bold">
              {formattedProduct?.name}
            </p>
            <div className="mb-6 text-2xl">
              {formattedProduct.regularPrice === formattedProduct.price ||
              !formattedProduct.regularPrice ||
              !formattedProduct.salePrice ? (
                <div className="flex h-10 w-fit flex-wrap justify-center overflow-hidden px-3 py-2 ">
                  <p className=" font-medium">
                    {deutschFormatter(formattedProduct.price)}
                  </p>
                </div>
              ) : (
                <div className="  flex h-10 w-fit flex-wrap  justify-center overflow-hidden px-3 py-2 ">
                  <p className=" mr-2 line-through">
                    {deutschFormatter(formattedProduct.regularPrice)}
                  </p>
                  <p className="font-medium text-destructive ">
                    {deutschFormatter(formattedProduct.price)}
                  </p>
                </div>
              )}
            </div>
            <p className="text-xs font-thin">inkl. MwSt. zzgl. Versandkosten</p>
          </div>
        </div>

        <p className="my-6 text-xs text-success">
          An Werktagen wird die Bestellung bis 16 Uhr noch am selben Tag
          verschickt.
        </p>
        {formattedProduct.variations && formattedProduct.variations > 0 && (
          <div className="flex w-full items-start justify-between">
            <p className="text-xs">
              {!formattedProduct.dropItem
                ? "Wähle deine Größe"
                : "Für den Drop anmelden"}
            </p>
            <div className="h-auto w-auto">
              {formattedProduct.productCategories
                .map((cat: string) => cat.toLowerCase())
                .includes("shoes") && (
                <div className="flex w-auto items-center justify-end text-xs text-primary">
                  <ChevronRightIcon className="w-4" /> Lädt...
                </div>
              )}
            </div>
          </div>
        )}

        {!formattedProduct.dropItem ? (
          <>
            <div className="w-full">
              <div>
                <div className="hidden md:grid md:grid-cols-3 md:gap-1 lg:grid lg:grid-cols-3 lg:gap-1">
                  {formattedProduct.variations &&
                    formattedProduct.variations > 0 &&
                    Array(formattedProduct.variations)
                      .fill(null)
                      .map((_, index) => {
                        return (
                          <div key={index} className="my-1 grid w-full">
                            <Button
                              disabled
                              className={`w-full border-2 border-black px-2 py-1 text-center text-xs ${"cursor-not-allowed border-gray-400 bg-gray-200 text-gray-400"}`}
                            >
                              Lädt...
                            </Button>
                          </div>
                        );
                      })}
                </div>
                <div className="my-6 md:hidden">
                  <div className="relative">
                    <select
                      className="h-11 w-full border border-black px-4 py-2 shadow-sm focus:border-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-600"
                      placeholder={"Lädt..."}
                    >
                      <option className="hover:bg-black!">Lädt...</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div className="w-full">
              <div className="w-full">
                <Button
                  disabled
                  className={cn(
                    "group relative h-16 w-full bg-primary hover:bg-primary"
                  )}
                >
                  <Spinner />
                  <ChevronRightIcon className="absolute right-1 h-12 w-12 opacity-0 ease-in-out group-hover:opacity-100" />
                </Button>
              </div>
            </div>
          </>
        ) : (
          <Spinner />
        )}
        <div className="mx-4 my-10">
          <AccordionDescription description={formattedProduct!.description} />
        </div>
      </div>
    </div>
  );
};

export default ProductInformationWrapper;
