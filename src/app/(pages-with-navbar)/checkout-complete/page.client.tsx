"use client";
import CheckoutFailed from "@components/checkout/CheckoutFailed";
import CheckoutSuccess from "@components/checkout/CheckoutSuccess";
import { SS_clearShoppingMethod } from "@lib/utilities/StripeHelpers/checkoutUtils";
import { useSearchParams } from "next/navigation";
import { useEffect } from "react";

//import { useEmptyCart } from "../../../hooks/graphql/mutations/cartHooks";

const CheckoutCompleteClient = () => {
  const searchParams = useSearchParams();
  const redirect_status = searchParams.get("redirect_status");
  useEffect(() => {
    SS_clearShoppingMethod();
  }, []);
  if (redirect_status === "succeeded") {
    return <CheckoutSuccess />;
  }
  return <CheckoutFailed />;
};

export default CheckoutCompleteClient;
