"use client";
import { CustomStats } from "@components/general/ProductPage";
import ScrollToTopButton from "@components/general/ScrollToTopButton";
import { searchClient } from "@lib/typesense/typesenseClient";
import { singleIndex } from "instantsearch.js/es/lib/stateMappings";
import React from "react";
import { Configure, Stats } from "react-instantsearch";
import { InstantSearchNext } from "react-instantsearch-nextjs";

export const SalePageClient = () => {
  return (
    <InstantSearchNext
      indexName="product_sale"
      searchClient={searchClient}
      future={{ preserveSharedStateOnUnmount: true }}
      routing={{ stateMapping: singleIndex("product_sale") }}
    >
      <Configure hitsPerPage={24} />
      <div className="flex grid-cols-[1fr] flex-col gap-5 bg-background max-sm:items-center lg:grid xl:grid-cols-[1fr] 2xl:grid-cols-[1fr]">
        <div className=" mt-5 ">
          <div className="hidden items-center justify-between lg:flex">
            <div className="bg-background px-2 pb-[10px] max-sm:hidden  ">
              <h1 className="mt-5 w-fit ">
                <Stats
                  translations={{
                    rootElementText: ({ nbHits }) => `${nbHits} Artikel`,
                  }}
                />
              </h1>
            </div>
          </div>

          <div className=" mb-8 flex justify-between max-md:justify-center">
            <CustomStats />
          </div>
        </div>
      </div>

      <ScrollToTopButton />
    </InstantSearchNext>
  );
};
