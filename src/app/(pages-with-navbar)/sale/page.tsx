import "instantsearch.css/themes/satellite.css";

import Spinner from "@components/general/Spinner";
import { Metadata } from "next";
import React, { Suspense } from "react";

import { SalePageClient } from "./page.client";

export const metadata: Metadata = {
  title: "Sale Artikel - Plaze Shop",
  description: "Kaufe reduzierte Artikel aus dem Plaze Shop.",
};

export const revalidate = 86400;

const ProductsPage = () => {
  return (
    <div>
      <div className="mx-4 md:mx-20 xl:mx-28">
        <Suspense fallback={<Spinner />}>
          <SalePageClient />
        </Suspense>
      </div>
    </div>
  );
};

export default ProductsPage;
