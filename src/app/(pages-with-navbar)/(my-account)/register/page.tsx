import { RegistrationForm } from "@components/registration/RegistrationForm";
import photo from "@public/tom-morbey-5jyEONpv0qA-unsplash.png";
import type { Metadata } from "next";
import Image from "next/image";

export const metadata: Metadata = {
  title: "Plaze - Registrierungsseite",
  description: "Erstellen Sie ein Konto auf Plaze ein",
};
export default function registrationPage() {
  return (
    <div className="flex flex-row">
      <Image
        src={photo}
        alt="A black and white image of a person jumping over their skateboard"
        className=" flex h-full  w-1/2 max-lg:hidden"
        width={0}
        height={0}
        sizes="50vw"
        priority
      />
      <div className=" flex flex-col max-sm:ml-3 max-sm:mr-5 max-sm:w-full sm:ml-12 sm:translate-x-44 lg:-translate-x-1">
        <div className="  max-sm:mt-7 sm:mt-10 ">
          <h1 className=" text-2xl font-normal">Willkommen in unserem PLAZE</h1>
          <p className="mb-3 mt-8 font-normal uppercase">
            kundenkonto erstellen
          </p>
        </div>
        <RegistrationForm />
      </div>
    </div>
  );
}
