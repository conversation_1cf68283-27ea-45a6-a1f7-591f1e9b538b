//import { LoginForm } from "@components/login/LoginForm";
import photo from "@public/tom-morbey-5jyEONpv0qA-unsplash.png";
import type { Metadata } from "next";
import Image from "next/image";

export const metadata: Metadata = {
  title: "Plaze - Einloggen",
  description: "Loggen Sie sich in Ihr Konto auf Plaze ein",
};
export default function loginPage() {
  return (
    <div className="flex flex-row ">
      <Image
        src={photo}
        alt="A black and white image of a person jumping over their skateboard"
        className="flex h-full w-1/2 max-lg:hidden"
        width={0}
        height={0}
        sizes="50vw"
        priority
      />

      <div className=" flex flex-col max-sm:ml-3 max-sm:mr-5 max-sm:w-full sm:ml-12 sm:translate-x-44 lg:-translate-x-1">
        <div className="max-sm:mt-7 sm:mt-10 ">
          <h1 className="text-2xl font-normal">Will<PERSON>mmen in unserem PLAZE</h1>
          {/*}
          <p className="mb-3 mt-8 font-normal uppercase">anmelden</p>
          {*/}
        </div>

        <h2 className="mt-4 text-xl">
          Der Benutzerbereich ist temporär deaktiviert.
        </h2>
        {/*}
        <LoginForm className="flex flex-col " />
        {*/}
      </div>
    </div>
  );
}
