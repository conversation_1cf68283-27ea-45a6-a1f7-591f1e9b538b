"use client";

import Spinner from "@components/general/Spinner";
import { ShoppingCartList } from "@components/shoppingCart/ShoppingCartList";
import ShoppingCartDescriptionCard from "@components/shoppingCart/ShoppingCartSideCard";
import { ShoppingCartTable } from "@components/shoppingCart/ShoppingCartTable";
import { Button } from "@components/ui/Button";
import { Icon } from "@components/ui/Icon";
import { ChevronLeftIcon } from "@heroicons/react/24/outline";
import dynamic from "next/dynamic";
import Link from "next/link";

import { useUserStore } from "../../../state/user";

export const CartPage = () => {
  const loading = useUserStore((data) => data.loading);
  const cart = useUserStore((data) => data.cart);

  const doesCartContainProducts = cart?.item_count ?? 0 > 0;

  return (
    <div className="relative flex h-full min-h-[80dvh] w-full max-w-full flex-col gap-y-5  px-4 pb-12 pt-6 md:px-16 lg:px-36 ">
      <h2 className="text-4xl font-bold">Warenkorb</h2>
      {loading ? (
        <div className="flex h-full w-full items-center justify-center ">
          <Spinner />
        </div>
      ) : doesCartContainProducts ? (
        <>
          <Link href={"/products"}>
            <Button
              variant={"link"}
              size={"sm"}
              className="p-0 font-light text-black"
            >
              <Icon size={"small"} className="w-3 ">
                <ChevronLeftIcon />
              </Icon>
              Weitershoppen
            </Button>
          </Link>
          <div className="mx-auto flex h-full min-w-full max-w-full flex-row flex-wrap items-start justify-end gap-2 md:flex-row xl:flex-nowrap">
            <ShoppingCartList />
            <ShoppingCartTable />
            <ShoppingCartDescriptionCard />
          </div>
        </>
      ) : (
        <p className="m-auto w-full text-center">
          Es wurden keine Artikel im Warenkorb gefunden.
          <Button variant={"link"}>
            <Link href={"/products"}> geh jetzt einkaufen!</Link>
          </Button>
        </p>
      )}
    </div>
  );
};

const noSSRShoppingCartPage = dynamic(() => Promise.resolve(CartPage), {
  ssr: false,
});

export default noSSRShoppingCartPage;
