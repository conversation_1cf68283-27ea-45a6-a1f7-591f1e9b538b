import { Metadata } from "next";
import Link from "next/link";
import React from "react";

export const revalidate = 86400;

export const metadata: Metadata = {
  title: "Impressum - Plaze Shop",
  description: "Das Impressum des Plaze Shop aus Chemnitz.",
};
const page = () => {
  return (
    <div className="lg:ml-48">
      <h1 className="mb-5 mt-16 text-xl font-medium">IMPRESSUM</h1>
      <p className="mb-6 font-light">
        Plaze GmbH
        <br />
        Geschäftsführer: <PERSON>, <PERSON><PERSON>
      </p>
      <p className="mb-6 font-light">
        Innere Klosterstraße 13
        <br />
        09111 Chemnitz
      </p>
      <p className="font-light ">Tel.: +49 371 6660081</p>
      <div className="mb-6">
        <Link href={"/"} className="font-light underline">
          E-Mail: <EMAIL>
        </Link>
      </div>
      <p className="mb-6 font-light ">
        Registergericht: Amtsgericht Chemnitz
        <br />
        Registernummer: HRB 19279
      </p>
      <p className="font-light  ">
        Umsatzsteuer-Identifikationsnummer gemäß § 27a UStG: DE218654350
        <br />
        Die EU-Kommission hat eine Internetplattform zur Online-Beilegung von
        Streitigkeiten (OS-Plattform) zwischen Unternehmern und Verbrauchern
        <br />{" "}
      </p>
      <div className="mb-6 flex">
        <p className="font-light">
          eingerichtet. Die OS-Plattform ist erreichbar unter
        </p>
        <Link
          href={
            "https://ec.europa.eu/consumers/odr/main/index.cfm?event=main.home.chooseLanguage"
          }
          className="font-light underline"
        >
          {" "}
          https://ec.europa.eu/consumers/odr/
        </Link>
      </div>
      <p className="mb-32 font-light">
        Wir sind nicht bereit und nicht verpflichtet, an einem
        Streitbeilegungsverfahren vor einer Verbraucherschlichtungsstelle
        teilzunehmen.
      </p>
    </div>
  );
};

export default page;
