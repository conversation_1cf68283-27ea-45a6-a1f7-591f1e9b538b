import "swiper/css";
import "swiper/css/navigation";
import "../algolia.css";

import { HeroGrid, InstagramFeed } from "@components/homepage";
import BrandsSlider from "@components/homepage/BrandsSlider";
import CategorySlider from "@components/homepage/CategorySlider";
import HomePageTextBlock from "@components/homepage/HomePageTextBlock";
import { getHomePage } from "@lib/api";
import { brand, BrandData } from "@lib/utilities/brandsHelpers";
import {
  _processBannerData,
  _processHeroGridData,
  _processTextAreaBlockData,
  processHomePageData,
} from "@lib/utilities/homePageHelpers";

type countedBrand = brand & { count: number };

export const revalidate = 86400;

const HomePage = async () => {
  const response = await getHomePage();
  const data = await processHomePageData(response.blocks);
  const brands = (await BrandData()) as countedBrand[];

  return (
    <div className="flex flex-col gap-24 py-4">
      {response.blocks.map((block, i) => {
        switch (block.name) {
          case "plaze/grid": {
            return <HeroGrid data={_processHeroGridData(block)} key={i} />;
          }
          case "plaze/product-slider": {
            return <CategorySlider key={i} data={block} />;
          }
          case "plaze/instagram-feed": {
            return <InstagramFeed data={data.InstagramFeed} />;
          }
          case "plaze/brand-slider-plugin": {
            return (
              <BrandsSlider
                data={brands
                  .filter((brand) => brand?.imageURL)
                  .sort((brand, current) => current.count - brand.count)}
              />
            );
          }
          case "acf/textareablock": {
            return (
              <HomePageTextBlock
                key={i}
                data={_processTextAreaBlockData(block)}
              />
            );
          }
        }
      })}
    </div>
  );
};

export default HomePage;
