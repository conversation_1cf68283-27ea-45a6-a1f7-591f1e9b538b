import { getAllProducts } from "@lib/api";
import type { MetadataRoute } from "next";

export const revalidate = 86400;

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const domain = "https://plaze-shop.de";

  let productSites: MetadataRoute.Sitemap = [];

  try {
    const products = await getAllProducts();
    productSites =
      products?.map((prod: any) => ({
        url: `${domain}/products/${prod.slug}`,
        lastModified: prod.modified as Date,
        changeFrequency: "daily",
        priority: 0.9,
      })) ?? [];
  } catch (error) {
    //console.log(error);
  }

  const staticSites = [
    {
      url: `${domain}`,
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 1,
    },
    {
      url: `${domain}/products`,
      lastModified: new Date(),
      changeFrequency: "daily",
      priority: 0.9,
    },
    {
      url: `${domain}/sale`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.7,
    },
    {
      url: `${domain}/alle-marken`,
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.5,
    },
    {
      url: `${domain}/contact`,
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 0.3,
    },
    {
      url: `${domain}/login`,
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.3,
    },
    {
      url: `${domain}/cart`,
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.3,
    },
    {
      url: `${domain}/revocation`,
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 0.1,
    },
    {
      url: `${domain}/terms`,
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 0.1,
    },
    {
      url: `${domain}/datenschutz`,
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 0.1,
    },
    {
      url: `${domain}/impressum`,
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 0.1,
    },
  ] satisfies MetadataRoute.Sitemap as MetadataRoute.Sitemap;

  return staticSites.concat(productSites);
}
