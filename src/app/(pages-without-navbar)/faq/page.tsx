"use client";
import { SearchBar } from "@components/faq/SearchBar";
import { LoadingSkeleton } from "@components/general/Loading";
import ScrollToTopButton from "@components/general/ScrollToTopButton";
import { Button } from "@components/ui/Button";
import FaqAccordion from "@components/ui/FaqAccordion";
import FaqMobileAccordion from "@components/ui/FaqMobileAccordion";
import { ArrowLeftIcon, ChevronLeftIcon } from "@heroicons/react/24/outline";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useState } from "react";

import plazeLogo from "../../../../public/assets/plaze_logo.png";
import { GetFaq } from "../../../hooks/graphql/queries/faqHooks";

function FaqPage() {
  const { data, loading } = GetFaq();
  const faqData = data?.pageBy?.blocks[0]?.attributes?.data;
  const [showFaq, setShowFaq] = useState({
    title: "",
    question: "",
    answer: "",
  });
  const [selectedQuestion, setSelectedQuestion] = useState<string>("");
  const [selectedTitle, setSelectedTitle] = useState<string>("");
  const handleFaq = (answer: string, question: string, title: string) => {
    setSelectedQuestion(question);
    setSelectedTitle(title);
    setShowFaq({
      title: title,
      question: question,
      answer: answer,
    });
  };
  useEffect(() => {
    if (faqData) {
      setShowFaq({
        title: faqData.faq_items_0_title,
        question: faqData.faq_items_0_faq_questions_answer_0_question,
        answer: faqData.faq_items_0_faq_questions_answer_0_answer,
      });
    }
  }, [faqData]);

  return loading ? (
    <LoadingSkeleton />
  ) : (
    <div className=" mb-36 ">
      <div className=" flex  h-24  w-full min-w-[10rem] max-sm:flex-row sm:justify-around ">
        <Link href={"/"} className="mt-8  max-sm:mt-16 ">
          <Button className="mt-2 w-52 bg-background text-black hover:bg-primary  sm:border sm:border-black">
            <ArrowLeftIcon className="w-5  -translate-x-2 text-black max-sm:hidden" />
            <ChevronLeftIcon className="w-5  -translate-x-2 text-black sm:hidden sm:-translate-y-2" />
            ZURÜCK ZUM SHOP
          </Button>
        </Link>
        <div className="h-16 max-sm:w-40 md:-translate-x-48 xl:-translate-x-96  ">
          <Image
            src={plazeLogo}
            alt="Plaze Logo"
            width={0}
            height={0}
            priority
            sizes="200px"
            className="h-full w-full"
          />
          <p className="ml-9 -translate-y-5 font-black">Hilfe Center</p>
        </div>
      </div>
      <div className="flex flex-col items-center bg-gray-bright max-sm:mt-5 max-sm:h-36 sm:h-32">
        <p className="mb-2 mt-5 flex justify-center font-black ">
          Wie können wir dir heute weiterhelfen?
        </p>
        <SearchBar data={faqData} handleFaq={handleFaq} />
      </div>
      <div className=" flex  max-sm:flex-col xl:justify-between">
        <FaqMobileAccordion
          handleFaq={handleFaq}
          faqData={faqData}
          selectedQuestion={selectedQuestion}
          selectedTitle={selectedTitle}
        />
        <div className="  h-full max-md:w-1/3 max-sm:hidden sm:ml-16 lg:w-1/4">
          <FaqAccordion
            selectedTitle={selectedTitle}
            handleFaq={handleFaq}
            faqData={faqData}
            selectedQuestion={selectedQuestion}
          />
        </div>
        <div className="mt-12 max-sm:ml-10 sm:ml-20 sm:w-1/2 xl:mr-36">
          <p className="font-bold">{showFaq?.title}</p>
          <p className=" mt-3 font-bold max-sm:w-full">{showFaq?.question}</p>
          <p className="mt-7 max-sm:w-full">{showFaq.answer}</p>
        </div>
      </div>
      <ScrollToTopButton />
    </div>
  );
}

export default FaqPage;
