.ais-Hits-list {
  padding-top: 2rem;
}

.ais-Hits-item {
  background-color: transparent;
  box-shadow: none;
  padding: 1rem;
}

@media screen and (min-width: 1024px) {
  .ais-Hits-item {
    padding: 1.5rem;
  }
}

[data-page="1"] .ais-Hits-list {
  list-style: none;
  background-color: white;
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 1px;
  padding: 0;
}
[data-page="1"].ais-Hits-item {
  box-shadow: none;
  width: 100%;
}

.ais-CurrentRefinements-list {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  gap: 5px;
}

.ais-CurrentRefinements-item {
  padding: 3px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 3px;
  min-width: 40px;
  flex: 0 0 auto;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
}

.ais-HierarchicalMenu {
  font-family: Arial, sans-serif;
}

.ais-HierarchicalMenu-list {
  list-style-type: none;
  padding: 0;
}

.ais-HierarchicalMenu-item {
  padding: 5px 10px;
  width: 400px;
}

.ais-HierarchicalMenu-link {
  color: #333;
  text-decoration: none;
}

.ais-HierarchicalMenu-link:hover {
  color: orange;
}

.ais-HierarchicalMenu-count {
  visibility: hidden;
}

.ais-HierarchicalMenu-label:active {
  color: orange;
}

.ais-HierarchicalMenu-showMore {
  color: orange;
  cursor: pointer;
}

.ais-RefinementList {
  width: 100%;
}

.ais-RefinementList-list {
  width: auto;
}

.ais-RefinementList-item {
  width: auto;
}

.ais-RefinementList-label {
  width: 600px;
}

.ais-RefinementList-checkbox {
  margin-right: 5px;
}
.ais-RefinementList-checkbox:checked {
  color: orange;
}

.ais-RefinementList-labelText {
  cursor: pointer;
  overflow: hidden;
  width: 600px;
}

.ais-RefinementList-count {
  visibility: hidden;
}

.ais-RefinementList-showMore {
  color: orange;
  cursor: pointer;
}

.ais-RefinementList-searchBox {
  width: 100%;
}

.ais-SearchBox-form {
  position: relative;
}

.ais-SearchBox-input {
  border: 1px solid #ccc;
  padding: 5px 10px;
}

.ais-SearchBox-form::before {
  position: absolute;
  left: 94%;
  top: 70%;
  transform: translateY(-50%);
}
.ais-SearchBox-resetIcon {
  position: absolute;
  right: 90%;
  top: 50%;
  transform: translateY(-60%);
}

.ais-SortBy {
  margin-bottom: 10px;
  color: #ff6200;
}

.ais-SortBy-select {
  appearance: none;
  padding: 0.3rem 2rem 0.3rem 0.3rem;
  max-width: 100%;
  background-color: #fff;
  background-image: url("data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 24 24%27%3E%3Cpath d=%27M0 7.3l2.8-2.8 9.2 9.3 9.2-9.3 2.8 2.8-12 12.2z%27 fill%3D%22%233A4570%22 /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: 10px 10px;
  background-position: 92% 50%;
  border: 1px solid #c4c8d8;
  border-radius: 5px;
  color: #ff6200;
}
.ais-SortBy-select:hover,
.ais-SortBy-select:focus {
  background-image: url("data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 24 24%27%3E%3Cpath d=%27M0 7.3l2.8-2.8 9.2 9.3 9.2-9.3 2.8 2.8-12 12.2z%27 fill%3D%22%233A4570%22 /%3E%3C/svg%3E");
  color: #ff6200;
}

.ais-SortBy-option {
  font-weight: normal;
  display: block;
  min-height: 1.2em;
  padding: 0px 2px 1px;
  white-space: nowrap;
  color: #ff6200;
}

.ais-Breadcrumb {
  margin-bottom: 10px;
  color: #ff6200;
}

.ais-Breadcrumb-item .ais-Breadcrumb-item--selected {
  font-weight: bold;
  color: #ff6200;
}

.ais-Breadcrumb-separator {
  display: none;
}

.ais-Breadcrumb-item:not(:last-child)::after {
  content: "/";
  margin: 0 5px;
}
.ais-Breadcrumb-link {
  color: #ff6200;
}

.ais-Pagination {
  display: flex;
  list-style-type: none;
  padding: 0;
}

.ais-Pagination-list {
  display: flex;
  list-style-type: none;
  padding: 0;
}

.ais-Pagination-item {
  margin: 0 5px;
}

.ais-Pagination-item--disabled .ais-Pagination-item--firstPage,
.ais-Pagination-item--disabled .ais-Pagination-item--previousPage {
  color: #ccc;
  pointer-events: none;
}
.ais-Pagination-link {
  color: #333;
  text-decoration: none;
  padding: 5px 10px;
  border: none;
  border-radius: 5px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.ais-Pagination-link:hover {
  background-color: #000;
  color: #000;
}
.ais-Pagination-link {
  font-weight: bold;
  font-size: 14px;
}

.ais-Pagination-item--active .ais-Pagination-link {
  background-color: #0275d8;
  color: white;
}
