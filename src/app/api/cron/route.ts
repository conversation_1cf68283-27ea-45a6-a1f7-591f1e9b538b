import { transformAllProductsFromCron } from "@lib/restAPI/helpers/transformProducts";
import {
  fetchWooCommerceCategories,
  fetchWooCommerceProducts,
} from "@lib/restAPI/helpers/wooCommerceClient";
import { commonFields } from "@lib/typesense/schema";
import { typesenseClient } from "@lib/typesense/typesenseClient";
import { error, log } from "console";
import { NextRequest, NextResponse } from "next/server";

export const GET = async (req: NextRequest) => {
  // get the bearer token from the header
  const authToken = (req.headers.get("Authorization") || "")
    .split("Bearer ")
    .at(1);

  // if not found OR the token does NOT equal the CRON_SECRET
  if (!authToken || authToken !== process.env.CRON_SECRET) {
    return NextResponse.json(
      { error: "Unauthorized" },
      {
        status: 401,
      }
    );
  }
  try {
    // Needs testing on a production build, If the blocking way of dealing with promises can be a pain in the ass for performance we can switch to non-blocking execution context and notify the user of the result via email maybe.
    // Needs further discussing but for now it should do

    const start = Date.now();

    const products = await fetchWooCommerceProducts();

    const categories = await fetchWooCommerceCategories();

    const simplifiedCategories = categories.map((item) => ({
      id: item.id as number,
      name: item.name as string,
      slug: item.slug as string,
      parent: item?.parent as number,
    }));

    const getLevel: any = (level: number, id: number) => {
      if (id === 0) {
        return level;
      }
      const parent = simplifiedCategories.find((s) => s.id === id)?.parent;
      if (!parent) {
        return level + 1;
      }
      return getLevel(level + 1, parent);
    };

    const simplifiedCategoriesWithLevel = simplifiedCategories.map((item) => {
      return { ...item, lvl: getLevel(0, item.parent) };
    });

    const transformedProducts = transformAllProductsFromCron(
      products,
      simplifiedCategoriesWithLevel
    );

    // Process the data to ensure it's compatible with Typesense
    const processedProducts = transformedProducts.map((product: any) => {
      // Convert objectID to integer if it's a string
      if (typeof product.objectID === "string") {
        product.objectID = parseInt(product.objectID);
      }

      // Convert price fields to numbers if they're strings
      if (typeof product.price === "string") {
        product.price = parseFloat(product.price) || 0;
      }
      if (typeof product.regular_price === "string") {
        product.regular_price = parseFloat(product.regular_price) || 0;
      }
      if (typeof product.sale_price === "string") {
        product.sale_price = parseFloat(product.sale_price) || 0;
      }

      // Ensure category fields are properly structured for hierarchical faceting
      if (product.category) {
        // Extract category data into top-level fields for Typesense
        Object.keys(product.category).forEach((key) => {
          product[`category.${key}`] = product.category[key];
        });
      }

      return product;
    });

    // Delete existing collections and recreate them
    try {
      await typesenseClient.collections("products").delete();
      console.log("Deleted existing products collection");
    } catch (error) {
      console.log("Products collection does not exist yet");
    }

    // Create the products collection
    await typesenseClient.collections().create({
      name: "products",
      fields: commonFields as any,
      default_sorting_field: "price",
    });

    // Import in batches to avoid timeouts
    const batchSize = 100;
    let importedCount = 0;

    // Import to products collection
    for (let i = 0; i < processedProducts.length; i += batchSize) {
      const batch = processedProducts.slice(i, i + batchSize);
      await typesenseClient.collections("products").documents().import(batch);
      importedCount += batch.length;
      console.log(
        `Imported batch ${i / batchSize + 1} of ${Math.ceil(
          processedProducts.length / batchSize
        )} to products collection`
      );
    }

    // Filter sale products and import to product_sale collection
    const saleProducts = processedProducts.filter((product: any) => {
      // Check if it's a sale product (has a sale price that's different from regular price)
      return (
        product.sale_price &&
        product.sale_price > 0 &&
        product.regular_price &&
        product.sale_price < product.regular_price
      );
    });

    let saleImportedCount = 0;

    // Create product_sale collection
    try {
      await typesenseClient.collections("product_sale").delete();
      console.log("Deleted existing product_sale collection");
    } catch (error) {
      console.log("Product_sale collection does not exist yet");
    }

    await typesenseClient.collections().create({
      name: "product_sale",
      fields: commonFields as any,
      default_sorting_field: "price",
    });

    // Import sale products
    for (let i = 0; i < saleProducts.length; i += batchSize) {
      const batch = saleProducts.slice(i, i + batchSize);
      await typesenseClient
        .collections("product_sale")
        .documents()
        .import(batch);
      saleImportedCount += batch.length;
      console.log(
        `Imported batch ${i / batchSize + 1} of ${Math.ceil(
          saleProducts.length / batchSize
        )} to product_sale collection`
      );
    }

    // check the output of the response in the console
    const totalTime = (Date.now() - start) / 1000;
    const successMessage = `🎉 Successfully added ${importedCount} records to products collection and ${saleImportedCount} records to product_sale collection in ${totalTime} seconds.`;

    log(successMessage);

    return NextResponse.json(
      {
        msg: successMessage,
        details: {
          products: importedCount,
          product_sale: saleImportedCount,
          time_seconds: totalTime,
        },
      },
      {
        status: 200,
      }
    );
  } catch (e: any) {
    error(e);
    return NextResponse.json(
      { msg: `Something went wrong ${e.message}` },
      {
        status: 500,
      }
    );
  }
};
