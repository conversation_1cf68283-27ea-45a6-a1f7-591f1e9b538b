import { canActivate } from "@lib/restAPI/helpers/authGuard";
import { ContactEmailTemplate } from "@lib/restAPI/helpers/ContactEmailTemplate";
import { DeleteAccountEmailTemplate } from "@lib/restAPI/helpers/DeleteAccountEmailTemplate";
import { DropEmailTemplate } from "@lib/restAPI/helpers/DropEmailTemplate";
import { error } from "console";
import { NextRequest, NextResponse } from "next/server";
import { Resend } from "resend";

export const POST = async (req: NextRequest) => {
  const apiKey = req.headers.get("Authorization")?.split("Bearer ")[1];

  if (!canActivate(apiKey)) {
    return NextResponse.json(
      { msg: `UnAuthorized request` },
      {
        status: 401,
      }
    );
  }
  const resend = new Resend(process.env.RESEND_API_EMAIL_KEY);
  try {
    let {
      email,
      note,
      vorname: firstName,
      nachname: lastName,
      Bestellnummer: orderNumber,
      telephone: phoneNumber,
      subject,
      receiver,
      customerName,
      id,
      slug,
      productName,
    } = await req.json();
    // Decide who to send the email to, the Admin or the Contact info related email
    let to = process.env.RESEND_API_EMAIL_CONTACT_RECEIVER!;
    let EmailTemplate = ContactEmailTemplate({
      email,
      firstName,
      lastName,
      note,
      orderNumber,
      phoneNumber,
    });

    if (receiver === "DROP") {
      EmailTemplate = DropEmailTemplate({
        email,
        firstName,
        lastName,
        phoneNumber,
        id,
        slug,
        productName,
      });
      subject = `Drop Anmeldung für ${productName}`;
    }

    if (receiver === "ADMIN") {
      to = process.env.RESEND_API_EMAIL_ADMIN_RECEIVER!;
      EmailTemplate = DeleteAccountEmailTemplate({
        email,
        customerName,
      });
    }

    const { data, error } = await resend.emails.send({
      from: process.env.RESEND_API_EMAIL_DOMAIN!,
      to,
      subject,
      text: "",
      react: EmailTemplate,
    });
    if (error) {
      throw Error(error.message);
    }
    return NextResponse.json(
      { msg: `Done`, data, code: 200 },
      {
        status: 200,
      }
    );
  } catch (e: any) {
    error(e);
    throw new Error(e.message);
  }
};
