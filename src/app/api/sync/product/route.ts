import { createClient } from "@libsql/client";
import { drizzle } from "drizzle-orm/libsql";
import { revalidatePath } from "next/cache";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { syncTable } from "../../../../syncDb/schema";

export const config = {
  api: {
    bodyParser: false,
  },
};

const productSchema = z.object({
  id: z.number(),
  name: z.string(),
  slug: z.string(),
  type: z.string(),
  status: z.string(),
  description: z.string(),
  sku: z.string(),
  price: z.string(),
  regular_price: z.string(),
  sale_price: z.string(),
  stock_quantity: z.number().nullable(),
  parent_id: z.number().nullable().optional(),
  categories: z.array(
    z.object({
      id: z.number(),
      name: z.string(),
      slug: z.string(),
    })
  ),
  stock_status: z.string(),
  manage_stock: z.boolean(),
  images: z.array(
    z.object({
      id: z.number(),
      src: z.string().url(),
      name: z.string(),
      alt: z.string(),
    })
  ),
  attributes: z.array(
    z.object({
      id: z.number(),
      name: z.string(),
      slug: z.string(),
      option: z.string().optional().nullable(),
    })
  ),
  meta_data: z.array(
    z.object({
      id: z.number(),
      key: z.string(),
      value: z.unknown(),
    })
  ),
  brands: z.array(
    z.object({
      id: z.number(),
      name: z.string(),
      slug: z.string(),
    })
  ),
});

//import * as Schema from "./schema";

const client = createClient({
  url: process.env.TURSO_DATABASE_URL!,
  authToken: process.env.TURSO_AUTH_TOKEN!,
});

export const POST = async (req: NextRequest) => {
  const signature = req.headers.get("X-WC-Webhook-Signature");

  if (!req.body || !signature) {
    return NextResponse.json({ message: "No body" }, { status: 200 });
  }

  try {
    const body = await req.json();

    const validatedData = productSchema.parse(body);

    revalidatePath(`products/${validatedData.slug}`);

    const db = drizzle({ client });

    // In die syncTable einfügen
    await db.insert(syncTable).values({
      id: validatedData.id,
      parent: validatedData.parent_id,
      type: validatedData.type,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ message: error.errors }, { status: 200 });
    }
    return NextResponse.json(
      { message: "Internal Server Error" },
      { status: 200 }
    );
  }
};
