import { canActivate } from "@lib/restAPI/helpers/authGuard";
import { typesenseClient } from "@lib/typesense/typesenseClient";
import { error } from "console";
import { NextRequest, NextResponse } from "next/server";

export const POST = async (req: NextRequest) => {
  const apiKey = req.headers.get("Authorization")?.split("Bearer ")[1];

  if (!canActivate(apiKey)) {
    return NextResponse.json(
      { msg: `UnAuthorized request` },
      {
        status: 401,
      }
    );
  }

  try {
    const { id } = await req.json();
    if (!id) {
      throw new Error("No Product was found");
    }
    // Convert id to string if it's a number
    const documentId = typeof id === "number" ? id.toString() : id;

    // Delete from products collection
    await typesenseClient
      .collections("products")
      .documents(documentId)
      .delete();

    // Also try to delete from product_sale collection if it exists
    try {
      await typesenseClient
        .collections("product_sale")
        .documents(documentId)
        .delete();
    } catch (error) {
      // Ignore error if the document doesn't exist in product_sale collection
    }

    return NextResponse.json(
      { msg: `Done` },
      {
        status: 200,
      }
    );
  } catch (e: any) {
    error(e);
    return NextResponse.json(
      { msg: `Something went wrong ${e.message}` },
      {
        status: 500,
      }
    );
  }
};
