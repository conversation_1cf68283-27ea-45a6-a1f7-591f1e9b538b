import { revalidatePath } from "next/cache";
import { NextRequest, NextResponse } from "next/server";

export const POST = async (req: NextRequest) => {
  // get the bearer token from the header
  const authToken = (req.headers.get("Authorization") || "")
    .split("Bearer ")
    .at(1);

  // if not found OR the token does NOT equal the CRON_SECRET
  if (!authToken || authToken !== process.env.CRON_SECRET) {
    console.log(req.headers.get("Authorization"));
    console.log(`COULD NOT REVALIDATE FRONTPAGE WITH SECRET ${authToken}`);
    return NextResponse.json(
      { error: "Unauthorized" },
      {
        status: 401,
      }
    );
  }
  console.log("REVALIDATING FRONTPAGE");
  revalidatePath("/");
  return NextResponse.json(
    { success: true },
    {
      status: 200,
    }
  );
};
