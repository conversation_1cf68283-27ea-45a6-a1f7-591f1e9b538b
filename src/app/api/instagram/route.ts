import { error } from "console";
import { NextRequest, NextResponse } from "next/server";

export const POST = async (_: NextRequest) => {
  const INSTAGRAM_ACCESS_TOKEN = process.env.INSTAGRAM_API_KEY;
  const INSTAGRAM_TOKEN_REFRESH_URL = `https://graph.instagram.com/refresh_access_token?grant_type=ig_refresh_token&access_token=${INSTAGRAM_ACCESS_TOKEN}`;
  try {
    const res = await fetch(INSTAGRAM_TOKEN_REFRESH_URL, {
      method: "GET",
    });
    await res.json();
    return NextResponse.json({
      code: 200,
      msg: "Done",
    });
  } catch (e: any) {
    // Log to Vercel log when there is error
    error(e);
    return NextResponse.json({
      code: 500,
      msg: `Something went wrong ${e.message}`,
    });
  }
};
