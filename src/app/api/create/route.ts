import { canActivate } from "@lib/restAPI/helpers/authGuard";
import { transformProductToSearchObjects } from "@lib/restAPI/helpers/transformProducts";
import { typesenseClient } from "@lib/typesense/typesenseClient";
import { error } from "console";
import { NextRequest, NextResponse } from "next/server";

export const POST = async (req: NextRequest) => {
  const apiKey = req.headers.get("Authorization")?.split("Bearer ")[1];
  if (!canActivate(apiKey)) {
    return NextResponse.json(
      { msg: `UnAuthorized request` },
      {
        status: 401,
      }
    );
  }

  try {
    const body = await req.json();
    if (!body) {
      throw new Error("No Product was found");
    }
    const transformedProduct = transformProductToSearchObjects(body);

    // Add objectID field based on id
    const productId = transformedProduct.id;
    (transformedProduct as any).objectID = parseInt(productId);

    // Convert price fields to numbers if they're strings
    if (typeof transformedProduct.price === "string") {
      transformedProduct.price = parseFloat(transformedProduct.price) || 0;
    }
    if (typeof transformedProduct.regular_price === "string") {
      transformedProduct.regular_price =
        parseFloat(transformedProduct.regular_price) || 0;
    }
    if (typeof transformedProduct.sale_price === "string") {
      transformedProduct.sale_price =
        parseFloat(transformedProduct.sale_price) || 0;
    }

    // Ensure category fields are properly structured for hierarchical faceting
    if ((transformedProduct as any).category) {
      // Extract category data into top-level fields for Typesense
      Object.keys((transformedProduct as any).category).forEach((key) => {
        (transformedProduct as any)[`category.${key}`] = (
          transformedProduct as any
        ).category[key];
      });
    }

    // Create in products collection
    await typesenseClient
      .collections("products")
      .documents()
      .create(transformedProduct);

    // Check if it's a sale product
    const isSaleProduct =
      transformedProduct.sale_price &&
      transformedProduct.sale_price > 0 &&
      transformedProduct.regular_price &&
      transformedProduct.sale_price < transformedProduct.regular_price;

    // If it's a sale product, also add to product_sale collection
    if (isSaleProduct) {
      try {
        await typesenseClient
          .collections("product_sale")
          .documents()
          .create(transformedProduct);
      } catch (error) {
        console.warn("Error adding to product_sale collection:", error);
        // Continue execution even if there's an error with the product_sale collection
      }
    }
    return NextResponse.json(
      { msg: `Done` },
      {
        status: 200,
      }
    );
  } catch (e: any) {
    error(e);
    return NextResponse.json(
      { msg: `Something went wrong ${e.message}` },
      {
        status: 500,
      }
    );
  }
};
