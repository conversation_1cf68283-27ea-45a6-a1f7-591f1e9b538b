import { canActivate } from "@lib/restAPI/helpers/authGuard";
import { transformProductToSearchObjects } from "@lib/restAPI/helpers/transformProducts";
import { typesenseClient } from "@lib/typesense/typesenseClient";
import { error } from "console";
import { NextRequest, NextResponse } from "next/server";

export const POST = async (req: NextRequest) => {
  const apiKey = req.headers.get("Authorization")?.split("Bearer ")[1];
  if (!canActivate(apiKey)) {
    return NextResponse.json(
      { msg: `UnAuthorized request` },
      {
        status: 401,
      }
    );
  }

  try {
    const body = await req.json();

    if (!body) {
      throw new Error("No Product was found");
    }

    const transformedProduct = transformProductToSearchObjects(body);

    // Get the ID for document identification
    const documentId = transformedProduct.id.toString();

    // Convert price fields to numbers if they're strings
    if (typeof transformedProduct.price === "string") {
      transformedProduct.price = parseFloat(transformedProduct.price) || 0;
    }
    if (typeof transformedProduct.regular_price === "string") {
      transformedProduct.regular_price =
        parseFloat(transformedProduct.regular_price) || 0;
    }
    if (typeof transformedProduct.sale_price === "string") {
      transformedProduct.sale_price =
        parseFloat(transformedProduct.sale_price) || 0;
    }

    // Ensure category fields are properly structured for hierarchical faceting
    if ((transformedProduct as any).category) {
      // Extract category data into top-level fields for Typesense
      Object.keys((transformedProduct as any).category).forEach((key) => {
        (transformedProduct as any)[`category.${key}`] = (
          transformedProduct as any
        ).category[key];
      });
    }

    // Update the product in the products collection
    await typesenseClient
      .collections("products")
      .documents(documentId)
      .update(transformedProduct);

    // Check if it's a sale product
    const isSaleProduct =
      transformedProduct.sale_price &&
      transformedProduct.sale_price > 0 &&
      transformedProduct.regular_price &&
      transformedProduct.sale_price < transformedProduct.regular_price;

    try {
      if (isSaleProduct) {
        // If it's a sale product, add/update it in the product_sale collection
        // Use create or update since upsert might not be available
        try {
          // Try to update first
          await typesenseClient
            .collections("product_sale")
            .documents(documentId)
            .update(transformedProduct);
        } catch (error) {
          // If update fails, try to create
          await typesenseClient
            .collections("product_sale")
            .documents()
            .create(transformedProduct);
        }
      } else {
        // If it's not a sale product, remove it from the product_sale collection if it exists
        try {
          await typesenseClient
            .collections("product_sale")
            .documents(documentId)
            .delete();
        } catch (error) {
          // Ignore error if the document doesn't exist in product_sale collection
        }
      }
    } catch (error) {
      console.warn("Error updating product_sale collection:", error);
      // Continue execution even if there's an error with the product_sale collection
    }

    return NextResponse.json(
      { msg: `Done` },
      {
        status: 200,
      }
    );
  } catch (e: any) {
    error(e);
    return NextResponse.json(
      { msg: `Something went wrong ${e.message}` },
      {
        status: 500,
      }
    );
  }
};
