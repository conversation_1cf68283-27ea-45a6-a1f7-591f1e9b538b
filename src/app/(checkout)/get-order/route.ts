import { getWooCommerceOrder } from "@lib/utilities/StripeHelpers/wooCommerceApi";
import { NextRequest, NextResponse } from "next/server";
export async function GET(request: NextRequest) {
  const res = await request.json();
  const { orderId } = res;
  try {
    const order = await getWooCommerceOrder(orderId);
    return NextResponse.json(order);
  } catch (error: any) {
    return NextResponse.json({
      details: error.message,
    });
  }
}
