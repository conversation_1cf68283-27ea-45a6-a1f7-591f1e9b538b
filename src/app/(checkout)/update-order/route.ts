import { updateWooCommerceOrder } from "@lib/utilities/StripeHelpers/wooCommerceApi";
import { NextRequest, NextResponse } from "next/server";
export async function PUT(request: NextRequest) {
  const res = await request.json();
  const { orderId, updatedUserData } = res;
  try {
    await updateWooCommerceOrder(updatedUserData, orderId);
    return NextResponse.json({ isCreated: true });
  } catch (error: any) {
    return NextResponse.json({
      isUpdated: false,
      details: error.message,
    });
  }
}
