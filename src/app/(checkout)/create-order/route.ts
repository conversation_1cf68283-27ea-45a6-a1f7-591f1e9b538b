const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
import { createWooCommerceOrder } from "@lib/utilities/StripeHelpers/wooCommerceApi";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  const res = await request.json();
  const { orderDataPayload, intentId } = res;
  try {
    const wooOrderResponse = await createWooCommerceOrder({
      ...orderDataPayload,
      meta_data: [
        {
          key: "_stripe_intent_id",
          value: intentId,
        },
      ],
    });

    const { data } = wooOrderResponse,
      cartTotal = data.total;

    const _updatedPaymentIntent = await stripe.paymentIntents.update(intentId, {
      amount: Math.floor(cartTotal * 100),
    });

    return NextResponse.json({
      orderIsCreated: true,
      paymentIntentIsUpdate: true,
      orderId: wooOrderResponse.data.id,
    });
  } catch (error: any) {
    console.error(error);
    return NextResponse.json({
      isCreated: false,
      details: error.message,
    });
  }
}
