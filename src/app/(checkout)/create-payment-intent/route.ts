import { NextResponse } from "next/server";

// This is your test secret API key.
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

const calculateOrderAmount = (total: string): number => {
  return Math.floor(Number(total) * 100);
};

export async function POST(request: Request) {
  try {
    const req = await request.json();
    const { total } = req;

    // Create a PaymentIntent with the order amount and currency
    const paymentIntent = await stripe.paymentIntents.create({
      amount: calculateOrderAmount(total),
      currency: "eur",
    });

    return NextResponse.json({
      paymentIntent: paymentIntent,
    });
  } catch (error) {
    throw new Error(error as string);
  }
}
