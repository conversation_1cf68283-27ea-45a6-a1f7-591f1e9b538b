import { deleteWooCommerceOrder } from "@lib/utilities/StripeHelpers/wooCommerceApi";
import { NextRequest, NextResponse } from "next/server";

export async function DELETE(request: NextRequest) {
  const res = await request.json();
  const { orderId } = res;
  try {
    await deleteWooCommerceOrder(orderId);

    return NextResponse.json({ isCreated: true });
  } catch (error: any) {
    return NextResponse.json({
      isCreated: false,
      details: error.message,
    });
  }
}
