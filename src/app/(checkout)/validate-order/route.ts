import { NextRequest, NextResponse } from "next/server";

// const CoCartAPI = require("@cocart/cocart-rest-api").default;
// const CoCart = new CoCartAPI({
//   url: process.env.WORDPRESS_URL,
//   consumerKey: process.env.WOOCOMMERCE_KEY,
//   consumerSecret: process.env.WOOCOMMERCE_SECRET,
// });
export async function GET(request: NextRequest) {
  // get cart
  const req = await request.json();

  const { _cartTotal, orderId } = req;

  /**
   *
     1 - get order
     2 - get cart
     3 - compare order.total ?==  card.total
     4 - onComplete return true/false
     5 - onError return {status: error} so the operation halts
   *
   */

  const _order = await fetch("/get-order", {
    method: "GET",
    mode: "cors",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      orderId,
    }),
  });

  try {
    return NextResponse.json({
      isValid: true,
    });
  } catch (error: any) {
    return NextResponse.json({
      isValid: false,
      reason: error,
    });
  }
}
