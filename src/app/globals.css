@tailwind base;
@tailwind components;
@tailwind utilities;

/* * {
  outline: solid 1px lime;
  background-color: #119f222f;
} */
@layer base {
  :root {
    --background: 0 0% 98%;
    --foreground: 20 14.3% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 20 14.3% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 20 14.3% 4.1%;
    --primary: 24.6 95% 53.1%;
    --primary-foreground: 60 9.1% 97.8%;
    --secondary: 60 4.8% 95.9%;
    --secondary-foreground: 24 9.8% 10%;
    --muted: 60 4.8% 95.9%;
    --muted-foreground: 25 5.3% 44.7%;
    --accent: 60 4.8% 95.9%;
    --accent-foreground: 24 9.8% 10%;
    --destructive: 0 66% 55%;
    --destructive-foreground: 60 9.1% 97.8%;
    --success: 162 45% 55%;
    --success-foreground: 0 0% 98%;
    --border: 20 5.9% 90%;
    --input: 20 5.9% 90%;
    --ring: 24.6 95% 53.1%;
    --radius: 0rem;
    --black: 0 0% 21%;
    --gray: 0 0% 31%;
    --gray-light-bright: 0 0% 94%;
    --gray-light: 217 9% 62%;
    --gray-middle: 0 0% 86%;
    --gray-dark: 0 0% 21%;
    --password-very-weak: 357 100% 84%;
    --password-weak: 356 81% 90%;
    --password-medium: 48 79% 82%;
    --password-strong: 129 48% 81%;
  }
}

@layer base {
  @media (prefers-reduced-motion) {
    * {
      @apply transition-none !important;
    }
  }
  * {
    @apply border-border;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  }
  body {
    @apply bg-background text-foreground;
  }
}
@layer utilities {
  .data-th::before {
    content: attr(data-th);
    font-weight: bold;
  }
}
