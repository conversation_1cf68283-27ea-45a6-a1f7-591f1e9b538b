export const getClothSize = ({
  category: categoryUnsafe,
  size: sizeUnsafe,
  brand: brandUnsafe,
}: {
  category: string[];
  size: string | number;
  brand: string | null;
}) => {
  const category = categoryUnsafe.map((cat) => cat.toLowerCase());
  const size = String(sizeUnsafe)
    .toLowerCase()
    .replace("us ", "")
    .replace("-", ".")
    .replace(",", ".");
  const brand = brandUnsafe ? brandUnsafe.toLowerCase() : null;

  if (category.some((cat) => ["shoes"].includes(cat))) {
    // SCHUHE
    switch (brand) {
      case "adidas skateboarding": {
        switch (size) {
          case "1c":
          case "1":
            return 32;
          case "1.5c":
          case "1.5":
            return 33;
          case "2c":
          case "2":
            return 33.5;
          case "2.5c":
          case "2.5":
            return 34;
          case "3c":
          case "3":
            return 35;
          case "3.5c":
          case "3.5":
            return 35.5;
          case "4c":
          case "4":
            return 36;
          case "4.5": {
            return 36 + 2 / 3;
          }
          case "5": {
            return 37 + 1 / 3;
          }
          case "5.5": {
            return 38;
          }
          case "6":
            return 38 + 2 / 3;
          case "6.5":
            return 39 + 1 / 3;
          case "7":
            return 40;
          case "7.5":
            return 40 + 2 / 3;
          case "8":
            return 41 + 1 / 3;
          case "8.5":
            return 42;
          case "9":
            return 42 + 2 / 3;
          case "9.5":
            return 43 + 1 / 3;
          case "10":
            return 44;
          case "10.5":
            return 44 + 2 / 3;
          case "11":
            return 45 + 1 / 3;
          case "11.5":
            return 46;
          case "12":
            return 46 + 2 / 3;
          case "12.5":
            return 47 + 1 / 3;
          case "13":
            return 48;
          case "13.5":
            return 48 + 2 / 3;
          case "14":
            return 49 + 1 / 3;
          case "14.5":
            return 50;
          case "15":
            return 50 + 2 / 3;
          default:
            return null;
        }
      }
      case "converse": {
        switch (size) {
          case "3":
            return 35;
          case "3.5":
            return 36;
          case "4":
            return 36.5;
          case "4.5":
            return 37;
          case "5":
            return 37.5;
          case "5.5":
            return 38;
          case "6":
            return 39;
          case "6.5":
            return 39.5;
          case "7":
            return 40;
          case "7.5":
            return 41;
          case "8":
            return 41.5;
          case "8.5":
            return 42;
          case "9":
            return 42.5;
          case "9.5":
            return 43;
          case "10":
            return 44;
          case "10.5":
            return 44.5;
          case "11":
            return 45;
          case "11.5":
            return 46;
          case "12":
            return 46.5;
          case "13":
            return 48;
          case "14":
            return 49;
          case "15":
            return 50;
          default:
            return null;
        }
      }
      case "dc": {
        switch (size) {
          case "4":
            return 36;
          case "4.5":
            return 36.5;
          case "5":
            return 37;
          case "5.5":
            return 37.5;
          case "6":
            return 38;
          case "6.5":
            return 38.5;
          case "7":
            return 39;
          case "7.5":
            return 40;
          case "8":
            return 40.5;
          case "8.5":
            return 41.5;
          case "9":
            return 42;
          case "9.5":
            return 42.5;
          case "10":
            return 43;
          case "10.5":
            return 44;
          case "11":
            return 44.5;
          case "11.5":
            return 45.5;
          case "12":
            return 46;
          case "12.5":
            return 46.5;
          case "13":
            return 47.5;
          case "14":
            return 48;
          case "15":
            return 50;
          default:
            return null;
        }
      }
      case "emerica": {
        switch (size) {
          case "5":
            return 37;
          case "5.5":
            return 37.5;
          case "6":
            return 38;
          case "6.5":
            return 38.5;
          case "7":
            return 39;
          case "7.5":
            return 40;
          case "8":
            return 41;
          case "8.5":
            return 41.5;
          case "9":
            return 42;
          case "9.5":
            return 42.5;
          case "10":
            return 43;
          case "10.5":
            return 44;
          case "11":
            return 45;
          case "11.5":
            return 45.5;
          case "12":
            return 46;
          case "13":
            return 47;
          case "14":
            return 48;
          case "15":
            return 49;
          default:
            return null;
        }
      }
      case "etnies": {
        switch (size) {
          case "3.5":
            return 35;
          case "4":
            return 35.5;
          case "4.5":
            return 36;
          case "5":
            return 37;
          case "5.5":
            return 37.5;
          case "6":
            return 38;
          case "6.5":
            return 38.5;
          case "7":
            return 39;
          case "7.5":
            return 40;
          case "8":
            return 41;
          case "8.5":
            return 41.5;
          case "9":
            return 42;
          case "9.5":
            return 42.5;
          case "10":
            return 43;
          case "10.5":
            return 44;
          case "11":
            return 45;
          case "11.5":
            return 45.5;
          case "12":
            return 46;
          case "13":
            return 47;
          case "14":
            return 48;
          case "15":
            return 49;
          default:
            return null;
        }
      }
      case "globe": {
        switch (size) {
          case "4":
            return 36;
          case "4.5":
            return 36.5;
          case "5":
            return 37;
          case "5.5":
            return 37.5;
          case "6":
            return 38;
          case "6.5":
            return 39;
          case "7":
            return 39.5;
          case "7.5":
            return 40;
          case "8":
            return 40.5;
          case "8.5":
            return 41.5;
          case "9":
            return 42;
          case "9.5":
            return 42.5;
          case "10":
            return 43;
          case "10.5":
            return 44;
          case "11":
            return 44.5;
          case "11.5":
            return 45.5;
          case "12":
            return 46;
          case "12.5":
            return 46.5;
          case "13":
            return 47.5;
          case "14":
            return 48;
          case "15":
            return 48.5;
          default:
            return null;
        }
      }
      case "huf": {
        switch (size) {
          case "4":
            return 36;
          case "4.5":
            return 36.5;
          case "5":
            return 37;
          case "5.5":
            return 37.5;
          case "6":
            return 38;
          case "6.5":
            return 38 - 5;
          case "7":
            return 39;
          case "7.5":
            return 40;
          case "8":
            return 40.5;
          case "8.5":
            return 41;
          case "9":
            return 42;
          case "9.5":
            return 42.5;
          case "10":
            return 43;
          case "10.5":
            return 44;
          case "11":
            return 44.5;
          case "11.5":
            return 45;
          case "12":
            return 45.5;
          case "12.5":
            return 46;
          case "13":
            return 47;
          default:
            return null;
        }
      }
      case "new balance": {
        switch (size) {
          case "4":
            return 36;
          case "4.5":
            return 37;
          case "5":
            return 37.5;
          case "5.5":
            return 38;
          case "6":
            return 38.5;
          case "6.5":
            return 39;
          case "7":
            return 40;
          case "7.5":
            return 40.5;
          case "8":
            return 41.5;
          case "8.5":
            return 42;
          case "9":
            return 42.5;
          case "9.5":
            return 43;
          case "10":
            return 44;
          case "10.5":
            return 44.5;
          case "11":
            return 45;
          case "11.5":
            return 46;
          case "12":
            return 46.5;
          case "12.5":
            return 47;
          case "13":
            return 47.5;
          case "14":
            return 49;
          case "15":
            return 50;
          default:
            return null;
        }
      }
      case "nike sb": {
        switch (size) {
          case "3.5":
            return 35.5;
          case "4":
            return 36;
          case "4.5":
            return 36.5;
          case "5":
            return 37.5;
          case "5.5":
            return 38;
          case "6":
            return 38.5;
          case "6.5":
            return 39;
          case "7":
            return 40;
          case "7.5":
            return 40.5;
          case "8":
            return 41;
          case "8.5":
            return 42;
          case "9":
            return 42.5;
          case "9.5":
            return 43;
          case "10":
            return 44;
          case "10.5":
            return 44.5;
          case "11":
            return 45;
          case "11.5":
            return 45.5;
          case "12":
            return 46;
          case "12.5":
            return 47;
          case "13":
            return 47.5;
          case "13.5":
            return 48;
          case "14":
            return 48.5;
          case "14.5":
            return 49;
          case "15":
            return 49.5;
          case "15.5":
            return 50;
          case "16":
            return 50.5;
          case "16.5":
            return 51;
          case "17":
            return 51.5;
          case "17.5":
            return 52;
          case "18":
            return 52.5;
          case "18.5":
            return 53;
          case "19":
            return 53.5;
          case "19.5":
            return 54;
          case "20":
            return 54.5;
          case "20.5":
            return 55;
          case "21":
            return 55.5;
          case "21.5":
            return 56;
          case "22":
            return 56.5;
          default:
            return null;
        }
      }
      case "reebok": {
        switch (size) {
          case "4":
            return 34.5;
          case "4.5":
            return 35;
          case "5":
            return 36;
          case "5.5":
            return 36.5;
          case "6":
            return 37.5;
          case "6.5":
            return 38.5;
          case "7":
            return 39;
          case "7.5":
            return 40;
          case "8":
            return 40.5;
          case "8.5":
            return 41.5;
          case "9":
            return 42;
          case "9.5":
            return 42.5;
          case "10":
            return 43;
          case "10.5":
            return 44;
          case "11":
            return 44.5;
          case "11.5":
            return 45;
          case "12":
            return 45.5;
          case "12.5":
            return 46;
          case "13":
            return 47;
          case "14":
            return 48.5;
          case "15":
            return 50;
          default:
            return null;
        }
      }
      case "vans":
      default:
        switch (size) {
          case "3.5":
            return 34.5;
          case "4.5":
            return 36;
          case "5":
            return 36.5;
          case "5.5":
            return 37;
          case "6":
            return 38;
          case "6.5":
            return 38.5;
          case "7":
            return 39;
          case "7.5":
            return 40;
          case "8":
            return 40.5;
          case "8.5":
            return 41;
          case "9":
            return 42;
          case "9.5":
            return 42.5;
          case "10":
            return 43;
          case "10.5":
            return 44;
          case "11":
            return 44.5;
          case "11.5":
            return 45;
          case "12":
            return 46;
          case "13":
            return 47;
          case "14":
            return 48;
          case "15":
            return 49;
          case "16":
            return 50;
          default:
            return null;
        }
    }
  }

  return null;
};

export const getClothSizeString = (
  brand: string | null,
  category: string[],
  size: string | number
) => {
  return String(
    Math.floor(
      (getClothSize({
        brand,

        category,
        size: size,
      }) ?? 0) * 10
    ) / 10
  );
};
