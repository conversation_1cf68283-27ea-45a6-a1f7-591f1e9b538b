// Fetching data (the data from Hallo page include Banner, HeroGrid, productSliders, BrandSliders and textAreas) from WordPress to display in home page

import { getAllBrands } from "@gql-queries/getAllBrands";
//import { getAllProductsPage } from "@gql-queries/getAllProductsPage";
import {
  getAccessoriesQuery,
  getFirstTextilQuery,
  getLastTextilQuery,
  getSkateboardsQuery,
} from "@gql-queries/getcategories";
import {
  searchClient as typesenseSearchClient,
  typesenseClient,
} from "@lib/typesense/typesenseClient";
import { homePageGraphQLResponse } from "@typesDeclarations/homePageTypes";
import { and, eq, gt, or } from "drizzle-orm";

import { getProduct } from "../graphql/queries/getSingleProduct";
import {
  getBrandsQuery,
  //  getLatestProductsQuery,
  // getProductsByCategoryIDQuery,
  // getProductsWithoutCategory,
} from "../graphql/queries/serverSide/queries.homepage";
import { db } from "../syncDb/db";
import { productsTable, variationsTable } from "../syncDb/schema";

async function fetchAPI(
  query: string,
  { variables }: { variables?: any } = {}
): Promise<any> {
  const headers = { "content-Type": "application/json" };

  const res = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URI as string, {
    method: "POST",
    mode: "cors",
    headers,
    cache: "force-cache",
    next: {
      revalidate: 3600,
    },
    body: JSON.stringify({
      query,
      variables,
    }),
  });
  const json = await res.json();
  return json.data;
}

export async function getHomePage(): Promise<homePageGraphQLResponse> {
  const data = await fetchAPI(
    `
    query NewQuery {
      pageBy(uri: "3334058-2") {
        blocks
      }
    }
    `
  );
  return data?.pageBy;
}

/**
 * @description fetched top selling brands
 * @usage serverSide */
export async function getBrands() {
  const res = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URI as string, {
    method: "POST",
    headers: { "content-type": "application/json" },
    body: JSON.stringify({
      query: getBrandsQuery,
    }),
    cache: "force-cache",
    next: {
      revalidate: 3600,
    },
  });
  const parsed = await res.json();

  return parsed;
}

type TypesenseProduct = {
  brands: {
    id: number;
    name: string;
    slug: string;
  }[];
  category: Record<string, string>;
  category_raw: {
    id: number;
    name: string;
    slug: string;
  }[];
  category_id: number[];
  category_name: string[];
  category_slug: string[];
  category_facet: string[];
  dateCreated: number;
  description: string;
  id: string;
  images: string[];
  isPublished: boolean;
  name: string;
  objectID: string;
  pa_clothsize: string[];
  price: number;
  price_sort: number;
  regular_price: number;
  slug: string;
  stockStatus: boolean;
  type: string;
};

/** @description get new products for category slider  (for a specific category) */
export async function getProductsByCategoryID(id?: number | null) {
  if (id) {
    // Get items from typesense
    const searchResults = await typesenseClient
      .collections<TypesenseProduct>("products")
      .documents()
      .search({
        q: "*",
        query_by: "name,description,brands.name,sku,slug",
        filter_by: `stockStatus:true && category_ids:=${id}`,
        sort_by: "dateCreated:desc",
        per_page: 9,
      });

    return searchResults;
  } else {
    return await getProducts();
  }
}

/** @description get new products for category slider  */
export async function getProducts() {
  // Get items from typesense
  const searchResults = await typesenseClient
    .collections<TypesenseProduct>("products")
    .documents()
    .search({
      q: "*",
      query_by: "name,description,brands.name,sku,slug",
      filter_by: "stockStatus:true",
      sort_by: "dateCreated:desc",
      per_page: 9,
    });

  return searchResults;
}

/** @description get Instagram post form Instagram API  */
export async function getInstagramPost() {
  const response = await fetch(
    ("https://graph.instagram.com/me/media?fields=id,caption,media_url,permalink,thumbnail_url,media_type,timestamp,children{media_url}&limit=4&access_token=" +
      process.env.INSTAGRAM_API_KEY) as string,
    {
      method: "GET",
    }
  );
  const data = await response.json();
  return data.data;
}

export async function getSingleProduct(id: any) {
  // Make an asynchronous function to fetch a single product based on its ID
  const res = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URI as string, {
    method: "POST",
    headers: { "content-type": "application/json" },

    body: JSON.stringify({
      query: getProduct,
      variables: { id: id },
      // Pass the 'id' variable as an argument to the query
      // The 'id' variable represents the ID of the product to fetch
    }),
  });
  const parsed = await res.json();
  return parsed;
}

export async function getAllProducts() {
  // Make an asynchronous function to fetch all products

  try {
    // Get all documents from the products collection
    const searchResults = await db
      .select({ slug: productsTable.slug })
      .from(productsTable)
      .leftJoin(variationsTable, eq(variationsTable.parent, productsTable.id))
      .where(
        or(
          and(
            eq(productsTable.isPublished, true),
            gt(productsTable.stockQuantity, 0)
          ),
          and(
            eq(productsTable.isPublished, true),
            gt(variationsTable.stockQuantity, 0)
          )
        )
      )
      .groupBy(productsTable.id);
    return searchResults ?? [];
  } catch (error) {
    console.error("Error fetching products from Typesense:", error);
  }

  /*

  const res = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URI as string, {
    method: "POST",
    headers: { "content-type": "application/json" },

    body: JSON.stringify({
      query: getAllProductsPage,
      variables: { after: "" },
      // Pass the 'id' variable as an argument to the query
      // The 'id' variable represents the ID of the product to fetch
    }),
  });
  const parsed = await res.json();
  let hasNextPage = parsed.data?.products?.pageInfo?.hasNextPage;
  let after = parsed.data?.products?.pageInfo?.endCursor;
  let products = parsed.data?.products?.nodes;
  while (hasNextPage) {
    const res = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URI as string, {
      method: "POST",
      headers: { "content-type": "application/json" },

      body: JSON.stringify({
        query: getAllProductsPage,
        variables: { after: after },
        // Pass the 'id' variable as an argument to the query
        // The 'id' variable represents the ID of the product to fetch
      }),
    });
    const parsed = await res.json();
    hasNextPage = parsed.data?.products?.pageInfo?.hasNextPage;
    after = parsed.data?.products?.pageInfo?.endCursor;
    products = products.concat(parsed.data?.products?.nodes);
  }
    */
}

export async function getAllBrandsPage() {
  const res = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URI as string, {
    method: "POST",
    headers: { "content-type": "application/json" },
    body: JSON.stringify({
      query: getAllBrands,
    }),
    cache: "force-cache",
    next: {
      revalidate: 3600,
    },
  });
  const parsed = await res.json();

  return parsed;
}

// Use Typesense search client instead of Algolia
const searchClient = typesenseSearchClient;

export default searchClient;

export async function getSkateboards() {
  const res = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URI as string, {
    method: "POST",
    headers: { "content-type": "application/json" },
    body: JSON.stringify({
      query: getSkateboardsQuery,
    }),
    cache: "force-cache",
    next: {
      revalidate: 3600,
    },
  });
  const parsed = await res.json();

  return parsed;
}

export async function getAccessories() {
  const res = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URI as string, {
    method: "POST",
    headers: { "content-type": "application/json" },
    body: JSON.stringify({
      query: getAccessoriesQuery,
    }),
    cache: "force-cache",
    next: {
      revalidate: 3600,
    },
  });
  const parsed = await res.json();

  return parsed;
}

export async function getFirstTextil() {
  const res = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URI as string, {
    method: "POST",
    headers: { "content-type": "application/json" },
    body: JSON.stringify({
      query: getFirstTextilQuery,
    }),
    cache: "force-cache",
    next: {
      revalidate: 3600,
    },
  });
  const parsed = await res.json();

  return parsed;
}

export async function getLastTextil() {
  const res = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URI as string, {
    method: "POST",
    headers: { "content-type": "application/json" },
    body: JSON.stringify({
      query: getLastTextilQuery,
    }),
    cache: "force-cache",
    next: {
      revalidate: 3600,
    },
  });
  const parsed = await res.json();

  return parsed;
}
