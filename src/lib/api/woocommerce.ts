import CoCartAPI from "@cocart/cocart-rest-api";
import WooCommerceRestApi from "@woocommerce/woocommerce-rest-api";

import { Variation } from "../../state/schema";

export const wooApi = new WooCommerceRestApi({
  url: process.env.WORDPRESS_URL!,
  consumerKey: process.env.WOOCOMMERCE_KEY!,
  consumerSecret: process.env.WOOCOMMERCE_SECRET!,
  version: "wc/v3",
});

/* @ts-ignore */
export const CoCart = new CoCartAPI({
  url: process.env.NEXT_PUBLIC_WORDPRESS_URI as string,
  timeout: 60000,
});

export const getProduct = async (slug: string) => {
  try {
    const { data } = await wooApi.get(`products`, {
      slug: slug,
      _fields:
        "id,slug,name,description,images,brands,meta_data,categories,stock_quantity,stock_status,price,regular_price,sale_price,variations",
    });

    /*
    const { data } = await CoCart.get(`products?slug=${slug}`);
*/
    return data[0];
  } catch (error) {
    console.error("Error fetching product:", error);
    return null;
  }
};

export const getProductVariations = async (
  productId: number
): Promise<Variation[]> => {
  try {
    const { data } = await CoCart.get(`products/${productId}/variations`);
    return data;
  } catch (error) {
    console.error("Error fetching product variations:", error);
    return [];
  }
};

export const getAllProducts = async () => {
  try {
    const { data } = await wooApi.get("products", {
      per_page: 100, // Anpassen nach Bedarf
    });
    return data;
  } catch (error) {
    console.error("Error fetching all products:", error);
    return [];
  }
};
