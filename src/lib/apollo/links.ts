import { ApolloLink, HttpLink, split } from "@apollo/client";
import { setContext } from "@apollo/client/link/context";
import { onError } from "@apollo/client/link/error";
import { getMainDefinition } from "@apollo/client/utilities";
import { toast } from "@components/ui/use-toast";
import { tokenKeys } from "@constants/authConstants";
import {
  getAuthToken,
  getSessionToken,
  readRefreshToken,
  requiresAuthorization,
} from "@lib/utilities/authUtils";
import { redirect } from "next/navigation";

// console start: print which operation is initiated
//
// session link: add sessionToken to woocommerce_session_header HTTP header

// authentication: session
// permission: authLink
// isAuthoredOperationLink: split link—if operation requires authentication header (jwt auth token), go to authLink. else forward operation to next link
// authLink: add authToken to Authorization Bearer HTTP header

// ErrorLink: Apollo client for handling GraphQL-raised errors (including erroneous responses)

// console end: print which operation is completed

// ! return forward(operation), which is the syntax for calling the next link down the chain.

/** see [Apollo Links](https://www.apollographql.com/docs/react/api/link/introduction) */
export const consoleStartOpLink = new ApolloLink((operation, forward) => {
  return forward(operation).map((data) => {
    console.log(`starting request for ${operation.operationName}`);
    if (operation.variables)
      console.log("with variables:", operation.variables);

    return data;
  });
});

export const sessionLink = setContext(async (_, { headers }) => {
  let sessionToken = await getSessionToken();

  const payload = sessionToken.split(".")?.[1];

  // if session token is expired, fetch a new one
  if (payload) {
    const decodedPayload = JSON.parse(atob(payload));
    console.log("decodedPayload", decodedPayload, Date.now() / 1000 - 300);
    if (
      !decodedPayload?.exp ||
      decodedPayload?.exp <= Date.now() / 1000 - 300
    ) {
      console.log("session token is expired, fetching a new one");
      sessionToken = await getSessionToken(true);
    }
  }

  /** `isAuthenticated` is used to remove sessionToken whenever a valid authentication [bearer] token is available
   * This is done because `Session token` takes precedence over `jwt token` on the backend, which results in invalid session state on the client
   */
  const isAuthenticated = Boolean(readRefreshToken());
  const nextHeaders = !isAuthenticated
    ? {
        ...headers,
        [`${tokenKeys.woocommerce_session_header}`]: `Session ${sessionToken}`,
      }
    : headers;
  return {
    headers: {
      ...nextHeaders,
    },
  };
});
export const authLink = setContext(async (_, { headers }) => {
  const authToken = await getAuthToken();
  return {
    invalidToken: Boolean(authToken),
    headers: {
      ...headers,
      authorization: authToken ? `Bearer ${authToken}` : "",
    },
  };
});

export const isAuthoredOperationSplitLink = split(({ query: operation }) => {
  const definition = getMainDefinition(operation);
  const operationName = definition.name?.value ?? "";
  console.log("entered isAuthoredOperationLink");
  console.log("operation name: ", operationName);
  console.log(
    "will proceed with authLink = ",
    Boolean(requiresAuthorization(operationName) && !!readRefreshToken())
  );
  console.log("leaving isAuthoredOperationLink");

  return Boolean(requiresAuthorization(operationName) && !!readRefreshToken());
}, authLink);

export const errorLink = onError(({ graphQLErrors, networkError }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.log(
        `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
      );
      toast({
        description: `[graphQLErrors]: Message: ${message}`,
        variant: "destructive",
        itemID: "network-error",
      });
      if (message === "not authorized") {
        console.log("Should redirect");
        redirect("/login");
      }
    });
  }
  if (networkError) {
    toast({
      description: `[Network  test error]: ${networkError}`,
      variant: "destructive",
      itemID: "network-error",
    });
  }
  console.log("leaving error link");
});

// /**
//  * HttpLink to customize apollo client behavior.
//  * @param {string} uri server's URL
//  * @param {string} credentials read-only property that indicates whether to send/receive cookies
//  * credentials: 'include' : indicates to always send user credentials with each request
//  */
export const httpLink = new HttpLink({
  uri: process.env.NEXT_PUBLIC_GRAPHQL_URI,
});

export const consoleEndOpLink = new ApolloLink((operation, forward) => {
  return forward(operation).map((data) => {
    console.log(`ending request for ${operation.operationName}`);
    return data;
  });
});
