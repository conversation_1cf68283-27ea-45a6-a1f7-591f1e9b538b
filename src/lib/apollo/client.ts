import { ApolloClient, from, InMemoryCache } from "@apollo/client";

import {
  //consoleEndOpLink,
  //consoleStartOpLink,
  errorLink,
  httpLink,
  isAuthoredOperationSplitLink,
  sessionLink,
} from "./links";

export const apolloClient = new ApolloClient({
  link: from([
    errorLink,
    //consoleStartOpLink,
    sessionLink,
    isAuthoredOperationSplitLink,
    //consoleEndOpLink,
    httpLink,
  ]),
  cache: new InMemoryCache(),
  connectToDevTools: true,
  defaultOptions: {
    watchQuery: {
      fetchPolicy: "no-cache",
      errorPolicy: "all",
    },
    query: {
      fetchPolicy: "no-cache",
      errorPolicy: "all",
    },
    mutate: {
      errorPolicy: "all",
    },
  },
});
