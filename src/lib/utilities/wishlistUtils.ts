import { WISHLIST } from "@constants/sessionConstants";
import { getPriceFormatter } from "@lib/utils";
import type { wishlist_LS, wishlistProduct } from "@typesDeclarations/wishlist";

const { deutschFormatter: formatPrice } = getPriceFormatter();

/**
 * @description returns an array of products databaseIds, which are used to fetch product data
 */
export function getWishlist_LS(): wishlist_LS {
  try {
    return JSON.parse(localStorage.getItem(WISHLIST) ?? "[]");
  } catch {
    return [];
  }
}

export const updateWishlist_LS = (wishlist: wishlist_LS) => {
  localStorage.setItem(WISHLIST, JSON.stringify(wishlist));
};

/**
 * @notice passing the wishlist to avoid cyclical dependency
 * @returns boolean
 * * true: item exists in wishlist LocalStorage
 * * false: item does not exist in wishlist LocalStorage
 */
export function doesItemExistInWishListLS(
  wishList: wishlist_LS,
  databaseId: string | number
): boolean {
  return wishList.some((item) => item.databaseId == databaseId);
}

const _addItemToWishlistLS = (
  databaseId: string | number,
  variationId?: string | number
): void => {
  const currentList = getWishlist_LS();
  let newList = [...currentList, { databaseId, variationId }];
  //   make sure all entries are unique
  newList = Array.from(new Set(newList));

  localStorage.setItem(WISHLIST, JSON.stringify(newList));
};

const _removeItemFromWishlistLS = (databaseId: string | number): void => {
  const currentList = getWishlist_LS(),
    newList = currentList.filter((item) => item.databaseId != databaseId);

  localStorage.setItem(WISHLIST, JSON.stringify(newList));
};

/** adds/removes wishlist item from local storage */
export const toggleItemInWishList_LS = (databaseId: string | number): void => {
  doesItemExistInWishListLS(getWishlist_LS(), databaseId)
    ? _removeItemFromWishlistLS(databaseId)
    : _addItemToWishlistLS(databaseId);
};

/**
 *  @description adds/removes wishlist item to LocalStorage
 *  @IMPORTANT returns the updated wishlist
 */
export const toggleItemInWishListLS = (
  wishlist: wishlist_LS,
  databaseId: string | number
): wishlist_LS => {
  doesItemExistInWishListLS(wishlist, databaseId)
    ? _removeItemFromWishlistLS(databaseId)
    : _addItemToWishlistLS(databaseId);

  return getWishlist_LS();
};
/** @description removes all wishlist items upon user logout */
export const clearWishlist = () => {
  localStorage.removeItem(WISHLIST);
};

export function adaptWishlistSimpleProduct(product: any): wishlistProduct & {
  databaseId: string | number;
} {
  return {
    databaseId: product.databaseId,
    category: product.productCategories.nodes[0].name,
    image: {
      sourceUrl: product.image.sourceUrl,
      alt: product.image.altText,
    },
    name: product.name,
    regularPrice: formatPrice(product.regularPrice),
    salePrice: formatPrice(product.salePrice),
    slug: product.slug,
  };
}

export function adaptWishlistVariableProduct(
  product: any
  // specifiedVariationID: string
): wishlistProduct & {
  databaseId: string | number;
} {
  // const specifiedVariationNode = product.variations.nodes.find(
  //   (variationNode: any) => {
  //     return `${variationNode.databaseId}` === `${specifiedVariationID}`;
  //   }
  // );
  return {
    databaseId: product.databaseId,
    category: product.productCategories.nodes[0].name,
    image: {
      sourceUrl: product.image.sourceUrl,
      alt: product.image.altText,
    },
    name: product.name,
    regularPrice: formatPrice(product.regularPrice),
    salePrice: formatPrice(product.salePrice),
    slug: product.slug,
    // size: specifiedVariationNode.attributes.nodes[0].value,
  };
}
