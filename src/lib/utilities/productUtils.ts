"use client";
import { getPriceFormatter } from "@lib/utils";
import {
  formatted_ShoppingCartProduct,
  shoppingCart_item,
} from "@typesDeclarations/wooTypes/wooCartTypes";

const { deutschFormatter: formatPrice } = getPriceFormatter();

/**
 * @description adapts a product item (received from gql endpoint) to shopping Cart props
 */
export function adaptProductToCart(
  product: shoppingCart_item
): formatted_ShoppingCartProduct {
  const productType = product.product?.node.type;
  return productType === "SIMPLE"
    ? _adaptSimpleProduct(product)
    : _adaptVariableProduct(product);
}

function _adaptSimpleProduct(
  product: any
  // product: shoppingCart_item
): formatted_ShoppingCartProduct {
  const category = product.product?.node.productCategories?.nodes[0]
      .name as string,
    databaseId = product.product?.node.databaseId as number,
    id = product.product?.node.id as string,
    image = {
      sourceUrl: product.product?.node.image?.sourceUrl as string,
      altText: product.product?.node.image?.altText as string,
    },
    itemNumber = product.product?.node.databaseId as number,
    name = product.product?.node.name as string,
    productKey = product.key as string,
    quantity = product.quantity as number,
    stockQuantity = product.product?.node.stockQuantity as number,
    regularPrice = formatPrice(product.product?.node.regularPrice as string),
    slug = product.product?.node.slug as string,
    manualTotalPrice = formatPrice(
      `${product.product?.node.price * product.quantity}`
    ),
    salePrice = product.product?.node.salePrice
      ? formatPrice(product.product?.node.salePrice)
      : undefined;

  return {
    category,
    databaseId,
    id,
    image,
    itemNumber,
    name,
    productKey,
    quantity,
    regularPrice,
    slug,
    stockQuantity,
    manualTotalPrice,
    salePrice,
  };
}

function _adaptVariableProduct(
  item: any
  // item: shoppingCart_item
): formatted_ShoppingCartProduct {
  const productKey = item.key as string,
    quantity = item.quantity as number;

  // item.product.node properties
  const name = item.product?.node.name as string,
    id = item.product?.node.id as string,
    databaseId = item.product?.node.databaseId as number,
    category = item.product?.node.productCategories?.nodes[0].name as string,
    itemNumber = item.product?.node.databaseId as number,
    slug = item.product?.node.slug as string;

  //  item.variation.node  properties
  const stockQuantity = item.variation?.node.stockQuantity as number,
    variationId = item.variation?.node.databaseId as number,
    size = item.variation?.node.attributes?.nodes[0].value as string,
    image = {
      sourceUrl: item.product?.node.image?.sourceUrl as string,
      altText: item.product?.node.image?.altText as string,
    },
    manualTotalPrice = formatPrice(
      `${item.variation?.node.price * item.quantity}`
    ) as string,
    regularPrice = formatPrice(item.variation?.node.regularPrice as string),
    salePrice = item.variation?.node.salePrice
      ? formatPrice(item.variation?.node.salePrice)
      : undefined;

  return {
    category,
    databaseId,
    id,
    image,
    itemNumber,
    name,
    productKey,
    quantity,
    regularPrice,
    size,
    slug,
    stockQuantity,
    manualTotalPrice,
    variationId,
    salePrice,
  };
}

export function formatCartItems(
  items: shoppingCart_item[]
): formatted_ShoppingCartProduct[] {
  return items.map((product) => adaptProductToCart(product));
}
