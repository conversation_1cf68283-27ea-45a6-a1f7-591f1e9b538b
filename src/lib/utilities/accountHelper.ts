"use client";
import * as z from "zod";
import zxcvbn from "zxcvbn";

/**
 * @description
 * Validates a password using the zxcvbn library and adds an issue to the context if the password score is less than 4.
 * @param {string} val - The password to validate.
 * @param {z.RefinementCtx} ctx - The Zod refinement context.
 * @note Each message have different string just to re-render the page when
 *       the score of the message is different.
 */
export const validatePassword = (val: string, ctx: z.RefinementCtx) => {
  switch (zxcvbn(val).score) {
    case 0:
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: " ",
      });
      break;
    case 1:
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "  ",
      });
      break;
    case 2:
    case 3:
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "   ",
      });
      break;
  }
};
const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;

const phoneNumberRegex = /^(\+?\d+|)$/;

/** only the 'authenticated' boolean is stored in local storage when the user is authenticated  */
const isAuthenticated = () => {
  if (typeof window !== "undefined") {
    return Boolean(localStorage.getItem("authenticated") || false);
  } else return false;
};

export { emailRegex, isAuthenticated, phoneNumberRegex };
