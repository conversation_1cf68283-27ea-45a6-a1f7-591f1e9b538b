"server-only";

import { getAllBrandsPage } from "@lib/api";

export interface brand {
  name: string;
  slug: string;
  id: string;
  productsCount: number;
  alt: string;
  imageURL: string;
}

/**
 * @description for each initial, create an object with all corresponding brands and their counts
 * @example a: ['Adidas','Anton',...]
 */
export type groupedBrands = {
  [initial: string]: brand[];
};

export async function BrandData(): Promise<brand[]> {
  // brandsSliderData
  const response = await getAllBrandsPage();
  return response.data.brands.edges.map((wrapper: { node: any }) => {
    const brand = wrapper.node;
    const res = {
      alt: brand.name,
      name: brand.name,
      imageURL: brand.thumbnailUrl,
      slug: brand.slug,
      count: brand.count,
      id: brand.databaseId,
    };
    return res;
  });
}

/**
 *
 * @param brands - array of brand object
 * @description Alphabetically sorts an array of brands
 * @returns sorted array of brand object (in ascending order, with numbers appended to the end )
 */
function _sortBrands(brands: brand[]): brand[] {
  return brands.sort((a, b) => a.name.localeCompare(b.name));
}

/**
 *
 * @param brands - array of brand object
 * @description Alphabetically sorts an array of brands
 * @returns An object; each key is an initial (a,b,...z,0,1,..9) and its value is an array of brands that start with said initial
 */
function _groupBrands(brands: brand[]): groupedBrands {
  let result: groupedBrands = {};

  brands.map((brand) => {
    const initial = brand.name.charAt(0);
    const firstCharCode = initial.charCodeAt(0);
    let key;
    if (48 <= firstCharCode && firstCharCode <= 57) {
      key = "0-9";
    } else {
      key = initial.toLowerCase();
    }
    if (!result[key]) {
      result[key] = [];
    }
    result[key].push(brand);
  });
  return result;
}

/**
 *
 * @description sorts and groups brands
 */
export function formatBrands(brands: brand[]): groupedBrands {
  return _groupBrands(_sortBrands(brands));
}
