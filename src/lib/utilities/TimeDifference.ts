const currentDate = new Date();

const getTimeDifference = (timestamp: string) => {
  const postDate = new Date(timestamp);
  const differenceInMilliseconds = currentDate.getTime() - postDate.getTime();

  // Calculate the difference in days
  const differenceInSeconds = differenceInMilliseconds / 1000;
  const differenceInMinutes = differenceInSeconds / 60;
  const differenceInHours = differenceInMinutes / 60;
  const differenceInDays = differenceInHours / 24;
  const differenceInWeeks = differenceInDays / 7;
  const differenceInMonths = Math.abs(
    currentDate.getMonth() -
      postDate.getMonth() +
      12 * (currentDate.getFullYear() - postDate.getFullYear())
  );
  const differenceInYears = Math.abs(
    currentDate.getFullYear() - postDate.getFullYear()
  );

  // Check if the post date is more than 30 days ago
  if (differenceInYears > 0) {
    return `${differenceInYears} year${differenceInYears > 1 ? "s" : ""} ago`;
  } else if (differenceInMonths > 0) {
    return `${differenceInMonths} month${
      differenceInMonths > 1 ? "s" : ""
    } ago`;
  } else if (differenceInWeeks > 0) {
    return `${differenceInWeeks} week${differenceInWeeks > 1 ? "s" : ""} ago`;
  } else if (differenceInDays > 0) {
    return `${differenceInDays} day${differenceInDays > 1 ? "s" : ""} ago`;
  } else if (differenceInHours > 0) {
    return `${differenceInHours} hour${differenceInHours > 1 ? "s" : ""} ago`;
  } else if (differenceInMinutes > 0) {
    return `${differenceInMinutes} minute${
      differenceInMinutes > 1 ? "s" : ""
    } ago`;
  } else if (differenceInSeconds > 0) {
    return `${differenceInSeconds} second${
      differenceInSeconds > 1 ? "s" : ""
    } ago`;
  } else {
    return `Just now`;
  }
};

export default getTimeDifference;
