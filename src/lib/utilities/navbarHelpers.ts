import { brand } from "./brandsHelpers";

export interface navbarItem {
  name: "skate" | "streetwear" | "schuhe" | "marken" | "sale";
  href: string;
  columns?: {
    columnName: string;
    columnEntries: {
      name: string;
      url: string;
    }[];
  }[];
  hisAndHers?: { imageURL: string; title: string; link: string }[];
  brands?: brand[];
}
export const navbarEntries: navbarItem[] = [
  {
    name: "skate",
    href: "Skateboards",
    columns: [
      {
        columnName: "skateboards",
        columnEntries: [],
      },
      {
        columnName: "ZUBEHÖR",
        columnEntries: [],
      },
    ],
  },
  {
    name: "streetwear",
    href: "Textil",
    columns: [
      {
        columnName: "streetwear",
        columnEntries: [],
      },
      {
        columnName: "und mehr",
        columnEntries: [],
      },
    ],
    /*
    hisAndHers: [
      {
        imageURL:
          "https://plaze.k3.network/wp-content/uploads/2023/03/HY2728_Adidas_Skateboarding_Superfire-TK-Jacket_Black_plaze-skateshop_1.jpg",
        link: "Frauen",
        title: "Damen",
      },
      {
        imageURL:
          "https://plaze.k3.network/wp-content/uploads/2023/03/HY2728_Adidas_Skateboarding_Superfire-TK-Jacket_Black_plaze-skateshop_1.jpg",
        link: "Männer",
        title: "Herren",
      },
    ],
    */
  },
  {
    name: "schuhe",
    href: "Shoes",
    /*
    columns: [
      {
        columnName: "schuhe",
        columnEntries: [
          { name: "Sneakers High Top", url: "products_price_desc" },
          { name: "Sneakers Low Top", url: "products_price_asc" },
          { name: "Letzte Hinzufügung", url: "Publication_date" },
          { name: "Namen", url: "Item_name" },
        ],
      },
    ],
    */
  },
  {
    name: "marken",
    href: "alle-marken",
    columns: [
      {
        columnName: "UNSERE TOP MARKEN",
        columnEntries: [],
      },
    ],
  },
];
