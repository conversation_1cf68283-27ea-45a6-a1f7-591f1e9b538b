import { getAccessories, getFirst<PERSON><PERSON><PERSON>, getLastTextil, getSkateboards } from "@lib/api";

export interface skate {
    name: string;
  }

export type groupedskates = {
    [initial: string]: skate[];
  };

  export interface Accessorie {
    name: string;
  }

export type groupedAccessories = {
    [initial: string]: Accessorie[];
  };

  export interface FirstTextil {
    name: string;
  }

export type groupedFirstTextil = {
    [initial: string]: FirstTextil[];
  };


  export interface LastTextil {
    name: string;
  }

export type groupedLastTextil = {
    [initial: string]: LastTextil[];
  };



export async function SkateboardsData(): Promise<skate[]> {
    // brandsSliderData
    const response = await getSkateboards();
    return response.data.category.children.edges
      .map((wrapper: { node: any }) => {
        const Skate = wrapper.node;
        const res = {
          name: Skate.name,
        };
        return res;
      })
  }

  export async function AccessoriesData(): Promise<Accessorie[]> {
    // brandsSliderData
    const response = await getAccessories();
    return response.data.category.children.edges
      .map((wrapper: { node: any }) => {
        const Accessorie = wrapper.node;
        const res = {
          name: Accessorie.name,
        };
        return res;
      })
  }

  export async function FirstTextilData(): Promise<FirstTextil[]> {
    // brandsSliderData
    const response = await getFirstTextil();
    return response.data.category.children.edges
      .map((wrapper: { node: any }) => {
        const Accessorie = wrapper.node;
        const res = {
          name: Accessorie.name,
        };
        return res;
      })
  }

  export async function LastTextilData(): Promise<LastTextil[]> {
    // brandsSliderData
    const response = await getLastTextil();
    return response.data.category.children.edges
      .map((wrapper: { node: any }) => {
        const Accessorie = wrapper.node;
        const res = {
          name: Accessorie.name,
        };
        return res;
      })
  }