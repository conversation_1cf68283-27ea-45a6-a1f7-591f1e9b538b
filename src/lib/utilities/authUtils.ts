import {
  authenticationIsOptional_OPERATIONS,
  authenticationIsRequired_OPERATIONS,
  authErrors,
  tokenKeys,
} from "@constants/authConstants";
import { GraphQLClient } from "graphql-request";
import Cookies from "js-cookie";
import { redirect } from "next/navigation";

import { RefreshJwtAuthTokenDocument } from "../../graphql/mutations/refreshJWTAuth.token.graphql.interface";
import { GetSessionTokenDocument } from "../../graphql/queries/getSession.token.graphql.interface";

// # -------------- Begin SESSION TOKEN Utils -------------------- # //

// ! session token is stored on localStorage and not on SessionStorage so it persists between user sessions  (and across multiple tabs)
// ? this is also useful for when the user is not an authenticated user but an "anonymous" visitor

export const setSessionToken = (sessionToken: string) => {
  localStorage.setItem(tokenKeys.SESSION_TOKEN_LS_KEY, sessionToken);
};
/** @description reads session token from local storage */
export function readSessionToken() {
  const token = localStorage.getItem(tokenKeys.SESSION_TOKEN_LS_KEY);
  return token;
}

/** @description attempts to fetch session token from WordPress */
export async function fetchSessionToken() {
  let sessionToken;

  const graphQLClient = new GraphQLClient(
    process.env.NEXT_PUBLIC_GRAPHQL_URI as string
  );

  const sessionTokenResult = await graphQLClient.request(
    GetSessionTokenDocument
  );

  sessionToken = sessionTokenResult.customer?.sessionToken ?? "no-token";

  return sessionToken;
}

/**
 * @async
 *  @description returns session token from localStorage
 * @IMPORTANT on successful fetch the token is stored to localStorage
 *
 *
 * OR
 *
 * attempts to fetch  a new session token from WordPress if not is available in localStorage
 *
 * @argument forceFetch  - fetch session token even if there's one in localStorage
 * @default forceFetch = false
 */
export async function getSessionToken(forceFetch = false) {
  let sessionToken: string | null | undefined = readSessionToken();
  if (!sessionToken || forceFetch) {
    sessionToken = await fetchSessionToken();
    setSessionToken(sessionToken);
  }
  return sessionToken;
}

// # -------------- End SESSION TOKEN Utils -------------------- # //

// # -------------- Begin AUTH TOKEN Utils --------------------- # //

let tokenSetter: string | number | NodeJS.Timeout | undefined,
  authToken: string | null | undefined;

//// ! auth token is stored on sessionStorage and not on localStorage since each session has its own jwt authorization token

export const setAuthToken = (authToken: string | null) => {
  localStorage.setItem(tokenKeys.AUTH_TOKEN_SS_KEY, authToken ?? "");
};
/** @description reads auth token from session storage */
export function readAuthToken() {
  const authToken = localStorage.getItem(tokenKeys.AUTH_TOKEN_SS_KEY as string);
  return authToken;
}

/** @description attempts to fetch auth token from WordPress */
async function fetchAuthToken() {
  const jwtRefreshToken = readRefreshToken();
  if (!jwtRefreshToken) {
    throw new Error(authErrors.MISSING_REFRESH_TOKEN);
  }
  try {
    const graphQLClient = new GraphQLClient(
      process.env.NEXT_PUBLIC_GRAPHQL_URI as string
    );
    const results = await graphQLClient.request(RefreshJwtAuthTokenDocument, {
      jwtRefreshToken,
    });
    authToken = results.refreshJwtAuthToken?.authToken;
    //   custom line added for ts check
    if (!authToken) {
      throw new Error("A------Failed to refresh auth token");
    }
  } catch (err) {
    throw err;
  }

  // if (tokenSetter) {
  //   clearInterval(tokenSetter);
  // }
  // // TODO check other possibility [fetch authToken only when it expires]
  // tokenSetter = setInterval(async () => {
  //   if (!hasCredentials()) {
  //     clearInterval(tokenSetter);
  //     return;
  //   }
  //   fetchAuthToken();
  // }, Number(tokenKeys.AUTH_KEY_TIMEOUT || 30000));

  return authToken;
}

/**
 *  @async might invoke graphql Mutation to fetch authToken
 *  @description * returns auth token from sessionStorage
 * * attempts to fetch  a new auth token from WordPress if not available in sessionStorage
 */
export async function getAuthToken() {
  let authToken = readAuthToken();
  if (!authToken || !tokenSetter) {
    const fetchedAuthToken = await fetchAuthToken();

    authToken = fetchedAuthToken ?? null;
    setAuthToken(authToken);
  }

  return authToken;
}
// # -------------- End AUTH TOKEN Utils ----------------------- # //

// # -------------- Begin REFRESH TOKEN Utils ------------------ # //
export const setRefreshToken = (refreshToken: string) => {
  localStorage.setItem(tokenKeys.REFRESH_TOKEN_LS_KEY, refreshToken);
};

/** @description reads auth token from session storage */
export function readRefreshToken() {
  const localRefreshToken = localStorage.getItem(
    tokenKeys.REFRESH_TOKEN_LS_KEY
  );
  return localRefreshToken;
}
// # -------------- End REFRESH TOKEN Utils -------------------- # //

// # -------------- Begin REFRESH TOKEN Utils ------------------ # //
export const setExpirationTimeStamp = (refreshToken: string) => {
  localStorage.setItem(tokenKeys.EXPIRATION_DATE, refreshToken);
};

/** @description reads auth token from session storage */
export function readExpirationTimeStamp() {
  const localRefreshToken = localStorage.getItem(tokenKeys.EXPIRATION_DATE);
  return localRefreshToken;
}
// # -------------- End REFRESH TOKEN Utils -------------------- # //

// # -------------- Start AUTH COOKIE Utils --------------------- # //

/**
 * @description sets Authentication cookie to "true"
 * @important authToken was not used since it's decoded information includes sensitive information and this cookie is not an HTTPOnly Cookie
 */
export const setAuthCookie = () => {
  Cookies.set(tokenKeys.AUTH_COOKIE, "true");
};

export const getAuthCookie = () => {
  return Cookies.get(tokenKeys.AUTH_COOKIE);
};

export const removeAuthCookie = () => {
  Cookies.remove(tokenKeys.AUTH_COOKIE);
};
// # -------------- End AUTH COOKIE Utils --------------------- # //

// # -------------- Start Credentials Utils --------------------- # //

/**
 * @abstract checks if the user is authenticated
 * @description checks if refreshToken exists
 * @returns  boolean
 */
export function hasCredentials() {
  const refreshToken = localStorage.getItem(tokenKeys.REFRESH_TOKEN_LS_KEY);

  return !!refreshToken;
}

/**
 *  @description saves all received credentials to proper location (localStorage - sessionStorage)
 * @saves credentials:
 *  * authToken
 *  * sessionToken
 *  * refreshToken
 */
export async function saveCredentials({
  authToken,
  sessionToken,
  refreshToken,
  tokenExpirationTimeStamp,
}: {
  authToken: string;
  sessionToken: string;
  refreshToken: string;
  tokenExpirationTimeStamp: string;
}) {
  if (!authToken || !sessionToken || !refreshToken) {
    throw new Error(`error in Saving Credentials:
    authToken = ${authToken}
    sessionToken = ${sessionToken}
    refreshToken = ${refreshToken}
    `);
  }
  setSessionToken(sessionToken);
  setAuthToken(authToken);
  setRefreshToken(refreshToken);
  setExpirationTimeStamp(tokenExpirationTimeStamp);
  setAuthCookie();
}
/**
 *  @description saves all received credentials to proper location (localStorage - sessionStorage)
 * @removes credentials:
 *  * authToken
 *  * sessionToken
 *  * refreshToken
 *  * isAuthenticated (cookie)
 */
export function clearCredentials() {
  localStorage.removeItem(tokenKeys.REFRESH_TOKEN_LS_KEY);
  localStorage.removeItem(tokenKeys.SESSION_TOKEN_LS_KEY);
  localStorage.removeItem(tokenKeys.AUTH_TOKEN_SS_KEY);
  localStorage.removeItem(tokenKeys.EXPIRATION_DATE);
  removeAuthCookie();
}
// # -------------- End Credentials Utils ----------------------- # //

/**
 * @description checks if the GraphQL operation needs a valid WJT auth Token to work properly
 * * returns `true` if the operation requires the token
 * * returns `false` if the operation does not require the token
 */
export function requiresAuthorization(operationName: string) {
  const authentication_is_required = authenticationIsRequired_OPERATIONS
    .map((operation) => operation.toLowerCase())
    .includes(operationName.toLowerCase());

  if (authentication_is_required) {
    try {
      const expirationTimeStamp = readExpirationTimeStamp();
      if (!expirationTimeStamp) {
        throw new Error("No expiration time");
      }
      if (!isTokenValid(+expirationTimeStamp)) {
        // I used the .then approach cause the RequiresAuthorization is used in other places that can't be refactored to use await with it so the fastest solution is to use the callback approach
        fetchAuthToken()
          .then((res) => {
            setAuthToken(res);
            setExpirationTimeStamp(`${generateFutureTimeStamp()}`);
          })
          .catch((err) => {
            // Throw the error to catch it in the main catch block
            throw err;
          });
      }
    } catch (err) {
      // errors can occur in the following cases:
      // 1- No refresh token is stored so the user must log in.
      // 2- no expiration time for the token which is a faulty behavior
      // 3- fetching the token by the refresh token throws an error
      // in both cases the user credentials should be cleared and redirected back to the login page
      clearCredentials();
      redirect("/login");
    }
  }
  const authentication_is_optional = authenticationIsOptional_OPERATIONS
    .map((operation) => operation.toLowerCase())
    .includes(operationName.toLowerCase());

  return authentication_is_required
    ? true
    : authentication_is_optional
    ? !!getAuthCookie()
    : false;
}

//------------------ Helper functions related to the refreshToken solution --------------
/**
 *
 * @param expirationTimestamp number an expiration timestamp in seconds
 * @returns boolean
 * A utility function that takes the expiration date for the JWT and returns false if the token is expired or it has less than a minute remaining
 * otherwise the function returns true
 * I added more than a minute check for cases like the user has 30 seconds in his token so we won't disturb him during his work it's just an extra level for safety
 */
const isTokenValid = (expirationTimestamp: number) => {
  const currentTimestamp = Date.now() / 1000;

  // Add 1 minute  to counter for some seconds delays
  const tokenExpirationThreshold = 60;

  const timeRemaining = expirationTimestamp - currentTimestamp;

  // Check if token has more than 1 minute left
  return timeRemaining > tokenExpirationThreshold;
};

/**
 *
 * @param minutesAfter number how many minutes in the future you the date to be in
 * @returns number the timeStamp for the future date in seconds
 * calculate the time stamp for some time in the future based on the number of minutes passed as arguments
 */
const generateFutureTimeStamp = (minutesAfter = 5) => {
  const currentTime = new Date();
  // Add 5 minutes to the current time
  // 5 minutes * 60000 milliseconds per minute
  const laterTime = new Date(currentTime.getTime() + minutesAfter * 60000);

  // The backend returns the expiry date in seconds not milliseconds to maintain Consistency we convert the result to seconds
  return Math.floor(laterTime.getTime() / 1000);
};
