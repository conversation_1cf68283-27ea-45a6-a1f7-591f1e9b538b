import { unstable_cache } from "next/cache";

import { BrandData } from "./brandsHelpers";
import {
  AccessoriesData,
  FirstTextilData,
  LastTextilData,
  SkateboardsData,
} from "./categoryHelper";
import { navbarEntries as data } from "./navbarHelpers";

const getCachedBrandData = unstable_cache(
  async () => {
    const brands = await BrandData();
    return brands;
  },
  ["brand-data"], // More specific cache key
  {
    revalidate: 3600,
    tags: ["brand"], // Add cache tags for better invalidation control
  }
);

const getCachedSkateData = unstable_cache(
  async () => {
    const Skate = await SkateboardsData();
    return Skate;
  },
  ["skate-data"], // More specific cache key
  {
    revalidate: 3600,
    tags: ["skate"], // Add cache tags for better invalidation control
  }
);

const getCachedAccessoriesData = unstable_cache(
  async () => {
    const Accessories = await AccessoriesData();
    return Accessories;
  },
  ["accessories-data"], // More specific cache key
  {
    revalidate: 3600,
    tags: ["accessories"], // Add cache tags for better invalidation control
  }
);

const getCachedFirstTextilData = unstable_cache(
  async () => {
    const FirstTextil = await FirstTextilData();
    return FirstTextil;
  },
  ["first-textil-data"], // More specific cache key
  {
    revalidate: 3600,
    tags: ["first-textil"], // Add cache tags for better invalidation control
  }
);

const getCachedLastTextilData = unstable_cache(
  async () => {
    const LastTextil = await LastTextilData();
    return LastTextil;
  },
  ["last-textil-data"], // More specific cache key
  {
    revalidate: 3600,
    tags: ["last-textil"], // Add cache tags for better invalidation control
  }
);

export const getNavbarEntries = unstable_cache(
  async () => {
    const navbarEntries = data;
    const markenIndex = navbarEntries.findIndex(
      (item) => item.name === "marken"
    );
    if (markenIndex !== -1) {
      const brands = await getCachedBrandData();
      const brandColumns = [];
      const columnSize = 6;
      const totalBrands = 18;

      for (let i = 0; i < totalBrands; i += columnSize) {
        const columnEntries = brands.slice(i, i + columnSize).map((brand) => ({
          name: brand.name,
          url: brand.name,
        }));
        const columnName = i === 0 ? "UNSERE TOP MARKEN" : ` `;
        brandColumns.push({
          columnName: columnName,
          columnEntries: columnEntries,
        });
      }
      navbarEntries[markenIndex].columns = brandColumns;
    }

    const skateboardIndex = navbarEntries.findIndex(
      (item) => item.name === "skate"
    );
    if (skateboardIndex !== -1) {
      const Skate = await getCachedSkateData();
      const accessories = await getCachedAccessoriesData();

      const columnSize = 5;
      const totalSkate = 5;
      const totalAccessories = 5;
      const SkateColumns = [];
      const AccessoriesColumns = [];

      for (let i = 0; i < totalSkate; i += columnSize) {
        const columnEntries = Skate.slice(i, i + columnSize).map(
          (skateItem) => ({
            name: skateItem.name,
            url: skateItem.name,
          })
        );
        SkateColumns.push({
          columnName: "skateboards",
          columnEntries: columnEntries,
        });
      }

      for (let i = 0; i < totalAccessories; i += columnSize) {
        const columnEntries = accessories
          .slice(i, i + columnSize)
          .map((accessoryItem) => ({
            name: accessoryItem.name,
            url: accessoryItem.name,
          }));
        AccessoriesColumns.push({
          columnName: "ZUBEHÖR",
          columnEntries: columnEntries,
        });
      }
      navbarEntries[skateboardIndex].columns = [
        ...SkateColumns,
        ...AccessoriesColumns,
      ];
    }

    const streetwearIndex = navbarEntries.findIndex(
      (item) => item.name === "streetwear"
    );
    if (streetwearIndex !== -1) {
      const firstTextil = await getCachedFirstTextilData();
      const lastTextil = await getCachedLastTextilData();

      const columnSize = 9;
      const totalFirstTextil = 9;
      const totalLastTextil = 4;
      const FirstTextilColumns = [];
      const LastTextilColumns = [];

      for (let i = 0; i < totalFirstTextil; i += columnSize) {
        const columnEntries = firstTextil
          .slice(i, i + columnSize)
          .map((TextilItem) => ({
            name: TextilItem.name,
            url: TextilItem.name,
          }));
        FirstTextilColumns.push({
          columnName: "streetwear",
          columnEntries: columnEntries,
        });
      }

      for (let i = 0; i < totalLastTextil; i += columnSize) {
        const columnEntries = lastTextil
          .slice(i, i + columnSize)
          .map((TextilItem) => ({
            name: TextilItem.name,
            url: TextilItem.name,
          }));
        LastTextilColumns.push({
          columnName: "und mehr",
          columnEntries: columnEntries,
        });
      }
      navbarEntries[streetwearIndex].columns = [
        ...FirstTextilColumns,
        ...LastTextilColumns,
      ];
    }
    return navbarEntries;
  },
  ["navbar-entries"], // More specific cache key
  {
    revalidate: 3600,
    tags: ["navbar"], // Add cache tags for better invalidation control
  }
);
