import { getProductVariations } from "@lib/api/woocommerce";
import { eq } from "drizzle-orm";
import { keys, values } from "lodash";
import { unstable_cache } from "next/cache";

import { db } from "../../syncDb/db";
import { productsTable, variationsTable } from "../../syncDb/schema";

export type formattedVariation = {
  id: number;
  name: string;
  price: number;
  regularPrice: number | null;
  salePrice: number | null;
  stockQuantity: number;
  stockStatus: string;
  unit: string;
  unitValue: string;
};

export type formattedProduct = {
  id: number;
  slug: string;
  name: string;
  description: string;
  galleryImages: string[];
  brands: { id: number; name: string; slug: string; thumbnailUrl?: string }[];
  dropItem: boolean;
  productCategories: string[];
  stockQuantity: number;
  stockStatus: "outofstock" | "instock";
  price: number;
  regularPrice: number | null;
  salePrice: number | null;
  variations: number;
  fuer: string | null;
};

const slugify = (str: string) =>
  str
    .toLowerCase()
    .replace(" ", "-")
    .replace("'", "")
    .replace('"', "")
    .replace(".", "-")
    .replace(",", "")
    .replace("ä", "ae")
    .replace("ü", "ue")
    .replace("ö", "oe");

export async function formatProductNew(
  slug: string
): Promise<formattedProduct | null> {
  const productData = await db.query.productsTable.findFirst({
    where: eq(productsTable.slug, slug),
  });

  const variation = productData?.id
    ? await db.query.variationsTable.findFirst({
        where: eq(variationsTable.parent, productData.id),
      })
    : null;

  if (!productData || !productData.slug) return null;

  return {
    id: productData.id,
    slug: productData.slug,
    name: productData.name,
    description: productData.description ?? "",
    galleryImages:
      (JSON.parse(productData.images as string) as string[] | null)?.map(
        (img: string) => img
      ) ?? [],
    brands:
      (JSON.parse(productData.brands as string) as {
        id: number;
        name: string;
        slug: string;
        thumbnailUrl?: string;
      }[]) ?? [],
    dropItem: false, //TODO: add drop item to DB
    productCategories: (
      JSON.parse(productData.categories as string) as {
        id: number;
        name: string;
        slug: string;
      }[]
    )?.map((cat) => cat.name),
    stockQuantity: productData.stockQuantity ?? 0,
    stockStatus:
      (productData.stockQuantity ?? 0) > 0 ? "instock" : "outofstock",
    price: parseFloat(
      (productData.salePrice && productData.salePrice !== "0"
        ? productData.salePrice
        : productData.regularPrice) ??
        (!!variation
          ? variation.salePrice && variation.salePrice !== "0"
            ? variation.salePrice
            : variation.regularPrice
          : "0")
    ),
    regularPrice:
      parseFloat(productData.regularPrice ?? "0") === 0
        ? null
        : parseFloat(productData.regularPrice ?? "0"),
    salePrice:
      parseFloat(productData.salePrice ?? "0") === 0
        ? null
        : parseFloat(productData.salePrice ?? "0"),
    variations: productData.type === "variable" ? 1 : 0,
    fuer: productData.fuer ? slugify(productData.fuer) : null,
  };
}

export async function formatVariations(id: number) {
  const cachedData = await unstable_cache(
    async () => {
      const variations = await getProductVariations(id);

      return variations?.map((variation) => {
        return {
          ...variation,
          unit: keys(variation.attributes)[0],
          unitValue: values(values(variation.attributes)[0].option)[0],
          unitValueSlug: keys(values(variation.attributes)[0].option)[0],
        };
      });
    },
    ["variations", id.toString()],
    {
      revalidate: 3600,
    }
  )();

  return cachedData;
}

export async function newFormatVariations(id: number) {
  const variations = await db.query.variationsTable.findMany({
    where: eq(variationsTable.parent, id),
  });

  return variations?.map((variation) => {
    const salePrice = parseFloat(variation.salePrice ?? "0") * 100;
    const regularPrice = parseFloat(variation.regularPrice ?? "0") * 100;
    return {
      unit:
        variation.unit && variation.unit !== ""
          ? `attribute_${variation.unit}`
          : "",
      unitValue: variation.unitValue ?? "",
      unitValueSlug: variation.unitValue ? slugify(variation.unitValue) : "", //TODO: add Unit Value Slug to DB
      id: variation.id,
      parent_id: variation.parent,
      name: variation.name,
      slug: "",
      permalink: "",
      sku: variation.sku,
      description: "",
      dates: {
        created: "",
        modified: "",
        created_gmt: "",
        modified_gmt: "",
      },
      featured: false,
      prices: {
        price: salePrice !== 0 ? String(salePrice) : String(regularPrice),
        regular_price: String(regularPrice),
        sale_price: salePrice === 0 ? "" : String(salePrice),
        price_range: [],
        on_sale: salePrice !== 0 && salePrice < regularPrice,
        date_on_sale: "",
        currency: {
          currency_code: "",
          currency_symbol: "",
          currency_symbol_pos: "",
          currency_minor_unit: 2,
          currency_decimal_separator: "",
          currency_thousand_separator: "",
          currency_prefix: "",
          currency_suffix: "",
        },
      },
      hidden_conditions: {
        virtual: false,
        downloadable: false,
        manage_stock: true,
        sold_individually: true,
        shipping_required: true,
      },
      images: [],
      categories: [],
      tags: [],
      attributes: {},
      stock: {
        is_in_stock: !!(variation.stockQuantity && variation.stockQuantity > 0),
        stock_quantity: variation.stockQuantity ?? 0,
        stock_status:
          variation.stockQuantity && variation.stockQuantity > 0
            ? "instock"
            : "outofstock",
        backorders: "no",
        backorders_allowed: false,
        backordered: false,
        low_stock_amount: "",
      },
      weight: { value: "", unit: "" },
      dimensions: { length: "", width: "", height: "", unit: "" },
      total_sales: "",
      add_to_cart: {
        is_purchasable: !!(
          variation.stockQuantity && variation.stockQuantity > 0
        ),
        purchase_quantity: {
          min_purchase: 1,
          max_purchase: variation.stockQuantity ?? 0,
        },
        rest_url: "string",
      },
      meta_data: [],
      _links: {},
    } satisfies FormatVariationsType[number];
  });
}

export type FormatVariationsType = Awaited<ReturnType<typeof formatVariations>>;
