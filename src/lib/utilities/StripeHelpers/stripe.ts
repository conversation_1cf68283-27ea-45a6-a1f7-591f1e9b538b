import type { stripeSessionResponse } from "@typesDeclarations/stripeTypes";
import { Maybe } from "graphql/jsutils/Maybe";

interface checkOutSessionProps {
  orderDataPayload: any;
  lineItems: any;
}
/**
 * @description generate a stripeSession object that has the `session ID` and `session URL`
 * @important prior to generating the stripeSession, this function also submits a new order to WooCommerce to retrieve its ID (the order ID is required to generate stripeSession)
 */
export default async function createCheckoutSession({
  lineItems,
  orderDataPayload,
}: checkOutSessionProps): Promise<Maybe<stripeSessionResponse>> {
  try {
    const checkoutSessionResponse = await fetch("/checkout_session", {
      method: "POST",
      mode: "cors",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ orderDataPayload, lineItems }),
    });

    const stripeSession: stripeSessionResponse =
      await checkoutSessionResponse.json();
    return stripeSession;
  } catch (error) {
    return null;
  }
}
