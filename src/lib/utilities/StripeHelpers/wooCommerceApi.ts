const WooCommerceRestApi = require("@woocommerce/woocommerce-rest-api").default;

// initialize the WooCommerceRestApi //
const wooCommerceAPI = new WooCommerceRestApi({
  url: process.env.WORDPRESS_URL!,
  consumerKey: process.env.WOOCOMMERCE_KEY!,
  consumerSecret: process.env.WOOCOMMERCE_SECRET!,
  version: "wc/v3",
});

// fetch all products from WooCommerce //
export async function fetchWooCommerceProducts() {
  try {
    const response = await wooCommerceAPI.get("products");
    return response;
  } catch (error) {
    throw new Error(error as string);
  }
}

/** get wooCommerce order by ID */
export async function getWooCommerceOrder({ orderId }: { orderId: number }) {
  const response = await wooCommerceAPI.get(`orders/${orderId}`);
  console.log("response");
  console.log(response);
}
// create new WooCommerce order by passing in required data object //
export async function createWooCommerceOrder(data: any) {
  try {
    const response = await wooCommerceAPI.post("orders", data);

    return response;
  } catch (error: any) {
    // Invalid request, for 4xx and 5xx statuses
    // console.log("error in res of orders:");
    // console.log(error);
    // console.log("Response Status:", error.response.status);
    // console.log("Response Headers:", error.response.headers);
    // console.log("Response Data:", error.response.data);
    throw new Error(error);
  }
}

export async function updateWooCommerceOrder(data: any, orderId: number) {
  try {
    const updatedData = {
      shipping: {
        first_name: data.firstName ?? undefined,
        last_name: data.lastName ?? undefined,
        address_1: data.line1 ?? undefined,
        address_2: data.line2 ?? undefined,
        city: data.city ?? undefined,
        state: data.state ?? undefined,
        postcode: data.postal_code ?? undefined,
        country: data.country ?? undefined,
      },
    };

    const response = await wooCommerceAPI.put(`orders/${orderId}`, updatedData);

    return response;
  } catch (error: any) {
    // Invalid request, for 4xx and 5xx statuses
    // console.log("error in res of orders:");
    // console.log(error);
    // console.log("Response Status:", error.response.status);
    // console.log("Response Headers:", error.response.headers);
    // console.log("Response Data:", error.response.data);
    throw new Error(error);
  }
}

export async function deleteWooCommerceOrder(orderId: number) {
  try {
    const response = await wooCommerceAPI.delete(`orders/${orderId}`, {
      force: true,
    });

    return response;
  } catch (error: any) {
    // Invalid request, for 4xx and 5xx statuses
    // console.log("error in res of orders:");
    // console.log(error);
    // console.log("Response Status:", error.response.status);
    // console.log("Response Headers:", error.response.headers);
    // console.log("Response Data:", error.response.data);
    throw new Error(error);
  }
}

export async function getAllOrders() {
  try {
    const res = await wooCommerceAPI.get("orders");
    return res;
  } catch (error: any) {
    console.error(error);
    throw new Error(error as string);
  }
}

export async function retrieveProductById(productId: string) {
  try {
    const response = await wooCommerceAPI.get(`products/${productId}`);
    return response.data;
  } catch (error) {
    throw new Error(error as string);
  }
}
