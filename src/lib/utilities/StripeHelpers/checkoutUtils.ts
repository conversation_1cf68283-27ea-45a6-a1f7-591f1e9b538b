import { STRIPE } from "@constants/sessionConstants";

import { FetchedShippingMethod } from "../../../hooks/graphql/queries/cartHooks";

/** @description stores selected shippingMethod to Session Storage  */
const SS_setShippingMethod = (shippingMethodDetails: FetchedShippingMethod) => {
  sessionStorage.setItem(
    STRIPE.PAYMENT_METHOD_PRICE,
    JSON.stringify(shippingMethodDetails)
  );
};

/** @description stores selected shippingMethod to Session Storage  */
const SS_getShippingMethod = (): FetchedShippingMethod => {
  return JSON.parse(
    sessionStorage.getItem(STRIPE.PAYMENT_METHOD_PRICE) as string
  );
};

/** @description clears selected shopping method after the users completes checking-out */
const SS_clearShoppingMethod = () => {
  sessionStorage.removeItem(STRIPE.PAYMENT_METHOD_PRICE);
};

/**
 * @description this functions adds the shipping method's price and **accumulate** taxes
 * @notice this logic accounts for an unexpected behavior on WooCommerce. disabling taxes from the `wooCommerce/settings/shipping` doesn't seem to seem remove the tax from the order-creation. This way will make sure the user sees what they pay for without any unexpected taxes
 */
const convertPriceToFrontEnd = ({
  price,
  taxes,
}: {
  price: string;
  taxes: Array<string>;
}) => {
  const totalTax = taxes.reduce((accumulator: number, currentValue: string) => {
    const parsedValue = parseFloat(currentValue);
    if (!isNaN(parsedValue)) {
      return accumulator + parsedValue;
    }
    return accumulator;
  }, 0);

  const shippingPrice = parseFloat(price);
  return totalTax + shippingPrice;
};

const convertPriceToWoocommerceOrder = ({
  price,
  taxes,
}: {
  price: string;
  taxes: Array<string>;
}) => {
  const totalTax = taxes.reduce((accumulator: number, currentValue: string) => {
    const parsedValue = parseFloat(currentValue);
    if (!isNaN(parsedValue)) {
      return accumulator + parsedValue;
    }
    return accumulator;
  }, 0);

  const shippingPrice = parseFloat(price);
  return ((totalTax + shippingPrice) / 1.19).toFixed(2);
};

export {
  convertPriceToFrontEnd,
  convertPriceToWoocommerceOrder,
  SS_clearShoppingMethod,
  SS_getShippingMethod,
  SS_setShippingMethod,
};
