import {
  getBrands,
  getInstagramPost,
  getProducts,
  getProductsByCategoryID,
} from "@lib/api";
import { getPriceFormatter } from "@lib/utils";
import type {
  acf_bannerBlock,
  acf_textAreaBlock,
  bannerData,
  brandsSliderData,
  categoryProductsSliderData,
  heroGridData,
  homePageDataResponse,
  homePageGraphQLResponse,
  imageDisplay,
  instagramPostsData,
  plaze_brandSliderPlugin,
  plaze_grid,
  plaze_grid_card,
  plaze_grid_slider,
  plaze_grid_video,
  plaze_instagramFeed,
  plaze_productSlider,
  productOfCategory,
  textBlockData,
} from "@typesDeclarations/homePageTypes";

const { deutschFormatter: formatPrice } = getPriceFormatter(true, false);

export async function processHomePageData(
  fetchedData: homePageGraphQLResponse["blocks"]
): Promise<any> {
  // :processedHomePageData
  const rawGridData = fetchedData.find(
    (section): section is plaze_grid => section.name === "plaze/grid"
  );

  const rawBannerData = fetchedData.find(
    (section): section is acf_bannerBlock => section.name === "acf/bannerblock"
  );

  const rawTextArea = fetchedData.filter(
    (section): section is acf_textAreaBlock =>
      section.name === "acf/textareablock"
  );
  const rawInstagramFeed = fetchedData.find(
    (section): section is plaze_instagramFeed =>
      section.name === "plaze/instagram-feed"
  );
  const rawBrandSlider = fetchedData.find(
    (section): section is plaze_brandSliderPlugin =>
      section.name === "plaze/brand-slider-plugin"
  );
  const rawCategoriesSliderData = fetchedData.filter(
    (section): section is plaze_productSlider =>
      section.name === "plaze/product-slider"
  );
  if (!rawGridData || !rawBannerData)
    throw new Error("Unable to retrieve Home Page Data");
  return {
    banner: rawBannerData && _processBannerData(rawBannerData),
    HeroGrid: rawGridData && _processHeroGridData(rawGridData),
    categoriesProductsSlider: await Promise.all(
      rawCategoriesSliderData.map((rawCategorySliderData) =>
        _processCategorySliderData(rawCategorySliderData)
      )
    ),
    brandsSlider: rawBrandSlider && (await _processBrandSliderPluginData()),

    textBlockData: rawTextArea.map((textBlock) =>
      _processTextAreaBlockData(textBlock)
    ),

    InstagramFeed:
      rawInstagramFeed && (await _processInstagramFeedAPI(rawInstagramFeed)),
  };
}

/** @description process fetch response into proper Banner object */
export function _processBannerData(
  data: homePageDataResponse["bannerBlockResponse"]
): bannerData {
  return data.attributes.data.banner_navbar;
}

/** @description process fetch response into proper HeroGrid object, taking care of separating response data and handling all 5 types of HeroGrid layout */
export function _processHeroGridData(
  data: homePageDataResponse["gridResponse"]
): heroGridData {
  const { innerBlocks, attributes } = data;

  let res: heroGridData;

  const type = Array.isArray(attributes) ? 0 : attributes?.layout ?? 0;

  switch (type) {
    case 0: {
      // Slider - Card - Card - Card
      const blocks = innerBlocks as Array<plaze_grid_slider | plaze_grid_card>;
      // extracting grid Slider Data
      const heroGrid_SliderData = blocks.find(
        (section): section is plaze_grid_slider =>
          section.name === "plaze/grid-slider"
      );
      if (!heroGrid_SliderData)
        throw new Error(
          "Bei der Verarbeitung von HeroGrid Slider-Daten mit dem Layouttyp ist ein Fehler aufgetreten = 0 (Standardtyp)"
        );

      const sliderData = {
        text:
          !heroGrid_SliderData.attributes?.text ||
          heroGrid_SliderData.attributes.text === ""
            ? null
            : heroGrid_SliderData.attributes.text,
        url:
          !heroGrid_SliderData.attributes?.url ||
          heroGrid_SliderData.attributes.url === ""
            ? null
            : heroGrid_SliderData.attributes.url,
        media: heroGrid_SliderData.attributes.media.map((entry) => {
          return {
            alt: entry.text,
            imageURL: entry.mediaURL,
            name: entry.text,
            slug: entry.url,
          };
        }),
      };

      // extracting categories data
      const heroGrid_categoriesData = blocks.filter(
        (entry): entry is plaze_grid_card => entry.name === "plaze/grid-card"
      );

      const categoriesData = heroGrid_categoriesData.map((entry) => {
        return {
          mediaURL: entry.attributes.media.mediaURL,
          mediaID: entry.attributes.media.mediaID,
          text:
            !entry.attributes?.text || entry.attributes.text === ""
              ? null
              : entry.attributes.text,
          slug:
            !entry.attributes?.url || entry.attributes.url === ""
              ? null
              : entry.attributes.url,
        };
      });
      const categories: imageDisplay[] = categoriesData.map((entry) => {
        return {
          alt: entry.text,
          name: entry.text,
          imageURL: entry.mediaURL,
          slug: entry.slug,
        };
      });
      res = {
        type: 0,
        sliderData,
        categories,
      };
      break;
    }
    case 1: {
      // Slider - Card - Card
      const blocks = innerBlocks as Array<plaze_grid_slider | plaze_grid_card>;

      // extracting grid Slider Data
      const heroGrid_SliderData = blocks.find(
        (section): section is plaze_grid_slider =>
          section.name === "plaze/grid-slider"
      );
      if (!heroGrid_SliderData)
        throw new Error(
          "something went wrong while processing HeroGrid Slider data with layout type 1"
        );

      const sliderData = {
        text:
          !heroGrid_SliderData.attributes?.text ||
          heroGrid_SliderData.attributes.text === ""
            ? null
            : heroGrid_SliderData.attributes.text,
        url:
          !heroGrid_SliderData.attributes?.url ||
          heroGrid_SliderData.attributes.url === ""
            ? null
            : heroGrid_SliderData.attributes.url,
        media: heroGrid_SliderData.attributes.media.map((entry) => {
          return {
            alt: entry.text,
            imageURL: entry.mediaURL,
            text: !entry?.text || entry.text === "" ? null : entry.text,
            slug: !entry?.url || entry.url === "" ? null : entry.url,
          };
        }),
      };

      // extracting categories data
      const heroGrid_categoriesData = blocks.filter(
        (entry): entry is plaze_grid_card => entry.name === "plaze/grid-card"
      );

      const categoriesData = heroGrid_categoriesData.map((entry) => {
        return {
          mediaURL: entry.attributes.media.mediaURL,
          mediaID: entry.attributes.media.mediaID,
          text:
            !entry.attributes?.text || entry.attributes.text === ""
              ? null
              : entry.attributes.text,
          slug:
            !entry.attributes?.url || entry.attributes.url === ""
              ? null
              : entry.attributes.url,
        };
      });
      const categories: imageDisplay[] = categoriesData.map((entry) => {
        return {
          alt: entry.text,
          name: entry.text,
          imageURL: entry.mediaURL,
          slug: entry.slug,
        };
      });
      res = {
        type: 1,
        sliderData,
        categories,
      };
      break;
    }
    case 2: {
      // Slider - Card
      const blocks = innerBlocks as Array<plaze_grid_slider | plaze_grid_card>;

      // extracting grid Slider Data
      const heroGrid_SliderData = blocks.find(
        (section): section is plaze_grid_slider =>
          section.name === "plaze/grid-slider"
      );
      if (!heroGrid_SliderData)
        throw new Error(
          "something went wrong while processing HeroGrid Slider data with layout type 2"
        );

      const sliderData = {
        text:
          !heroGrid_SliderData.attributes?.text ||
          heroGrid_SliderData.attributes.text === ""
            ? null
            : heroGrid_SliderData.attributes.text,
        url:
          !heroGrid_SliderData.attributes?.url ||
          heroGrid_SliderData.attributes.url === ""
            ? null
            : heroGrid_SliderData.attributes.url,
        media: heroGrid_SliderData.attributes.media.map((entry) => {
          return {
            alt: entry.text,
            imageURL: entry.mediaURL,
            name: entry.text,
            slug: entry.url,
          };
        }),
      };

      // extracting single category (one card) data
      const heroGrid_ProductData = blocks.find(
        (section): section is plaze_grid_card =>
          section.name === "plaze/grid-card"
      );
      if (!heroGrid_ProductData)
        throw new Error(
          "something went wrong while processing HeroGrid Product  data with layout type 2 "
        );

      const {
        text,
        media: {
          // mediaID,
          mediaURL,
          url,
        },
      } = heroGrid_ProductData.attributes;

      const productData = {
        imageURL: mediaURL,
        alt: text,
        slug: url && url !== "" ? url : null,
        name: text && text !== "" ? text : null,
      };
      res = {
        type: 2,
        sliderData,
        productData,
      };
      break;
    }
    case 3: {
      // Slider - Video - Card - Card
      const blocks = innerBlocks as Array<
        plaze_grid_slider | plaze_grid_card | plaze_grid_video
      >;

      // extracting grid Slider Data
      const heroGrid_SliderData = blocks.find(
        (section): section is plaze_grid_slider =>
          section.name === "plaze/grid-slider"
      );
      if (!heroGrid_SliderData)
        throw new Error(
          "something went wrong while processing HeroGrid Slider data with layout type 3"
        );

      const sliderData = {
        text:
          !heroGrid_SliderData.attributes?.text ||
          heroGrid_SliderData.attributes.text === ""
            ? null
            : heroGrid_SliderData.attributes.text,
        url:
          !heroGrid_SliderData.attributes?.url ||
          heroGrid_SliderData.attributes.url === ""
            ? null
            : heroGrid_SliderData.attributes.url,
        media: heroGrid_SliderData.attributes.media.map((entry) => {
          return {
            alt: entry.text,
            imageURL: entry.mediaURL,
            name: entry.text,
            slug: entry.url,
          };
        }),
      };

      // extracting categories data
      const heroGrid_categoriesData = blocks.filter(
        (entry): entry is plaze_grid_card => entry.name === "plaze/grid-card"
      );

      const categoriesData = heroGrid_categoriesData.map((entry) => {
        return {
          mediaURL: entry.attributes.media.mediaURL,
          mediaID: entry.attributes.media.mediaID,
          text: entry.attributes.text,
          slug: entry.attributes.url,
        };
      });

      const categories: imageDisplay[] = categoriesData.map((entry) => {
        return {
          alt: entry.text,
          name: entry?.text && entry.text !== "" ? entry.text : null,
          imageURL: entry.mediaURL,
          slug: entry?.slug && entry.slug !== "" ? entry.slug : null,
        };
      });

      // extracting grid Video Data
      const heroGrid_VideoData = blocks.find(
        (section): section is plaze_grid_video =>
          section.name === "plaze/grid-video"
      );
      if (!heroGrid_VideoData)
        throw new Error(
          "something went wrong while processing HeroGrid Video data with layout type 3 "
        );

      const { mediaURL, text } = heroGrid_VideoData.attributes.media;
      const videURL = heroGrid_VideoData.attributes.videoUrl ?? mediaURL;
      const videoData = {
        url: videURL,
        title: text,
      };

      res = {
        type: 3,
        categories,
        sliderData,
        videoData,
      };
      break;
    }
    case 4: {
      // Video - Card - Card
      const blocks = innerBlocks as Array<plaze_grid_video | plaze_grid_card>;

      // extracting grid Video Data
      const heroGrid_VideoData = blocks.find(
        (section): section is plaze_grid_video =>
          section.name === "plaze/grid-video"
      );
      if (!heroGrid_VideoData)
        throw new Error(
          "something went wrong while processing HeroGrid Video data with layout type 4"
        );

      const { mediaURL, text } = heroGrid_VideoData.attributes.media;
      const videURL = heroGrid_VideoData.attributes.videoUrl ?? mediaURL;
      const videoData = {
        url: videURL,
        title: text,
      };

      // extracting categories data
      const heroGrid_categoriesData = blocks.filter(
        (entry): entry is plaze_grid_card => entry.name === "plaze/grid-card"
      );

      const categoriesData = heroGrid_categoriesData.map((entry) => {
        return {
          mediaURL: entry.attributes.media.mediaURL,
          mediaID: entry.attributes.media.mediaID,
          text: entry.attributes.text,
          url: entry.attributes,
        };
      });
      const categories: imageDisplay[] = categoriesData.map((entry) => {
        return {
          alt: entry.text,
          name: entry.text,
          imageURL: entry.mediaURL,
          slug: entry.mediaURL,
        };
      });
      res = {
        type: 4,
        categories,
        videoData,
      };
      break;
    }
    default:
      throw new Error(
        "unable to process Hero Grid Data, no matching Grid Type was found"
      );
  }

  return res;
}

/** @description process fetch response into proper  */
export async function _processCategorySliderData(
  data: homePageDataResponse["productSliderResponse"]
): Promise<categoryProductsSliderData> {
  "use server";
  const { attributes } = data;

  let response, products: productOfCategory[];

  if (Array.isArray(attributes) || !attributes?.categoryId) {
    // fetch default [no specific category]
    response = await getProducts();
  } else {
    response = await getProductsByCategoryID(attributes?.categoryId);
  }
  products =
    response.hits?.map(({ document: product }) => {
      const res: productOfCategory = {
        productType: product.type,
        stockStatus: product.stockStatus ? "instock" : "outofstock",
        alt: product.name,
        name: product.name,
        price: formatPrice(product.price),
        regularPrice: product.regular_price
          ? formatPrice(product.regular_price)
          : formatPrice("0"),
        description: product.name,
        imageURL: product.images[0],
        slug: product.slug,
        salePrice:
          product.price === product.regular_price
            ? formatPrice(product.price)
            : formatPrice("0"),
        category_raw: product.category_raw,
      };
      return res;
    }) ?? [];

  const final = {
    products: products,
    categoryName:
      Array.isArray(attributes) || !attributes?.categoryId
        ? "NEWCOMER"
        : `NEW ${
            products[0]?.category_raw
              ?.find((cat: any) => cat.id === attributes?.categoryId)
              ?.name.toUpperCase() ?? "PRODUCTS"
          }`,
  };
  return final;
}

async function _processBrandSliderPluginData(): Promise<brandsSliderData> {
  // brandsSliderData
  const response = await getBrands();
  return response.data.brands.edges
    .map((wrapper: { node: any }) => {
      const brand = wrapper.node;
      const res = {
        alt: brand.name,
        name: brand.name,
        imageURL: brand.thumbnailUrl,
        slug: brand.slug,
        count: brand.count,
      };
      return res;
    })
    .sort(
      (a: brandsSliderData[0], b: brandsSliderData[0]) => b.count - a.count
    );
}

export function _processTextAreaBlockData(
  data: homePageDataResponse["textAreaBlockResponse"]
): textBlockData {
  const textAreaBlock = {
    text: data.attributes.data.text_area,
    title: data.attributes.data.text_area_title,
  };
  return textAreaBlock;
}

async function _processInstagramFeedAPI(
  data: homePageDataResponse["instagramFeedResponse"]
): Promise<instagramPostsData | undefined> {
  const attribute = data;
  if (attribute) {
    const instagramPostUrl = await getInstagramPost();
    // console.log("instagramPostUrl", instagramPostUrl);
    return (instagramPostUrl ?? []).map(
      (data: {
        id: string;
        caption: string;
        media_url: string;
        timestamp: string;
        media_type: string;
        permalink: string;
      }) => {
        const res = {
          id: data.id,
          caption: data.caption,
          media_url: data.media_url,
          media_type: data.media_type,
          timestamp: data.timestamp,
          permalink: data.permalink,
        };
        return res;
      }
    );
  }
}
