import { createCollections } from "./schema";

// Function to migrate data from Algolia to Typesense
export async function migrateFromAlgoliaToTypesense() {
  try {
    // Create Typesense collections with proper schema
    await createCollections();

    return { success: true };
  } catch (error: any) {
    console.error("Error during migration:", error);
    console.error(error?.importResults);
    return { success: false, error };
  }
}
