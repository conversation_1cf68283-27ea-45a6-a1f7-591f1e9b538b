import { typesenseClient } from "./typesenseClient";

// Define the common schema fields for both products and product_sale collections
const commonFields = [
  { name: "objectID", type: "string", facet: false },
  { name: "name", type: "string", facet: false },
  { name: "slug", type: "string", facet: false },
  { name: "dateCreated", type: "int64", facet: false },
  { name: "regular_price", type: "float", facet: true },
  {
    name: "sale_price",
    type: "float",
    facet: true,
    optional: true,
    nullable: true,
  },
  { name: "price", type: "float", facet: true },
  { name: "description", type: "string", facet: false },
  { name: "category", type: "object", facet: false },
  { name: "category.lvl0", type: "string", facet: true, optional: true },
  { name: "category.lvl1", type: "string", facet: true, optional: true },
  { name: "category.lvl2", type: "string", facet: true, optional: true },
  { name: "sku", type: "string", facet: false, nullable: true, optional: true },
  { name: "images", type: "string[]", facet: false },
  { name: "stockStatus", type: "bool", facet: true },
  { name: "brands", type: "object[]", facet: true, optional: true },
  { name: "brands.id", type: "int32[]", facet: false, optional: true },
  { name: "brands.name", type: "string[]", facet: true, optional: true },
  { name: "brands.slug", type: "string[]", facet: false, optional: true },
  { name: "isPublished", type: "bool", facet: true },
  { name: "pa_clothsize", type: "string[]", facet: true, optional: true },
  { name: "pa_decksize", type: "string[]", facet: true, optional: true },
  { name: "pa_pantsize", type: "string[]", facet: true, optional: true },
  { name: "pa_shoesize", type: "string[]", facet: true, optional: true },
];

// Define the schema for the products collection
const productsSchema = {
  name: "products",
  fields: [...commonFields],
  default_sorting_field: "dateCreated",
  enable_nested_fields: true,
};

// Define the schema for the product_sale collection
const productSaleSchema = {
  name: "product_sale",
  fields: [...commonFields],
  default_sorting_field: "dateCreated",
  enable_nested_fields: true,
};

// Function to create collections in Typesense
export async function createCollections() {
  try {
    // Check if collections already exist and delete them if they do
    try {
      await typesenseClient.collections("products").delete();
      console.log("Deleted existing products collection");
    } catch (error) {
      console.log("Products collection does not exist yet");
    }

    try {
      await typesenseClient.collections("product_sale").delete();
      console.log("Deleted existing product_sale collection");
    } catch (error) {
      console.log("Product_sale collection does not exist yet");
    }

    // Create the collections with proper type casting
    // This is a workaround for TypeScript type issues
    await typesenseClient.collections().create({
      name: "products",
      fields: commonFields as any,
      default_sorting_field: "dateCreated",
      enable_nested_fields: true,
    });
    console.log("Created products collection");

    await typesenseClient.collections().create({
      name: "product_sale",
      fields: commonFields as any,
      default_sorting_field: "dateCreated",
      enable_nested_fields: true,
    });
    console.log("Created product_sale collection");

    return { success: true };
  } catch (error) {
    console.error("Error creating collections:", error);
    return { success: false, error };
  }
}

// Export the schemas for use elsewhere
export { productsSchema, productSaleSchema, commonFields };
