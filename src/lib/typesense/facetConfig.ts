/**
 * Facet configuration for Typesense based on Algolia's renderingContent settings
 * This is used to configure the order and display of facets in the UI
 */

export const facetOrdering = {
  facets: {
    order: [
      "pa_clothsize",
      "pa_decksize",
      "pa_shoesize"
    ]
  },
  values: {
    pa_clothsize: {
      sortRemainingBy: "alpha"
    },
    pa_decksize: {
      sortRemainingBy: "alpha"
    },
    pa_shoesize: {
      sortRemainingBy: "alpha"
    }
  }
};

/**
 * Helper function to get the facet order for a specific facet
 * @param facetName The name of the facet
 * @returns The order of the facet, or 999 if not found
 */
export function getFacetOrder(facetName: string): number {
  const order = facetOrdering.facets.order;
  const index = order.indexOf(facetName);
  return index >= 0 ? index : 999; // Return a high number for facets not in the order list
}

/**
 * Helper function to sort facet values
 * @param facetName The name of the facet
 * @param values The facet values to sort
 * @returns Sorted facet values
 */
export function sortFacetValues(facetName: string, values: any[]): any[] {
  const facetConfig = facetOrdering.values[facetName as keyof typeof facetOrdering.values];
  
  if (facetConfig && facetConfig.sortRemainingBy === 'alpha') {
    return values.sort((a, b) => a.label.localeCompare(b.label));
  }
  
  // Default sorting (by count)
  return values.sort((a, b) => b.count - a.count);
}

export default {
  facetOrdering,
  getFacetOrder,
  sortFacetValues
};
