import Typesense from "typesense";
import TypesenseInstantSearchAdapter from "typesense-instantsearch-adapter";

const typesenseInstantSearchAdapter = new TypesenseInstantSearchAdapter({
  server: {
    apiKey: process.env.NEXT_PUBLIC_TYPESENSE_SEARCH_KEY! || "",
    nodes: [
      {
        host: process.env.NEXT_PUBLIC_TYPESENSE_HOST!,
        port: parseInt(process.env.NEXT_PUBLIC_TYPESENSE_PORT || "443"),
        protocol: process.env.NEXT_PUBLIC_TYPESENSE_PROTOCOL || "https",
      },
    ],
    cacheSearchResultsForSeconds: 2 * 60, // Cache search results for 2 minutes
  },
  additionalSearchParameters: {
    query_by: "name,description,brands.name,sku,slug",
    query_by_weights: "4,2,2,1,1",
    facet_by:
      "brands.name,category.lvl0,category.lvl1,category.lvl2,pa_clothsize,pa_decksize,pa_pantsize,pa_shoesize,price,stockStatus,isPublished",
    include_fields: "*",
    highlight_full_fields: "name,description",
    highlight_affix_num_tokens: 4,
    num_typos: 2,
    min_len_1typo: 4,
    min_len_2typo: 8,
    typo_tokens_threshold: 1,
    sort_by: "_text_match:desc,dateCreated:desc", // default sorting
  },
});

// Search client for InstantSearch.js
const searchClient = typesenseInstantSearchAdapter.searchClient;

// Admin client for backend operations
const typesenseClient = new Typesense.Client({
  apiKey: process.env.TYPESENSE_ADMIN_API_KEY! || "",
  nodes: [
    {
      host: process.env.NEXT_PUBLIC_TYPESENSE_HOST!,
      port: parseInt(process.env.NEXT_PUBLIC_TYPESENSE_PORT || "443"),
      protocol: process.env.NEXT_PUBLIC_TYPESENSE_PROTOCOL || "https",
    },
  ],
  connectionTimeoutSeconds: 10,
});

export { searchClient, typesenseClient };
