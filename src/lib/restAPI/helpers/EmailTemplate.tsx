import * as React from "react";

interface EmailTemplateProps {
  email: string;
  note: string;
  firstName: string;
  lastName: string;
  orderNumber: string;
  phoneNumber: string;
  subject?: string;
}

export const EmailTemplate: React.FC<Readonly<EmailTemplateProps>> = ({
  email,
  note,
  firstName,
  lastName,
  orderNumber,
  phoneNumber,
}) => (
  <div>
    <p>You Got a new Mail from PLAZE contact form</p>
    <h4>from: {`${firstName} ${lastName}`}</h4>
    <div>
      <p>contact information: </p>
      <h4> email: {email}</h4>
      <h4> phoneNumber: {phoneNumber}</h4>
    </div>
    <div>
      <h4>Regarding the order: {orderNumber}</h4>
    </div>
    <div>
      <p>More Details:</p>
      <p>{note}</p>
    </div>
  </div>
);
