import { IProduct } from "../interfaces/IWordPressProduct";

function transformProductToSearchObjects(product: IProduct) {
  /**
   *! I moved this mapping to the WordPress Plugin Any transformations here keep it simple as possible for less complexity.
   *! Any new Transformation can be found in the woocommerce-notifier-webhook in the transform_product function

  const categories = product.categories ?? [];
  const updatedCategories: { [key: string]: string } = {};
  categories.forEach((category: any, index: number) => {
    const categoryLevel = `lvl${index}`;
    const hierarchicalCategory = categories
      .slice(0, index + 1)
      .map((c: any) => c.name)
      .join(" > ");

    updatedCategories[categoryLevel] = hierarchicalCategory;
  });
  for (let i = 0; i < categories.length; i++) {
    const categoryLevel = `lvl${i}`;
    console.log(categoryLevel);

    //   const categoryName = categories[i].name;

    // Build the hierarchical category string
    const hierarchicalCategory = categories
      .slice(0, i + 1)
      .map((c: any) => c.name)
      .join(" > ");

    updatedCategories[categoryLevel] = hierarchicalCategory;
  }
*/

  // Working with PHP time and date objects is a nightmare so I converted the dates here taking into consideration the time zone
  const date_created = new Date(product.date_created.date).toISOString();
  const date_modified = new Date(product.date_modified.date).toISOString();

  const price = Number(product.price); // Attempt to parse the price string
  const regular_price = Number(product.regular_price); // Attempt to parse the regular_price string
  const sale_price = Number(product.sale_price); // Attempt to parse the sale_price string

  return {
    ...product,
    price,
    regular_price,
    sale_price,
    date_created,
    date_modified,
  };
}

function transformAllProductsFromCron(
  products: any,
  categoriesWithLvl: {
    id: number;
    name: string;
    slug: string;
    parent: number;
    lvl: number;
  }[]
) {
  const transformedProducts = products.map((product: any) => {
    const price = Number(product.price); // Attempt to parse the price string

    // If the conversion was successful, update the product object with the numeric price
    if (!isNaN(price)) {
      product.price = price;
    } else {
      console.warn(
        `Failed to convert price "${product.price}" for product ID ${product.id} to a number. Skipping.`
      );
    }

    const categoriesToFilterOut = ["uncategorized", "unkategorisiert"];

    // Assuming product.categories is an array of objects
    const categories = product.categories
      .filter(
        (item: { slug: string }) => !categoriesToFilterOut.includes(item.slug)
      )
      .sort((currentItem: { id: number }, nextItem: { id: number }) => {
        // We sort by predetermined Level of the categories
        const currentLevel = categoriesWithLvl.find(
          (cwl) => cwl.id === currentItem.id
        )?.lvl;
        const nextLevel = categoriesWithLvl.find(
          (cwl) => cwl.id === nextItem.id
        )?.lvl;
        return (currentLevel ?? 0) - (nextLevel ?? 0);
      });

    // Sometimes there are parent Categories missing from product data, so we need add these

    if (categories.length > 0) {
      let firstCategory = categoriesWithLvl.find(
        (cwl) => cwl.id === categories[0].id
      );

      while (firstCategory?.parent !== 0) {
        const categoryBeforeFirst = categoriesWithLvl.find(
          (cwl) => cwl.id === firstCategory?.parent
        );
        if (!categoryBeforeFirst) {
          break;
        }
        categories.unshift({
          id: categoryBeforeFirst.id,
          name: categoryBeforeFirst.name,
          slug: categoryBeforeFirst.slug,
        });
        firstCategory = categoryBeforeFirst;
      }
    }

    const updatedCategories: { [key: string]: string } = {};

    categories.forEach((category: any, index: number) => {
      const categoryLevel = `lvl${index}`;
      const hierarchicalCategory = categories
        .slice(0, index + 1)
        .map((c: any) => c.name)
        .join(" > ");

      updatedCategories[categoryLevel] = hierarchicalCategory;
    });

    return {
      objectID: parseInt(product.id), // Add objectID field based on product.id,
      collection: "product",
      name: product.name,
      slug: product.slug,
      type: product.type,
      date_created: product.date_created_gmt,
      featured: product.featured,
      catalog_visibility: product.catalog_visibility,
      description: product.description,
      images: product.images.map((image: any) => image.src), // Or Maybe we only need the first one for the overview?
      sku: product.sku,
      price: product.price,
      regular_price: product.regular_price,
      sale_price: product.sale_price,
      categories: product.categories,
      category: updatedCategories,
      tags: product.tags, //Maybe needs further decomposing
      attributes: product.attributes, //Maybe needs further decomposing
      brands: product.brands, //Maybe needs further decomposing
      meta_data: product.meta_data,
      stock_status: product.stock_status,
    };
  });

  return transformedProducts;
}

export { transformAllProductsFromCron, transformProductToSearchObjects };
