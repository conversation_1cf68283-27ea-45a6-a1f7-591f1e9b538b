import WooCommerceRestApi from "@woocommerce/woocommerce-rest-api";
import { error } from "console";

// initialize the WooCommerceRestApi //
const api = new WooCommerceRestApi({
  url: process.env.WORDPRESS_URL!,
  consumerKey: process.env.WOOCOMMERCE_KEY!,
  consumerSecret: process.env.WOOCOMMERCE_SECRET!,
  version: "wc/v3",
});

export async function fetchWooCommerceProducts() {
  try {
    let response: any[] = [];
    let lastResponse = [];
    let page = 1;
    while (page === 1 || lastResponse.length > 0) {
      const r = await api.get("products", {
        page: page,
        per_page: 100,
        status: "publish",
      });
      lastResponse = r.data;
      response = response.concat(lastResponse);
      page++;
    }
    return response;
  } catch (e) {
    error(e);
    throw e;
  }
}

export async function fetchWooCommerceCategories() {
  try {
    let response: any[] = [];
    let lastResponse = [];
    let page = 1;
    while (page === 1 || lastResponse.length > 0) {
      const r = await api.get("products/categories", {
        page: page,
      });
      lastResponse = r.data;
      response = response.concat(lastResponse);
      page++;
    }
    return response;
  } catch (e) {
    error(e);
    throw e;
  }
}
