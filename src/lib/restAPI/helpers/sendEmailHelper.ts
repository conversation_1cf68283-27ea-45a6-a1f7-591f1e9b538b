export const sendEmail = async (
  data: any,
  receiver: "CONTACT" | "ADMIN" | "DROP"
) => {
  const response = await fetch("/api/email", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.NEXT_PUBLIC_REST_API_AUTH_KEY}`,
    },
    body: JSON.stringify({ ...data, receiver }),
  });
  const json = await response.json();
  return json;
};
