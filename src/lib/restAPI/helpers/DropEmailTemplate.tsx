import * as React from "react";

interface EmailTemplateProps {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  id: number;
  slug: string;
  productName: string;
}

export const DropEmailTemplate: React.FC<Readonly<EmailTemplateProps>> = ({
  email,
  slug,
  firstName,
  lastName,
  id,
  productName,
  phoneNumber,
}) => (
  <div>
    <p>Jemand hat sich für den Drop von {productName} registriert.</p>
    <h4>Name: {`${firstName} ${lastName}`}</h4>
    <div>
      <p>Kontakt: </p>
      <h4> E-Mail: {email}</h4>
      <h4> Telefonnummer: {phoneNumber}</h4>
    </div>
    <div>
      <h4>Produkt: {productName}</h4>
      <a href={`https://plaze-shop.de/products/${slug}`}>Produkt im Webshop</a>
      <a
        href={`https://admin.plaze-shop.de/wp-admin/post.php?post=${id}&action=edit`}
      >
        Produkt im Backend
      </a>
    </div>
  </div>
);
