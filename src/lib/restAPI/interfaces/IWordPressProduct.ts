type WP_DATE_OBJECT = {
  date: string;
  timezone_type: number;
  timezone: string;
};
export interface IProduct {
  id: string;
  collection: "product";
  name: string;
  slug: string;
  type: string;
  date_created: WP_DATE_OBJECT;
  date_modified: WP_DATE_OBJECT;
  featured: string;
  catalog_visibility: string;
  description: string;
  images: any[]; // Or Maybe we only need the first one for the overview?
  sku: string;
  price: string;
  regular_price: string;
  sale_price: string;
  categories: any;
  category: { [key: string]: string };
  tags: string[]; //Maybe needs further decomposing
  attributes: string[]; //Maybe needs further decomposing
  brands: string[]; //Maybe needs further decomposing
  meta_data: string;
  stock_status: string;
}
