import type { ClassValue } from "clsx";
import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 *
 * @param regularPrice  initial price
 * @param salePrice  price after sale
 * @returns  a percentile value  representing the amount of change in price
 * @range [-100,0]
 * @notice returns a negative value
 */
export const getChangePercentage = (
  regularPrice: string,
  salePrice: string
): number => {
  const oldPrice = parseFloat(regularPrice),
    newPrice = parseFloat(salePrice);
  const changePercentage = ((newPrice - oldPrice) / oldPrice) * -100;
  const parsedNumber = Math.round(changePercentage);
  return parsedNumber;
};

export const getPriceFormatter = (
  withEuro: boolean | undefined = true, // withEuro is optional to add a Euro symbol to the price
  inCents: boolean | undefined = true
): {
  /** formats any valid number into proper German number formatting using `Intl` API   */
  deutschFormatter: (_: string | number) => string;
} => {
  const formatter = new Intl.NumberFormat("de-DE", {
    style: "currency",
    currency: withEuro ? "EUR" : undefined,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  const deutschFormatter = (price: string | number) => {
    const p = typeof price === "string" ? parseFloat(price) : price;
    return formatter.format(inCents ? p / 100 : p);
  };

  return {
    deutschFormatter,
  };
};

/**
 * @description generates a range of integers from a given `start` and `stop position`
 * @example arrayRange(1,5) --> [1,2,3,4,5]
 * @example arrayRange(1,5,2) --> [1,3,5,7,9]
 */
export function productRange(start: number, stop: number, step: number = 1) {
  return Array.from(
    { length: (stop - start) / step + 1 },
    (value, index) => start + index * step
  );
}
