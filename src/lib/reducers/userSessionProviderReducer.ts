"use client";
import { clearCredentials } from "@lib/utilities/authUtils";
import { userSession } from "@typesDeclarations/contextTypes/sessionContext.types";
import { USER_SESSION_ACTION } from "@typesDeclarations/utilTypes";

export const userSessionsReducer = (
  state: userSession,
  action: USER_SESSION_ACTION
) => {
  switch (action.type) {
    case "SET_CART": {
      const productsNodes = action.payload?.contents.nodes as Array<any>;
      if (!productsNodes)
        return {
          ...state,
          cart: null,
        };
      const manuallyCalculatedTotal = productsNodes.reduce(
        (accTotal, productsNode) => {
          const quantity = productsNode.quantity;

          /** @see ProductTypesEnum */
          const type = productsNode.product.node.type;
          const price =
            type === "SIMPLE"
              ? parseFloat(productsNode.product?.node?.price)
              : parseFloat(productsNode.variation?.node?.price);
          const productTotal = quantity * price;
          return accTotal + productTotal;
        },
        0
      );
      return {
        ...state,
        cart: { ...action.payload, manuallyCalculatedTotal },
      };
    }
    case "SET_CUSTOMER":
      return {
        ...state,
        customer: { ...state.customer, ...action.payload },
      };
    case "RESET_STATE": {
      clearCredentials();
      return {
        wishList: [],
        customer: null,
        cart: null,
      };
    }
    default:
      throw new Error("Ungültige Aktion an Sitzungsreduzierer gesendet");
  }
};
