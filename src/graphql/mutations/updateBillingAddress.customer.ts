import { gql } from "@apollo/client";

const updateBillingAddress = gql(`
  mutation billingAddress($firstName: String!, $lastName: String!,$address1: String!, $city: String!,$postcode: String!, $country: CountriesEnum!) {
    updateCustomer(
    input: {billing: {firstName: $firstName, lastName: $lastName, address1: $address1, city: $city , postcode: $postcode, country: $country}}
  ) 
 {customer{
  id
 }}
}     
`);

export { updateBillingAddress };
