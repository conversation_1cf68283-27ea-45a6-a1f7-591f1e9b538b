import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type AddToCartMutationVariables = Types.Exact<{
  productId: Types.Scalars["Int"]["input"];
  variationId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  quantity?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  extraData?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type AddToCartMutation = {
  __typename?: "RootMutation";
  addToCart?: {
    __typename?: "AddToCartPayload";
    cart?: {
      __typename?: "Cart";
      total?: string | null;
      isEmpty?: boolean | null;
      subtotal?: string | null;
      subtotalTax?: string | null;
      shippingTax?: string | null;
      shippingTotal?: string | null;
      totalTax?: string | null;
      feeTax?: string | null;
      feeTotal?: string | null;
      discountTax?: string | null;
      discountTotal?: string | null;
      contents?: {
        __typename?: "CartToCartItemConnection";
        itemCount?: number | null;
        nodes: Array<{
          __typename?: "SimpleCartItem";
          key: string;
          quantity?: number | null;
          total?: string | null;
          product?: {
            __typename?: "CartItemToProductConnectionEdge";
            node:
              | {
                  __typename?: "ExternalProduct";
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                }
              | {
                  __typename?: "GroupProduct";
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                }
              | {
                  __typename?: "SimpleProduct";
                  stockQuantity?: number | null;
                  price?: string | null;
                  regularPrice?: string | null;
                  salePrice?: string | null;
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                }
              | {
                  __typename?: "VariableProduct";
                  stockQuantity?: number | null;
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                };
          } | null;
          variation?: {
            __typename?: "CartItemToProductVariationConnectionEdge";
            node: {
              __typename?: "SimpleProductVariation";
              id: string;
              databaseId: number;
              name?: string | null;
              stockQuantity?: number | null;
              regularPrice?: string | null;
              salePrice?: string | null;
              price?: string | null;
              attributes?: {
                __typename?: "ProductVariationToVariationAttributeConnection";
                nodes: Array<{
                  __typename?: "VariationAttribute";
                  label?: string | null;
                  value?: string | null;
                }>;
              } | null;
            };
          } | null;
        }>;
      } | null;
      appliedCoupons?: Array<{
        __typename?: "AppliedCoupon";
        code: string;
        discountAmount: string;
        discountTax: string;
      } | null> | null;
    } | null;
    cartItem?: {
      __typename?: "SimpleCartItem";
      key: string;
      quantity?: number | null;
      total?: string | null;
      product?: {
        __typename?: "CartItemToProductConnectionEdge";
        node:
          | {
              __typename?: "ExternalProduct";
              type?: Types.ProductTypesEnum | null;
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              image?: {
                __typename?: "MediaItem";
                altText?: string | null;
                sourceUrl?: string | null;
              } | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  id: string;
                  name?: string | null;
                }>;
              } | null;
            }
          | {
              __typename?: "GroupProduct";
              type?: Types.ProductTypesEnum | null;
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              image?: {
                __typename?: "MediaItem";
                altText?: string | null;
                sourceUrl?: string | null;
              } | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  id: string;
                  name?: string | null;
                }>;
              } | null;
            }
          | {
              __typename?: "SimpleProduct";
              stockQuantity?: number | null;
              price?: string | null;
              regularPrice?: string | null;
              salePrice?: string | null;
              type?: Types.ProductTypesEnum | null;
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              image?: {
                __typename?: "MediaItem";
                altText?: string | null;
                sourceUrl?: string | null;
              } | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  id: string;
                  name?: string | null;
                }>;
              } | null;
            }
          | {
              __typename?: "VariableProduct";
              stockQuantity?: number | null;
              type?: Types.ProductTypesEnum | null;
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              image?: {
                __typename?: "MediaItem";
                altText?: string | null;
                sourceUrl?: string | null;
              } | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  id: string;
                  name?: string | null;
                }>;
              } | null;
            };
      } | null;
      variation?: {
        __typename?: "CartItemToProductVariationConnectionEdge";
        node: {
          __typename?: "SimpleProductVariation";
          id: string;
          databaseId: number;
          name?: string | null;
          stockQuantity?: number | null;
          regularPrice?: string | null;
          salePrice?: string | null;
          price?: string | null;
          attributes?: {
            __typename?: "ProductVariationToVariationAttributeConnection";
            nodes: Array<{
              __typename?: "VariationAttribute";
              label?: string | null;
              value?: string | null;
            }>;
          } | null;
        };
      } | null;
    } | null;
  } | null;
};

export type UpdateCartItemQuantitiesMutationVariables = Types.Exact<{
  items?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.CartItemQuantityInput>>
    | Types.InputMaybe<Types.CartItemQuantityInput>
  >;
}>;

export type UpdateCartItemQuantitiesMutation = {
  __typename?: "RootMutation";
  updateItemQuantities?: {
    __typename?: "UpdateItemQuantitiesPayload";
    cart?: {
      __typename?: "Cart";
      total?: string | null;
      isEmpty?: boolean | null;
      subtotal?: string | null;
      subtotalTax?: string | null;
      shippingTax?: string | null;
      shippingTotal?: string | null;
      totalTax?: string | null;
      feeTax?: string | null;
      feeTotal?: string | null;
      discountTax?: string | null;
      discountTotal?: string | null;
      contents?: {
        __typename?: "CartToCartItemConnection";
        itemCount?: number | null;
        nodes: Array<{
          __typename?: "SimpleCartItem";
          key: string;
          quantity?: number | null;
          total?: string | null;
          product?: {
            __typename?: "CartItemToProductConnectionEdge";
            node:
              | {
                  __typename?: "ExternalProduct";
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                }
              | {
                  __typename?: "GroupProduct";
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                }
              | {
                  __typename?: "SimpleProduct";
                  stockQuantity?: number | null;
                  price?: string | null;
                  regularPrice?: string | null;
                  salePrice?: string | null;
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                }
              | {
                  __typename?: "VariableProduct";
                  stockQuantity?: number | null;
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                };
          } | null;
          variation?: {
            __typename?: "CartItemToProductVariationConnectionEdge";
            node: {
              __typename?: "SimpleProductVariation";
              id: string;
              databaseId: number;
              name?: string | null;
              stockQuantity?: number | null;
              regularPrice?: string | null;
              salePrice?: string | null;
              price?: string | null;
              attributes?: {
                __typename?: "ProductVariationToVariationAttributeConnection";
                nodes: Array<{
                  __typename?: "VariationAttribute";
                  label?: string | null;
                  value?: string | null;
                }>;
              } | null;
            };
          } | null;
        }>;
      } | null;
      appliedCoupons?: Array<{
        __typename?: "AppliedCoupon";
        code: string;
        discountAmount: string;
        discountTax: string;
      } | null> | null;
    } | null;
    items?: Array<{
      __typename?: "SimpleCartItem";
      key: string;
      quantity?: number | null;
      total?: string | null;
      product?: {
        __typename?: "CartItemToProductConnectionEdge";
        node:
          | {
              __typename?: "ExternalProduct";
              type?: Types.ProductTypesEnum | null;
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              image?: {
                __typename?: "MediaItem";
                altText?: string | null;
                sourceUrl?: string | null;
              } | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  id: string;
                  name?: string | null;
                }>;
              } | null;
            }
          | {
              __typename?: "GroupProduct";
              type?: Types.ProductTypesEnum | null;
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              image?: {
                __typename?: "MediaItem";
                altText?: string | null;
                sourceUrl?: string | null;
              } | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  id: string;
                  name?: string | null;
                }>;
              } | null;
            }
          | {
              __typename?: "SimpleProduct";
              stockQuantity?: number | null;
              price?: string | null;
              regularPrice?: string | null;
              salePrice?: string | null;
              type?: Types.ProductTypesEnum | null;
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              image?: {
                __typename?: "MediaItem";
                altText?: string | null;
                sourceUrl?: string | null;
              } | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  id: string;
                  name?: string | null;
                }>;
              } | null;
            }
          | {
              __typename?: "VariableProduct";
              stockQuantity?: number | null;
              type?: Types.ProductTypesEnum | null;
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              image?: {
                __typename?: "MediaItem";
                altText?: string | null;
                sourceUrl?: string | null;
              } | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  id: string;
                  name?: string | null;
                }>;
              } | null;
            };
      } | null;
      variation?: {
        __typename?: "CartItemToProductVariationConnectionEdge";
        node: {
          __typename?: "SimpleProductVariation";
          id: string;
          databaseId: number;
          name?: string | null;
          stockQuantity?: number | null;
          regularPrice?: string | null;
          salePrice?: string | null;
          price?: string | null;
          attributes?: {
            __typename?: "ProductVariationToVariationAttributeConnection";
            nodes: Array<{
              __typename?: "VariationAttribute";
              label?: string | null;
              value?: string | null;
            }>;
          } | null;
        };
      } | null;
    } | null> | null;
  } | null;
};

export type RemoveItemsFromCartMutationVariables = Types.Exact<{
  keys?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.Scalars["ID"]["input"]>>
    | Types.InputMaybe<Types.Scalars["ID"]["input"]>
  >;
  all?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type RemoveItemsFromCartMutation = {
  __typename?: "RootMutation";
  removeItemsFromCart?: {
    __typename?: "RemoveItemsFromCartPayload";
    cart?: {
      __typename?: "Cart";
      total?: string | null;
      isEmpty?: boolean | null;
      subtotal?: string | null;
      subtotalTax?: string | null;
      shippingTax?: string | null;
      shippingTotal?: string | null;
      totalTax?: string | null;
      feeTax?: string | null;
      feeTotal?: string | null;
      discountTax?: string | null;
      discountTotal?: string | null;
      contents?: {
        __typename?: "CartToCartItemConnection";
        itemCount?: number | null;
        nodes: Array<{
          __typename?: "SimpleCartItem";
          key: string;
          quantity?: number | null;
          total?: string | null;
          product?: {
            __typename?: "CartItemToProductConnectionEdge";
            node:
              | {
                  __typename?: "ExternalProduct";
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                }
              | {
                  __typename?: "GroupProduct";
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                }
              | {
                  __typename?: "SimpleProduct";
                  stockQuantity?: number | null;
                  price?: string | null;
                  regularPrice?: string | null;
                  salePrice?: string | null;
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                }
              | {
                  __typename?: "VariableProduct";
                  stockQuantity?: number | null;
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                };
          } | null;
          variation?: {
            __typename?: "CartItemToProductVariationConnectionEdge";
            node: {
              __typename?: "SimpleProductVariation";
              id: string;
              databaseId: number;
              name?: string | null;
              stockQuantity?: number | null;
              regularPrice?: string | null;
              salePrice?: string | null;
              price?: string | null;
              attributes?: {
                __typename?: "ProductVariationToVariationAttributeConnection";
                nodes: Array<{
                  __typename?: "VariationAttribute";
                  label?: string | null;
                  value?: string | null;
                }>;
              } | null;
            };
          } | null;
        }>;
      } | null;
      appliedCoupons?: Array<{
        __typename?: "AppliedCoupon";
        code: string;
        discountAmount: string;
        discountTax: string;
      } | null> | null;
    } | null;
    cartItems?: Array<{
      __typename?: "SimpleCartItem";
      key: string;
      quantity?: number | null;
      total?: string | null;
      product?: {
        __typename?: "CartItemToProductConnectionEdge";
        node:
          | {
              __typename?: "ExternalProduct";
              type?: Types.ProductTypesEnum | null;
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              image?: {
                __typename?: "MediaItem";
                altText?: string | null;
                sourceUrl?: string | null;
              } | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  id: string;
                  name?: string | null;
                }>;
              } | null;
            }
          | {
              __typename?: "GroupProduct";
              type?: Types.ProductTypesEnum | null;
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              image?: {
                __typename?: "MediaItem";
                altText?: string | null;
                sourceUrl?: string | null;
              } | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  id: string;
                  name?: string | null;
                }>;
              } | null;
            }
          | {
              __typename?: "SimpleProduct";
              stockQuantity?: number | null;
              price?: string | null;
              regularPrice?: string | null;
              salePrice?: string | null;
              type?: Types.ProductTypesEnum | null;
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              image?: {
                __typename?: "MediaItem";
                altText?: string | null;
                sourceUrl?: string | null;
              } | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  id: string;
                  name?: string | null;
                }>;
              } | null;
            }
          | {
              __typename?: "VariableProduct";
              stockQuantity?: number | null;
              type?: Types.ProductTypesEnum | null;
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              image?: {
                __typename?: "MediaItem";
                altText?: string | null;
                sourceUrl?: string | null;
              } | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  id: string;
                  name?: string | null;
                }>;
              } | null;
            };
      } | null;
      variation?: {
        __typename?: "CartItemToProductVariationConnectionEdge";
        node: {
          __typename?: "SimpleProductVariation";
          id: string;
          databaseId: number;
          name?: string | null;
          stockQuantity?: number | null;
          regularPrice?: string | null;
          salePrice?: string | null;
          price?: string | null;
          attributes?: {
            __typename?: "ProductVariationToVariationAttributeConnection";
            nodes: Array<{
              __typename?: "VariationAttribute";
              label?: string | null;
              value?: string | null;
            }>;
          } | null;
        };
      } | null;
    } | null> | null;
  } | null;
};

export type ApplyCouponMutationVariables = Types.Exact<{
  code: Types.Scalars["String"]["input"];
}>;

export type ApplyCouponMutation = {
  __typename?: "RootMutation";
  applyCoupon?: {
    __typename?: "ApplyCouponPayload";
    cart?: {
      __typename?: "Cart";
      total?: string | null;
      isEmpty?: boolean | null;
      subtotal?: string | null;
      subtotalTax?: string | null;
      shippingTax?: string | null;
      shippingTotal?: string | null;
      totalTax?: string | null;
      feeTax?: string | null;
      feeTotal?: string | null;
      discountTax?: string | null;
      discountTotal?: string | null;
      contents?: {
        __typename?: "CartToCartItemConnection";
        itemCount?: number | null;
        nodes: Array<{
          __typename?: "SimpleCartItem";
          key: string;
          quantity?: number | null;
          total?: string | null;
          product?: {
            __typename?: "CartItemToProductConnectionEdge";
            node:
              | {
                  __typename?: "ExternalProduct";
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                }
              | {
                  __typename?: "GroupProduct";
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                }
              | {
                  __typename?: "SimpleProduct";
                  stockQuantity?: number | null;
                  price?: string | null;
                  regularPrice?: string | null;
                  salePrice?: string | null;
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                }
              | {
                  __typename?: "VariableProduct";
                  stockQuantity?: number | null;
                  type?: Types.ProductTypesEnum | null;
                  id: string;
                  databaseId: number;
                  name?: string | null;
                  slug?: string | null;
                  image?: {
                    __typename?: "MediaItem";
                    altText?: string | null;
                    sourceUrl?: string | null;
                  } | null;
                  productCategories?: {
                    __typename?: "ProductToProductCategoryConnection";
                    nodes: Array<{
                      __typename?: "ProductCategory";
                      id: string;
                      name?: string | null;
                    }>;
                  } | null;
                };
          } | null;
          variation?: {
            __typename?: "CartItemToProductVariationConnectionEdge";
            node: {
              __typename?: "SimpleProductVariation";
              id: string;
              databaseId: number;
              name?: string | null;
              stockQuantity?: number | null;
              regularPrice?: string | null;
              salePrice?: string | null;
              price?: string | null;
              attributes?: {
                __typename?: "ProductVariationToVariationAttributeConnection";
                nodes: Array<{
                  __typename?: "VariationAttribute";
                  label?: string | null;
                  value?: string | null;
                }>;
              } | null;
            };
          } | null;
        }>;
      } | null;
      appliedCoupons?: Array<{
        __typename?: "AppliedCoupon";
        code: string;
        discountAmount: string;
        discountTax: string;
      } | null> | null;
    } | null;
  } | null;
};

export type EmptyCartMutationVariables = Types.Exact<{ [key: string]: never }>;

export type EmptyCartMutation = {
  __typename?: "RootMutation";
  emptyCart?: {
    __typename?: "EmptyCartPayload";
    cart?: {
      __typename?: "Cart";
      contentsTax?: string | null;
      contentsTotal?: string | null;
      discountTax?: string | null;
      discountTotal?: string | null;
      displayPricesIncludeTax?: boolean | null;
      feeTax?: string | null;
      feeTotal?: string | null;
      isEmpty?: boolean | null;
      needsShippingAddress?: boolean | null;
      shippingTax?: string | null;
      shippingTotal?: string | null;
      subtotal?: string | null;
      subtotalTax?: string | null;
      total?: string | null;
      totalTax?: string | null;
    } | null;
  } | null;
};

export type UpdateCartShippingMutationVariables = Types.Exact<{
  shippingMethod: Types.Scalars["String"]["input"];
}>;

export type UpdateCartShippingMutation = {
  __typename?: "RootMutation";
  updateShippingMethod?: {
    __typename?: "UpdateShippingMethodPayload";
    cart?: {
      __typename?: "Cart";
      total?: string | null;
      shippingTax?: string | null;
      shippingTotal?: string | null;
    } | null;
  } | null;
};

export const AddToCartDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "AddToCart" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "productId" },
          },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "Int" } },
          },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "variationId" },
          },
          type: { kind: "NamedType", name: { kind: "Name", value: "Int" } },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "quantity" },
          },
          type: { kind: "NamedType", name: { kind: "Name", value: "Int" } },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "extraData" },
          },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "addToCart" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "productId" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "productId" },
                      },
                    },
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "variationId" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "variationId" },
                      },
                    },
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "quantity" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "quantity" },
                      },
                    },
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "extraData" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "extraData" },
                      },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "cart" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "FragmentSpread",
                        name: { kind: "Name", value: "cartContent" },
                      },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "cartItem" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "FragmentSpread",
                        name: { kind: "Name", value: "CartItemContent" },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CartItemContent" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "CartItem" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "key" } },
          { kind: "Field", name: { kind: "Name", value: "quantity" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "product" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "node" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "image" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "altText" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "sourceUrl" },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "type" } },
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "databaseId" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      { kind: "Field", name: { kind: "Name", value: "slug" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "productCategories" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "nodes" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "id" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "name" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                      {
                        kind: "InlineFragment",
                        typeCondition: {
                          kind: "NamedType",
                          name: { kind: "Name", value: "SimpleProduct" },
                        },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "stockQuantity" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "price" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "regularPrice" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "salePrice" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                          ],
                        },
                      },
                      {
                        kind: "InlineFragment",
                        typeCondition: {
                          kind: "NamedType",
                          name: { kind: "Name", value: "VariableProduct" },
                        },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "stockQuantity" },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "variation" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "node" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "databaseId" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "attributes" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "nodes" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "label" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "value" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "stockQuantity" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "regularPrice" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "salePrice" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "price" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "cartContent" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "Cart" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          { kind: "Field", name: { kind: "Name", value: "isEmpty" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "contents" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "IntValue", value: "100" },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "itemCount" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "nodes" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "FragmentSpread",
                        name: { kind: "Name", value: "CartItemContent" },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "appliedCoupons" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "code" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "discountAmount" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "format" },
                      value: { kind: "EnumValue", value: "RAW" },
                    },
                  ],
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "discountTax" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "format" },
                      value: { kind: "EnumValue", value: "RAW" },
                    },
                  ],
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "subtotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "subtotalTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "shippingTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "shippingTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "totalTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "feeTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "feeTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "discountTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "discountTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<AddToCartMutation, AddToCartMutationVariables>;
export const UpdateCartItemQuantitiesDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "UpdateCartItemQuantities" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "items" },
          },
          type: {
            kind: "ListType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "CartItemQuantityInput" },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "updateItemQuantities" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "items" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "items" },
                      },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "cart" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "FragmentSpread",
                        name: { kind: "Name", value: "cartContent" },
                      },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "items" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "FragmentSpread",
                        name: { kind: "Name", value: "CartItemContent" },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CartItemContent" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "CartItem" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "key" } },
          { kind: "Field", name: { kind: "Name", value: "quantity" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "product" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "node" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "image" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "altText" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "sourceUrl" },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "type" } },
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "databaseId" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      { kind: "Field", name: { kind: "Name", value: "slug" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "productCategories" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "nodes" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "id" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "name" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                      {
                        kind: "InlineFragment",
                        typeCondition: {
                          kind: "NamedType",
                          name: { kind: "Name", value: "SimpleProduct" },
                        },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "stockQuantity" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "price" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "regularPrice" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "salePrice" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                          ],
                        },
                      },
                      {
                        kind: "InlineFragment",
                        typeCondition: {
                          kind: "NamedType",
                          name: { kind: "Name", value: "VariableProduct" },
                        },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "stockQuantity" },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "variation" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "node" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "databaseId" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "attributes" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "nodes" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "label" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "value" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "stockQuantity" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "regularPrice" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "salePrice" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "price" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "cartContent" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "Cart" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          { kind: "Field", name: { kind: "Name", value: "isEmpty" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "contents" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "IntValue", value: "100" },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "itemCount" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "nodes" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "FragmentSpread",
                        name: { kind: "Name", value: "CartItemContent" },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "appliedCoupons" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "code" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "discountAmount" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "format" },
                      value: { kind: "EnumValue", value: "RAW" },
                    },
                  ],
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "discountTax" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "format" },
                      value: { kind: "EnumValue", value: "RAW" },
                    },
                  ],
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "subtotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "subtotalTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "shippingTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "shippingTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "totalTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "feeTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "feeTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "discountTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "discountTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  UpdateCartItemQuantitiesMutation,
  UpdateCartItemQuantitiesMutationVariables
>;
export const RemoveItemsFromCartDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "RemoveItemsFromCart" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "keys" } },
          type: {
            kind: "ListType",
            type: { kind: "NamedType", name: { kind: "Name", value: "ID" } },
          },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "all" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Boolean" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "removeItemsFromCart" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "keys" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "keys" },
                      },
                    },
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "all" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "all" },
                      },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "cart" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "FragmentSpread",
                        name: { kind: "Name", value: "cartContent" },
                      },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "cartItems" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "FragmentSpread",
                        name: { kind: "Name", value: "CartItemContent" },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CartItemContent" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "CartItem" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "key" } },
          { kind: "Field", name: { kind: "Name", value: "quantity" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "product" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "node" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "image" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "altText" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "sourceUrl" },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "type" } },
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "databaseId" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      { kind: "Field", name: { kind: "Name", value: "slug" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "productCategories" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "nodes" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "id" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "name" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                      {
                        kind: "InlineFragment",
                        typeCondition: {
                          kind: "NamedType",
                          name: { kind: "Name", value: "SimpleProduct" },
                        },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "stockQuantity" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "price" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "regularPrice" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "salePrice" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                          ],
                        },
                      },
                      {
                        kind: "InlineFragment",
                        typeCondition: {
                          kind: "NamedType",
                          name: { kind: "Name", value: "VariableProduct" },
                        },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "stockQuantity" },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "variation" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "node" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "databaseId" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "attributes" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "nodes" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "label" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "value" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "stockQuantity" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "regularPrice" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "salePrice" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "price" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "cartContent" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "Cart" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          { kind: "Field", name: { kind: "Name", value: "isEmpty" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "contents" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "IntValue", value: "100" },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "itemCount" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "nodes" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "FragmentSpread",
                        name: { kind: "Name", value: "CartItemContent" },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "appliedCoupons" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "code" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "discountAmount" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "format" },
                      value: { kind: "EnumValue", value: "RAW" },
                    },
                  ],
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "discountTax" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "format" },
                      value: { kind: "EnumValue", value: "RAW" },
                    },
                  ],
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "subtotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "subtotalTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "shippingTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "shippingTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "totalTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "feeTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "feeTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "discountTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "discountTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  RemoveItemsFromCartMutation,
  RemoveItemsFromCartMutationVariables
>;
export const ApplyCouponDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "applyCoupon" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "code" } },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "String" },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "applyCoupon" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "code" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "code" },
                      },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "cart" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "FragmentSpread",
                        name: { kind: "Name", value: "cartContent" },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CartItemContent" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "CartItem" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "key" } },
          { kind: "Field", name: { kind: "Name", value: "quantity" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "product" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "node" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "image" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "altText" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "sourceUrl" },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "type" } },
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "databaseId" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      { kind: "Field", name: { kind: "Name", value: "slug" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "productCategories" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "nodes" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "id" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "name" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                      {
                        kind: "InlineFragment",
                        typeCondition: {
                          kind: "NamedType",
                          name: { kind: "Name", value: "SimpleProduct" },
                        },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "stockQuantity" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "price" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "regularPrice" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "salePrice" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                          ],
                        },
                      },
                      {
                        kind: "InlineFragment",
                        typeCondition: {
                          kind: "NamedType",
                          name: { kind: "Name", value: "VariableProduct" },
                        },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "stockQuantity" },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "variation" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "node" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "databaseId" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "attributes" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "nodes" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "label" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "value" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "stockQuantity" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "regularPrice" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "salePrice" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "price" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "cartContent" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "Cart" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          { kind: "Field", name: { kind: "Name", value: "isEmpty" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "contents" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "IntValue", value: "100" },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "itemCount" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "nodes" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "FragmentSpread",
                        name: { kind: "Name", value: "CartItemContent" },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "appliedCoupons" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "code" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "discountAmount" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "format" },
                      value: { kind: "EnumValue", value: "RAW" },
                    },
                  ],
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "discountTax" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "format" },
                      value: { kind: "EnumValue", value: "RAW" },
                    },
                  ],
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "subtotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "subtotalTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "shippingTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "shippingTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "totalTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "feeTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "feeTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "discountTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "discountTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<ApplyCouponMutation, ApplyCouponMutationVariables>;
export const EmptyCartDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "emptyCart" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "emptyCart" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "clearPersistentCart" },
                      value: { kind: "BooleanValue", value: true },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "cart" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "contentsTax" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "contentsTotal" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "discountTax" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "discountTotal" },
                      },
                      {
                        kind: "Field",
                        name: {
                          kind: "Name",
                          value: "displayPricesIncludeTax",
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "feeTax" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "feeTotal" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "isEmpty" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "needsShippingAddress" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "shippingTax" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "shippingTotal" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "subtotal" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "subtotalTax" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "total" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "totalTax" },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<EmptyCartMutation, EmptyCartMutationVariables>;
export const UpdateCartShippingDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "updateCartShipping" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "shippingMethod" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "String" },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "updateShippingMethod" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "shippingMethods" },
                      value: {
                        kind: "ListValue",
                        values: [
                          {
                            kind: "Variable",
                            name: { kind: "Name", value: "shippingMethod" },
                          },
                        ],
                      },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "cart" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "total" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "shippingTax" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "shippingTotal" },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  UpdateCartShippingMutation,
  UpdateCartShippingMutationVariables
>;
