import { gql } from "@apollo/client";

const register = gql(/* GraphQL */ `
  mutation Register(
    $firstName: String!
    $lastName: String!
    $email: String!
    $password: String!
  ) {
    registerCustomer(
      input: {
        email: $email
        firstName: $firstName
        lastName: $lastName
        password: $password
      }
    ) {
      customer {
        username
      }
    }
  }
`);

export { register };
