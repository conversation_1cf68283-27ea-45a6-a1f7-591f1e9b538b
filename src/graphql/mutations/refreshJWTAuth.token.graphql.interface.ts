import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type RefreshJwtAuthTokenMutationVariables = Types.Exact<{
  jwtRefreshToken?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type RefreshJwtAuthTokenMutation = {
  __typename?: "RootMutation";
  refreshJwtAuthToken?: {
    __typename?: "RefreshJwtAuthTokenPayload";
    authToken?: string | null;
  } | null;
};

export const RefreshJwtAuthTokenDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "refreshJwtAuthToken" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "jwtRefreshToken" },
          },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
          defaultValue: { kind: "StringValue", value: "", block: false },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "refreshJwtAuthToken" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "jwtRefreshToken" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "jwtRefreshToken" },
                      },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "authToken" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  RefreshJwtAuthTokenMutation,
  RefreshJwtAuthTokenMutationVariables
>;
