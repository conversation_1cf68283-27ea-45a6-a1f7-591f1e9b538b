import { gql } from "@apollo/client";

// TODO  rename personalData const to updatePersonalData
// ! IMPORTANT change the AuthoredOperations value as well (from personalData to updatePersonalData)
const personalData = gql(`
mutation updatePersonalData($phone: String!, $email: String!, $firstName: String!, $lastName: String!) {
  updateCustomer(
    input: {firstName: $firstName, lastName: $lastName, email: $email, billing: {phone: $phone}}
  ) 
  {
    customer{
      username
      }
  }

}     
`);

export { personalData };
