import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type DeleteShippingAddressMutationVariables = Types.Exact<{
  [key: string]: never;
}>;

export type DeleteShippingAddressMutation = {
  __typename?: "RootMutation";
  updateCustomer?: {
    __typename?: "UpdateCustomerPayload";
    customer?: { __typename?: "Customer"; id: string } | null;
  } | null;
};

export const DeleteShippingAddressDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "deleteShippingAddress" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "updateCustomer" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "shipping" },
                      value: {
                        kind: "ObjectValue",
                        fields: [
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "firstName" },
                            value: {
                              kind: "StringValue",
                              value: "",
                              block: false,
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "lastName" },
                            value: {
                              kind: "StringValue",
                              value: "",
                              block: false,
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "address1" },
                            value: {
                              kind: "StringValue",
                              value: "",
                              block: false,
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "city" },
                            value: {
                              kind: "StringValue",
                              value: "",
                              block: false,
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "postcode" },
                            value: {
                              kind: "StringValue",
                              value: "",
                              block: false,
                            },
                          },
                        ],
                      },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "customer" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  DeleteShippingAddressMutation,
  DeleteShippingAddressMutationVariables
>;
