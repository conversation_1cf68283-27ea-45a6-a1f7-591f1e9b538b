import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type DeleteBillingAddressMutationVariables = Types.Exact<{
  [key: string]: never;
}>;

export type DeleteBillingAddressMutation = {
  __typename?: "RootMutation";
  updateCustomer?: {
    __typename?: "UpdateCustomerPayload";
    customer?: { __typename?: "Customer"; id: string } | null;
  } | null;
};

export const DeleteBillingAddressDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "deleteBillingAddress" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "updateCustomer" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "billing" },
                      value: {
                        kind: "ObjectValue",
                        fields: [
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "firstName" },
                            value: {
                              kind: "StringValue",
                              value: "",
                              block: false,
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "lastName" },
                            value: {
                              kind: "StringValue",
                              value: "",
                              block: false,
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "address1" },
                            value: {
                              kind: "StringValue",
                              value: "",
                              block: false,
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "city" },
                            value: {
                              kind: "StringValue",
                              value: "",
                              block: false,
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "postcode" },
                            value: {
                              kind: "StringValue",
                              value: "",
                              block: false,
                            },
                          },
                        ],
                      },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "customer" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  DeleteBillingAddressMutation,
  DeleteBillingAddressMutationVariables
>;
