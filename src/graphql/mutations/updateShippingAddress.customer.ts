import { gql } from "@apollo/client";

const updateShippingAddress = gql(`
  mutation shippingAddress($firstName: String!, $lastName: String!,$address1: String!, $city: String!,$postcode: String!, $country: CountriesEnum!) {
    updateCustomer(
    input: {shipping: {firstName: $firstName, lastName: $lastName, address1: $address1, city: $city , postcode: $postcode, country: $country}}
  ) 
 {
  customer
  {
  id
 }}

}     
`);

export { updateShippingAddress };
