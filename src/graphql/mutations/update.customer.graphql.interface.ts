import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type UpdateCustomerMutationVariables = Types.Exact<{
  email: Types.Scalars["String"]["input"];
  id?: Types.InputMaybe<Types.Scalars["ID"]["input"]>;
  address1?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  address2?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  city?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  country?: Types.InputMaybe<Types.CountriesEnum>;
  phone?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  postcode?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  password?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  lastName?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  firstName?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type UpdateCustomerMutation = {
  __typename?: "RootMutation";
  updateCustomer?: {
    __typename?: "UpdateCustomerPayload";
    customer?: {
      __typename?: "Customer";
      firstName?: string | null;
      email?: string | null;
      displayName?: string | null;
      lastName?: string | null;
      username?: string | null;
      shipping?: {
        __typename?: "CustomerAddress";
        address1?: string | null;
        address2?: string | null;
        city?: string | null;
        country?: Types.CountriesEnum | null;
        email?: string | null;
        phone?: string | null;
        postcode?: string | null;
        state?: string | null;
      } | null;
    } | null;
  } | null;
};

export const UpdateCustomerDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "updateCustomer" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "email" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "String" },
            },
          },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "id" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "ID" } },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "address1" },
          },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "address2" },
          },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "city" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "country" },
          },
          type: {
            kind: "NamedType",
            name: { kind: "Name", value: "CountriesEnum" },
          },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "phone" },
          },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "postcode" },
          },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "password" },
          },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "lastName" },
          },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "firstName" },
          },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "updateCustomer" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "id" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "id" },
                      },
                    },
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "email" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "email" },
                      },
                    },
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "shipping" },
                      value: {
                        kind: "ObjectValue",
                        fields: [
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "phone" },
                            value: {
                              kind: "Variable",
                              name: { kind: "Name", value: "phone" },
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "postcode" },
                            value: {
                              kind: "Variable",
                              name: { kind: "Name", value: "postcode" },
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "email" },
                            value: {
                              kind: "Variable",
                              name: { kind: "Name", value: "email" },
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "country" },
                            value: {
                              kind: "Variable",
                              name: { kind: "Name", value: "country" },
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "address2" },
                            value: {
                              kind: "Variable",
                              name: { kind: "Name", value: "address2" },
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "city" },
                            value: {
                              kind: "Variable",
                              name: { kind: "Name", value: "city" },
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "address1" },
                            value: {
                              kind: "Variable",
                              name: { kind: "Name", value: "address1" },
                            },
                          },
                        ],
                      },
                    },
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "password" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "password" },
                      },
                    },
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "lastName" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "lastName" },
                      },
                    },
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "firstName" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "firstName" },
                      },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "customer" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "firstName" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "email" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "displayName" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "lastName" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "username" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "shipping" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "address1" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "address2" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "city" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "country" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "email" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "phone" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "postcode" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "state" },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  UpdateCustomerMutation,
  UpdateCustomerMutationVariables
>;
