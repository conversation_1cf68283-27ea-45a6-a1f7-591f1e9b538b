import { gql } from "@apollo/client";

const login = gql(/* GraphQL */ `
  mutation login($password: String!, $username: String!) {
    login(input: { password: $password, username: $username }) {
      customer {
        databaseId
        id
        username
        lastName
        firstName
        displayName
        email
        sessionToken
        jwtAuthExpiration
        jwtAuthToken
        jwtRefreshToken
        jwtUserSecret
      }
    }
  }
`);

/**
 * @deprecated - use login instead
 *
 */
const loginWithCookies = gql(/* GraphQL */ `
  mutation loginWithCookies($login: String!, $password: String!) {
    loginWithCookies(
      input: { login: $login, password: $password, rememberMe: true }
    ) {
      status
    }
  }
`);

export { login, loginWithCookies };
