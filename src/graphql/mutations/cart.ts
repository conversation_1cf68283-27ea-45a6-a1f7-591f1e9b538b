import { gql } from "graphql-request";

import { cartContent, cartItemContent } from "../fragments/cartSlices";

/**
 * 1. AddToCart
 * 2. UpdateCartItemQuantities
 * 3. RemoveItemsFromCart
 */
export const addToCart = gql`
  mutation AddToCart(
    $productId: Int!
    $variationId: Int
    $quantity: Int
    $extraData: String
  ) {
    addToCart(
      input: {
        productId: $productId
        variationId: $variationId
        quantity: $quantity
        extraData: $extraData
      }
    ) {
      cart {
        ...cartContent
      }
      cartItem {
        ...CartItemContent
      }
    }
  }
  ${cartContent}
  ${cartItemContent}
`;

export const updateCartItemQuantities = gql`
  mutation UpdateCartItemQuantities($items: [CartItemQuantityInput]) {
    updateItemQuantities(input: { items: $items }) {
      cart {
        ...cartContent
      }
      items {
        ...CartItemContent
      }
    }
  }
  ${cartContent}
  ${cartItemContent}
`;

export const removeItemsFromCart = gql`
  mutation RemoveItemsFromCart($keys: [ID], $all: Boolean) {
    removeItemsFromCart(input: { keys: $keys, all: $all }) {
      cart {
        ...cartContent
      }
      cartItems {
        ...CartItemContent
      }
    }
  }
  ${cartContent}
  ${cartItemContent}
`;

export const applyCoupon = gql`
mutation applyCoupon($code: String!){
  applyCoupon(input:{code:$code}){
    cart {
        ...cartContent
      }
    }

  ${cartContent}
}`;

export const emptyCart = gql`
  mutation emptyCart {
    emptyCart(input: { clearPersistentCart: true }) {
      cart {
        contentsTax
        contentsTotal
        discountTax
        discountTotal
        displayPricesIncludeTax
        feeTax
        feeTotal
        isEmpty
        needsShippingAddress
        shippingTax
        shippingTotal
        subtotal
        subtotalTax
        total
        totalTax
      }
    }
  }
`;

export const updateCartShipping = gql`
  mutation updateCartShipping($shippingMethod: String!) {
    updateShippingMethod(input: { shippingMethods: [$shippingMethod] }) {
      cart {
        total
        shippingTax
        shippingTotal
      }
    }
  }
`;
