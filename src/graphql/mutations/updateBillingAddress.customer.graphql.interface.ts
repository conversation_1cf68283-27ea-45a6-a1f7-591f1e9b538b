import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type BillingAddressMutationVariables = Types.Exact<{
  firstName: Types.Scalars["String"]["input"];
  lastName: Types.Scalars["String"]["input"];
  address1: Types.Scalars["String"]["input"];
  city: Types.Scalars["String"]["input"];
  postcode: Types.Scalars["String"]["input"];
  country: Types.CountriesEnum;
}>;

export type BillingAddressMutation = {
  __typename?: "RootMutation";
  updateCustomer?: {
    __typename?: "UpdateCustomerPayload";
    customer?: { __typename?: "Customer"; id: string } | null;
  } | null;
};

export const BillingAddressDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "billingAddress" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "firstName" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "String" },
            },
          },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "lastName" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "String" },
            },
          },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "address1" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "String" },
            },
          },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "city" } },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "String" },
            },
          },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "postcode" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "String" },
            },
          },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "country" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "CountriesEnum" },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "updateCustomer" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "billing" },
                      value: {
                        kind: "ObjectValue",
                        fields: [
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "firstName" },
                            value: {
                              kind: "Variable",
                              name: { kind: "Name", value: "firstName" },
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "lastName" },
                            value: {
                              kind: "Variable",
                              name: { kind: "Name", value: "lastName" },
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "address1" },
                            value: {
                              kind: "Variable",
                              name: { kind: "Name", value: "address1" },
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "city" },
                            value: {
                              kind: "Variable",
                              name: { kind: "Name", value: "city" },
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "postcode" },
                            value: {
                              kind: "Variable",
                              name: { kind: "Name", value: "postcode" },
                            },
                          },
                          {
                            kind: "ObjectField",
                            name: { kind: "Name", value: "country" },
                            value: {
                              kind: "Variable",
                              name: { kind: "Name", value: "country" },
                            },
                          },
                        ],
                      },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "customer" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  BillingAddressMutation,
  BillingAddressMutationVariables
>;
