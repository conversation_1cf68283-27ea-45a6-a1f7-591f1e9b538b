import { gql } from "@apollo/client";

const updateAccount = gql(/* GraphQL */ `
  mutation updateCustomer(
    $email: String!
    $id: ID
    $address1: String
    $address2: String
    $city: String
    $country: CountriesEnum
    $phone: String
    $postcode: String
    $password: String
    $lastName: String
    $firstName: String
  ) {
    updateCustomer(
      input: {
        id: $id
        email: $email
        shipping: {
          phone: $phone
          postcode: $postcode
          email: $email
          country: $country
          address2: $address2
          city: $city
          address1: $address1
        }
        password: $password
        lastName: $lastName
        firstName: $firstName
      }
    ) {
      customer {
        firstName
        email
        displayName
        lastName
        username
        shipping {
          address1
          address2
          city
          country
          email
          phone
          postcode
          state
        }
      }
    }
  }
`);

export { updateAccount };
