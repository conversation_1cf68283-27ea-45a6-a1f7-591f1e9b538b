// export const orderFields = gql`
//   fragment OrderFields on Order {
//     id
//     databaseId
//     orderNumber
//     orderVersion
//     status
//     needsProcessing
//     subtotal
//     paymentMethodTitle
//     total
//     totalTax
//     date
//     dateCompleted
//     datePaid
//     billing {
//       ...AddressFields
//     }
//     shipping {
//       ...AddressFields
//     }
//     lineItems(first: 100) {
//       nodes {
//         ...LineItemFields
//       }
//     }
//   }
//   ${addressFields}
//   ${lineItemFields}
// `;
