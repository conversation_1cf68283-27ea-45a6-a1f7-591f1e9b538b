import { gql } from "graphql-request";

// import { orderFields } from "./orderFields";

// export const addressFields = gql`
//   fragment AddressFields on CustomerAddress {
//     firstName
//     lastName
//     company
//     address1
//     address2
//     city
//     state
//     country
//     postcode
//     phone
//   }
// `;

// export const lineItemFields = gql`
//   fragment LineItemFields on LineItem {
//     databaseId
//     product {
//       node {
//         ...ProductContentSlice
//       }
//     }
//     orderId
//     quantity
//     subtotal
//     total
//     totalTax
//   }
//   ${productContentSlice}
// `;

// export const customerFields = gql`
//   fragment CustomerFields on Customer {
//     id
//     databaseId
//     firstName
//     lastName
//     displayName
//     billing {
//       ...AddressFields
//     }
//     shipping {
//       ...AddressFields
//     }
//     orders(first: 100) {
//       nodes {
//         ...OrderFields
//       }
//     }
//   }
//   ${addressFields}
//   ${orderFields}
// `;

export const customerContent = gql`
  fragment CustomerContent on Customer {
    id
    sessionToken
  }
`;
