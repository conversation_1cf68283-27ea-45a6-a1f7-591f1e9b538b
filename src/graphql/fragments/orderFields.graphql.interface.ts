import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type OrderFieldsFragment = {
  __typename?: "Order";
  id: string;
  databaseId?: number | null;
  orderNumber?: string | null;
  orderVersion?: string | null;
  status?: Types.OrderStatusEnum | null;
  needsProcessing?: boolean | null;
  subtotal?: string | null;
  paymentMethodTitle?: string | null;
  total?: string | null;
  totalTax?: string | null;
  date?: string | null;
  dateCompleted?: string | null;
  datePaid?: string | null;
  billing?: {
    __typename?: "CustomerAddress";
    firstName?: string | null;
    lastName?: string | null;
    company?: string | null;
    address1?: string | null;
    address2?: string | null;
    city?: string | null;
    state?: string | null;
    country?: Types.CountriesEnum | null;
    postcode?: string | null;
    phone?: string | null;
  } | null;
  shipping?: {
    __typename?: "CustomerAddress";
    firstName?: string | null;
    lastName?: string | null;
    company?: string | null;
    address1?: string | null;
    address2?: string | null;
    city?: string | null;
    state?: string | null;
    country?: Types.CountriesEnum | null;
    postcode?: string | null;
    phone?: string | null;
  } | null;
  lineItems?: {
    __typename?: "OrderToLineItemConnection";
    nodes: Array<{
      __typename?: "LineItem";
      databaseId?: number | null;
      orderId?: number | null;
      quantity?: number | null;
      subtotal?: string | null;
      total?: string | null;
      totalTax?: string | null;
      product?: {
        __typename?: "LineItemToProductConnectionEdge";
        node:
          | {
              __typename?: "ExternalProduct";
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              type?: Types.ProductTypesEnum | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  name?: string | null;
                  id: string;
                }>;
              } | null;
              image?: {
                __typename?: "MediaItem";
                id: string;
                sourceUrl?: string | null;
                altText?: string | null;
              } | null;
            }
          | {
              __typename?: "GroupProduct";
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              type?: Types.ProductTypesEnum | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  name?: string | null;
                  id: string;
                }>;
              } | null;
              image?: {
                __typename?: "MediaItem";
                id: string;
                sourceUrl?: string | null;
                altText?: string | null;
              } | null;
            }
          | {
              __typename?: "SimpleProduct";
              price?: string | null;
              regularPrice?: string | null;
              soldIndividually?: boolean | null;
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              type?: Types.ProductTypesEnum | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  name?: string | null;
                  id: string;
                }>;
              } | null;
              image?: {
                __typename?: "MediaItem";
                id: string;
                sourceUrl?: string | null;
                altText?: string | null;
              } | null;
            }
          | {
              __typename?: "VariableProduct";
              price?: string | null;
              regularPrice?: string | null;
              soldIndividually?: boolean | null;
              id: string;
              databaseId: number;
              name?: string | null;
              slug?: string | null;
              type?: Types.ProductTypesEnum | null;
              productCategories?: {
                __typename?: "ProductToProductCategoryConnection";
                nodes: Array<{
                  __typename?: "ProductCategory";
                  name?: string | null;
                  id: string;
                }>;
              } | null;
              image?: {
                __typename?: "MediaItem";
                id: string;
                sourceUrl?: string | null;
                altText?: string | null;
              } | null;
            };
      } | null;
    }>;
  } | null;
};

export const OrderFieldsFragmentDoc = {
  kind: "Document",
  definitions: [
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "OrderFields" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "Order" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "databaseId" } },
          { kind: "Field", name: { kind: "Name", value: "orderNumber" } },
          { kind: "Field", name: { kind: "Name", value: "orderVersion" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "needsProcessing" } },
          { kind: "Field", name: { kind: "Name", value: "subtotal" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "paymentMethodTitle" },
          },
          { kind: "Field", name: { kind: "Name", value: "total" } },
          { kind: "Field", name: { kind: "Name", value: "totalTax" } },
          { kind: "Field", name: { kind: "Name", value: "date" } },
          { kind: "Field", name: { kind: "Name", value: "dateCompleted" } },
          { kind: "Field", name: { kind: "Name", value: "datePaid" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "billing" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "FragmentSpread",
                  name: { kind: "Name", value: "AddressFields" },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "shipping" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "FragmentSpread",
                  name: { kind: "Name", value: "AddressFields" },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "lineItems" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "IntValue", value: "100" },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "nodes" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "FragmentSpread",
                        name: { kind: "Name", value: "LineItemFields" },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "ProductContentSlice" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "Product" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "databaseId" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "slug" } },
          { kind: "Field", name: { kind: "Name", value: "type" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "productCategories" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "nodes" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "image" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sourceUrl" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "size" },
                      value: {
                        kind: "EnumValue",
                        value: "WOOCOMMERCE_THUMBNAIL",
                      },
                    },
                  ],
                },
                { kind: "Field", name: { kind: "Name", value: "altText" } },
              ],
            },
          },
          {
            kind: "InlineFragment",
            typeCondition: {
              kind: "NamedType",
              name: { kind: "Name", value: "SimpleProduct" },
            },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "price" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "regularPrice" },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "soldIndividually" },
                },
              ],
            },
          },
          {
            kind: "InlineFragment",
            typeCondition: {
              kind: "NamedType",
              name: { kind: "Name", value: "VariableProduct" },
            },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "price" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "regularPrice" },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "soldIndividually" },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "AddressFields" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "CustomerAddress" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "firstName" } },
          { kind: "Field", name: { kind: "Name", value: "lastName" } },
          { kind: "Field", name: { kind: "Name", value: "company" } },
          { kind: "Field", name: { kind: "Name", value: "address1" } },
          { kind: "Field", name: { kind: "Name", value: "address2" } },
          { kind: "Field", name: { kind: "Name", value: "city" } },
          { kind: "Field", name: { kind: "Name", value: "state" } },
          { kind: "Field", name: { kind: "Name", value: "country" } },
          { kind: "Field", name: { kind: "Name", value: "postcode" } },
          { kind: "Field", name: { kind: "Name", value: "phone" } },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "LineItemFields" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "LineItem" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "databaseId" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "product" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "node" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "FragmentSpread",
                        name: { kind: "Name", value: "ProductContentSlice" },
                      },
                    ],
                  },
                },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "orderId" } },
          { kind: "Field", name: { kind: "Name", value: "quantity" } },
          { kind: "Field", name: { kind: "Name", value: "subtotal" } },
          { kind: "Field", name: { kind: "Name", value: "total" } },
          { kind: "Field", name: { kind: "Name", value: "totalTax" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<OrderFieldsFragment, unknown>;
