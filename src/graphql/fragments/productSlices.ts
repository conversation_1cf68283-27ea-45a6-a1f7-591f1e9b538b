// import { gql } from "@apollo/client";

// export const productContentSlice = gql`
//   fragment ProductContentSlice on Product {
//     id
//     databaseId
//     name
//     slug
//     type
//     productCategories {
//       nodes {
//         name
//         id
//       }
//     }
//     image {
//       id
//       sourceUrl
//       altText
//     }
//     ... on SimpleProduct {
//       regularPrice(format: RAW)
//       salePrice(format: RAW)
//       price(format: RAW)
//       soldIndividually
//     }
//     ... on VariableProduct {
//       variations {
//         nodes {
//           databaseId
//           name
//           regularPrice(format: RAW)
//           salePrice(format: RAW)
//           attributes {
//             nodes {
//               name
//               value
//               id
//             }
//           }
//         }
//       }
//     }
//   }
// `;

// export const productVariationContentSlice = gql`
//   fragment ProductVariationContentSlice on ProductVariation {
//     id
//     databaseId
//     name
//     slug
//     image {
//       id
//       sourceUrl
//       altText
//     }
//     regularPrice(format: RAW)
//     salePrice(format: RAW)
//   }
// `;

// export const productContentFull = gql`
//   fragment ProductContentFull on Product {
//     id
//     databaseId
//     slug
//     name
//     type
//     productCategories {
//       nodes {
//         name
//         id
//       }
//     }
//     description
//     shortDescription(format: RAW)
//     image {
//       id
//       sourceUrl
//       altText
//     }
//     galleryImages {
//       nodes {
//         id
//         sourceUrl
//         altText
//       }
//     }
//     productTags(first: 20) {
//       nodes {
//         id
//         slug
//         name
//       }
//     }
//     attributes {
//       nodes {
//         id
//         attributeId
//         ... on LocalProductAttribute {
//           name
//           options
//           variation
//         }
//         ... on GlobalProductAttribute {
//           name
//           options
//           variation
//         }
//       }
//     }
//     ... on SimpleProduct {
//       onSale
//       stockStatus
//       regularPrice(format: RAW)
//       salePrice(format: RAW)
//       stockStatus
//       stockQuantity
//       soldIndividually
//     }
//     ... on VariableProduct {
//       onSale
//       regularPrice(format: RAW)
//       salePrice(format: RAW)
//       stockStatus
//       stockQuantity
//       soldIndividually
//       variations(first: 50) {
//         nodes {
//           id
//           databaseId
//           name
//           regularPrice(format: RAW)
//           salePrice(format: RAW)
//           onSale
//           attributes {
//             nodes {
//               name
//               label
//               value
//             }
//           }
//         }
//       }
//     }
//   }
// `;

// export const variationContent = gql`
//   fragment VariationContent on ProductVariation {
//     id
//     name
//     slug
//     regularPrice(format: RAW)
//     salePrice(format: RAW)
//     stockStatus
//     stockQuantity
//     onSale
//     image {
//       id
//       sourceUrl
//       altText
//     }
//   }
// `;

// /**
//  *  1. ProductContentSlice
//  *  2. ProductVariationContentSlice

//  */
// export const productSliceIndex = {
//   ProductContentSlice: productContentSlice,
//   ProductVariationContentSlice: productVariationContentSlice,
// };
