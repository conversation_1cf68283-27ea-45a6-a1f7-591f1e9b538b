import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

export type CustomerContentFragment = {
  __typename?: "Customer";
  id: string;
  sessionToken?: string | null;
};

export const CustomerContentFragmentDoc = {
  kind: "Document",
  definitions: [
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CustomerContent" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "Customer" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "sessionToken" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<CustomerContentFragment, unknown>;
