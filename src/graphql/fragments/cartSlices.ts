import { gql } from "graphql-request";

export const cartItemContent = gql`
  fragment CartItemContent on CartItem {
    key
    quantity
    total(format: RAW)
    product {
      node {
        image {
          altText
          sourceUrl
        }
        type
        id
        databaseId
        name
        slug
        productCategories {
          nodes {
            id
            name
          }
        }
        ... on SimpleProduct {
          stockQuantity
          # used to calculate the cart's total
          price(format: RAW)
          regularPrice(format: RAW)
          salePrice(format: RAW)
        }
        ... on VariableProduct {
          stockQuantity
        }
      }
    }
    variation {
      node {
        id
        databaseId

        attributes {
          nodes {
            label
            value
          }
        }
        name
        stockQuantity
        regularPrice(format: RAW)
        salePrice(format: RAW)
        # used to calculate the cart's total
        price(format: RAW)
      }
    }
  }
`;

export const cartContent = gql`
  fragment cartContent on Cart {
    total(format: RAW)

    isEmpty
    contents(first: 100) {
      itemCount
      nodes {
        ...CartItemContent
      }
    }
    appliedCoupons {
      code
      discountAmount(format: RAW)
      discountTax(format: RAW)
    }

    subtotal(format: RAW)
    subtotalTax(format: RAW)
    shippingTax(format: RAW)
    shippingTotal(format: RAW)
    total(format: RAW)
    totalTax(format: RAW)
    feeTax(format: RAW)
    feeTotal(format: RAW)
    discountTax(format: RAW)
    discountTotal(format: RAW)
  }
  ${cartItemContent}
`;
