import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type GetCartQueryVariables = Types.Exact<{
  customerId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetCartQuery = {
  __typename?: "RootQuery";
  cart?: {
    __typename?: "Cart";
    total?: string | null;
    isEmpty?: boolean | null;
    subtotal?: string | null;
    subtotalTax?: string | null;
    shippingTax?: string | null;
    shippingTotal?: string | null;
    totalTax?: string | null;
    feeTax?: string | null;
    feeTotal?: string | null;
    discountTax?: string | null;
    discountTotal?: string | null;
    contents?: {
      __typename?: "CartToCartItemConnection";
      itemCount?: number | null;
      nodes: Array<{
        __typename?: "SimpleCartItem";
        key: string;
        quantity?: number | null;
        total?: string | null;
        product?: {
          __typename?: "CartItemToProductConnectionEdge";
          node:
            | {
                __typename?: "ExternalProduct";
                type?: Types.ProductTypesEnum | null;
                id: string;
                databaseId: number;
                name?: string | null;
                slug?: string | null;
                image?: {
                  __typename?: "MediaItem";
                  altText?: string | null;
                  sourceUrl?: string | null;
                } | null;
                productCategories?: {
                  __typename?: "ProductToProductCategoryConnection";
                  nodes: Array<{
                    __typename?: "ProductCategory";
                    id: string;
                    name?: string | null;
                  }>;
                } | null;
              }
            | {
                __typename?: "GroupProduct";
                type?: Types.ProductTypesEnum | null;
                id: string;
                databaseId: number;
                name?: string | null;
                slug?: string | null;
                image?: {
                  __typename?: "MediaItem";
                  altText?: string | null;
                  sourceUrl?: string | null;
                } | null;
                productCategories?: {
                  __typename?: "ProductToProductCategoryConnection";
                  nodes: Array<{
                    __typename?: "ProductCategory";
                    id: string;
                    name?: string | null;
                  }>;
                } | null;
              }
            | {
                __typename?: "SimpleProduct";
                stockQuantity?: number | null;
                price?: string | null;
                regularPrice?: string | null;
                salePrice?: string | null;
                type?: Types.ProductTypesEnum | null;
                id: string;
                databaseId: number;
                name?: string | null;
                slug?: string | null;
                image?: {
                  __typename?: "MediaItem";
                  altText?: string | null;
                  sourceUrl?: string | null;
                } | null;
                productCategories?: {
                  __typename?: "ProductToProductCategoryConnection";
                  nodes: Array<{
                    __typename?: "ProductCategory";
                    id: string;
                    name?: string | null;
                  }>;
                } | null;
              }
            | {
                __typename?: "VariableProduct";
                stockQuantity?: number | null;
                type?: Types.ProductTypesEnum | null;
                id: string;
                databaseId: number;
                name?: string | null;
                slug?: string | null;
                image?: {
                  __typename?: "MediaItem";
                  altText?: string | null;
                  sourceUrl?: string | null;
                } | null;
                productCategories?: {
                  __typename?: "ProductToProductCategoryConnection";
                  nodes: Array<{
                    __typename?: "ProductCategory";
                    id: string;
                    name?: string | null;
                  }>;
                } | null;
              };
        } | null;
        variation?: {
          __typename?: "CartItemToProductVariationConnectionEdge";
          node: {
            __typename?: "SimpleProductVariation";
            id: string;
            databaseId: number;
            name?: string | null;
            stockQuantity?: number | null;
            regularPrice?: string | null;
            salePrice?: string | null;
            price?: string | null;
            attributes?: {
              __typename?: "ProductVariationToVariationAttributeConnection";
              nodes: Array<{
                __typename?: "VariationAttribute";
                label?: string | null;
                value?: string | null;
              }>;
            } | null;
          };
        } | null;
      }>;
    } | null;
    appliedCoupons?: Array<{
      __typename?: "AppliedCoupon";
      code: string;
      discountAmount: string;
      discountTax: string;
    } | null> | null;
  } | null;
  customer?: {
    __typename?: "Customer";
    id: string;
    sessionToken?: string | null;
  } | null;
};

export const GetCartDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "getCart" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "customerId" },
          },
          type: { kind: "NamedType", name: { kind: "Name", value: "Int" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "cart" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "FragmentSpread",
                  name: { kind: "Name", value: "cartContent" },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "customer" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "customerId" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "customerId" },
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "FragmentSpread",
                  name: { kind: "Name", value: "CustomerContent" },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CartItemContent" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "CartItem" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "key" } },
          { kind: "Field", name: { kind: "Name", value: "quantity" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "product" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "node" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "image" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "altText" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "sourceUrl" },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "type" } },
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "databaseId" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      { kind: "Field", name: { kind: "Name", value: "slug" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "productCategories" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "nodes" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "id" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "name" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                      {
                        kind: "InlineFragment",
                        typeCondition: {
                          kind: "NamedType",
                          name: { kind: "Name", value: "SimpleProduct" },
                        },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "stockQuantity" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "price" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "regularPrice" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "salePrice" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                          ],
                        },
                      },
                      {
                        kind: "InlineFragment",
                        typeCondition: {
                          kind: "NamedType",
                          name: { kind: "Name", value: "VariableProduct" },
                        },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "stockQuantity" },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "variation" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "node" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "databaseId" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "attributes" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "nodes" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "label" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "value" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "stockQuantity" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "regularPrice" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "salePrice" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "price" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "format" },
                            value: { kind: "EnumValue", value: "RAW" },
                          },
                        ],
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "cartContent" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "Cart" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          { kind: "Field", name: { kind: "Name", value: "isEmpty" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "contents" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "IntValue", value: "100" },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "itemCount" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "nodes" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "FragmentSpread",
                        name: { kind: "Name", value: "CartItemContent" },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "appliedCoupons" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "code" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "discountAmount" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "format" },
                      value: { kind: "EnumValue", value: "RAW" },
                    },
                  ],
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "discountTax" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "format" },
                      value: { kind: "EnumValue", value: "RAW" },
                    },
                  ],
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "subtotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "subtotalTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "shippingTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "shippingTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "total" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "totalTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "feeTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "feeTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "discountTax" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "discountTotal" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CustomerContent" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "Customer" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "sessionToken" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetCartQuery, GetCartQueryVariables>;
