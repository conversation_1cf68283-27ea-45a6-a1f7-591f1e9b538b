import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type GetFaqQueryVariables = Types.Exact<{ [key: string]: never }>;

export type GetFaqQuery = {
  __typename?: "RootQuery";
  pageBy?: { __typename?: "Page"; blocks?: any | null } | null;
};

export const GetFaqDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "getFaq" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "pageBy" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "uri" },
                value: { kind: "StringValue", value: "faq", block: false },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "blocks" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "htmlContent" },
                      value: { kind: "BooleanValue", value: true },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetFaqQuery, GetFaqQueryVariables>;
