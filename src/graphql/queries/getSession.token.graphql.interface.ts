import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type GetSessionTokenQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetSessionTokenQuery = {
  __typename?: "RootQuery";
  customer?: { __typename?: "Customer"; sessionToken?: string | null } | null;
};

export const GetSessionTokenDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "getSessionToken" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "customer" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sessionToken" },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetSessionTokenQuery,
  GetSessionTokenQueryVariables
>;
