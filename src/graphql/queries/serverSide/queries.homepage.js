/** @description string representation of the getBrands graphql query */
const getBrandsQuery = `query getBrands {
  brands(first: 40) {
    edges {
      node {
        count
        name
        slug
        thumbnailUrl
      }
    }
  }
}
`;

/** @description used to fetch products of a certain category  */
const getProductsByCategoryIDQuery = `query getProductsByCategoryID($id: ID!) {
productCategory(id: $id, idType: DATABASE_ID) {
  name
  contentNodes(first: 100) {
    nodes {
      ... on SimpleProduct {
      type
        id
        name
        price
        salePrice
        regularPrice
        stockStatus
        slug
        image {
          altText
          mediaItemUrl
        }
        galleryImages {
          nodes {
            altText
            description
            mediaItemUrl
          }
        }
      }
      ... on VariableProduct {
      type
        id
        name
        price
        salePrice
        regularPrice
        stockStatus
        slug
        variations{
          nodes{
            stockStatus
            name
            price
            salePrice
            regularPrice
            stockStatus
          }
        }
        image {
          altText
          mediaItemUrl
        }
                  galleryImages {
          nodes {
            altText
            description
            mediaItemUrl
          }
        }
      }
    }
  }
}
}`;

/** @description used to fetch products  */
const getProductsWithoutCategory = `query getProductsWithoutCategory {
  products {
      nodes {
        ... on SimpleProduct {
        type
          id
          name
          price
          salePrice
          regularPrice
          stockStatus
          slug
          image {
            altText
            mediaItemUrl
          }
          galleryImages {
            nodes {
              altText
              description
              mediaItemUrl
            }
          }
        }
        ... on VariableProduct {
        type
          id
          name
          price
          salePrice
          regularPrice
          stockStatus
          slug
          variations{
            nodes{
              stockStatus
              name
              price
              salePrice
              regularPrice
              stockStatus
            }
          }
          image {
            altText
            mediaItemUrl
          }
                    galleryImages {
            nodes {
              altText
              description
              mediaItemUrl
            }
          }
        }
      }
    
  }
  }`;

/** @description used to fetch products of a certain category  */
const getLatestProductsQuery = `query {
productCategories(first: 1) {
  nodes {
    contentNodes(first: 12) {
      nodes {
        ... on VariableProduct {
          id
          name
          salePrice
          price
          regularPrice
          slug
          image {
            description
            mediaItemUrl
          }
          galleryImages {
            nodes {
              description
              mediaItemUrl
            }
          }
        }
        ... on SimpleProduct {
          id
          name
          price
          salePrice
          regularPrice
          slug
          image {
            description
            mediaItemUrl
          }
          galleryImages {
            nodes {
              description
              mediaItemUrl
            }
          }
        }
      }
    }
  }
}
}`;
export {
  getBrandsQuery,
  getLatestProductsQuery,
  getProductsByCategoryIDQuery,
  getProductsWithoutCategory,
};
