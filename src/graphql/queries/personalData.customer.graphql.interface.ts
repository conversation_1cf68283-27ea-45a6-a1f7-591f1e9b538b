import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type GetPersonalDataQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetPersonalDataQuery = {
  __typename?: "RootQuery";
  customer?: {
    __typename?: "Customer";
    id: string;
    firstName?: string | null;
    lastName?: string | null;
    email?: string | null;
    billing?: { __typename?: "CustomerAddress"; phone?: string | null } | null;
  } | null;
};

export const GetPersonalDataDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "getPersonalData" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "customer" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "firstName" } },
                { kind: "Field", name: { kind: "Name", value: "lastName" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "billing" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "phone" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetPersonalDataQuery,
  GetPersonalDataQueryVariables
>;
