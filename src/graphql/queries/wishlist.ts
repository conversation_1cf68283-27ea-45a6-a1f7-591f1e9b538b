import { DocumentNode, gql } from "@apollo/client";

export const getProductsForWishlist = (
  IDs: Array<string | number>
): DocumentNode => {
  /** each section is responsible for fetching a product (be it SINGLE or VARIABLE) */
  const sections = IDs.map((databaseId) => {
    return `product_${databaseId}:product(id: ${databaseId}, idType: DATABASE_ID) {
     id
     databaseId
     name
     type
     slug
     productCategories {
       nodes {
         name
       }
     }
     image {
       sourceUrl
       altText
     }
     ... on SimpleProduct {
       price(format: RAW)
       regularPrice(format: RAW)
       salePrice(format: RAW)
     }
     ... on VariableProduct {
      price
      salePrice
      regularPrice
       variations {
         nodes {
           attributes {
             nodes {
               label
               value
             }
           }
           databaseId
           regularPrice(format: RAW)
           salePrice(format: RAW)
           price(format: RAW)
         }
       }
     }
   }`;
  });
  /** the entire query responsible for fetching multiple products */
  const completeQuery = gql`query getProductsForWishlist{
  ${sections}
  }
  `;

  return completeQuery;
};
