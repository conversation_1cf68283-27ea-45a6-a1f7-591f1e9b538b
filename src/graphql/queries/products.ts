// import { gql } from "graphql-request";

// import {
//   productContentFull,
//   variationContent,
// } from "../fragments/productSlices";

// export const GetProduct = gql`
//   query GetProduct($id: ID!, $idType: ProductIdTypeEnum) {
//     product(id: $id, idType: $idType) {
//       ...ProductContentFull
//     }
//   }
//   ${productContentFull}
// `;

// export const GetProductVariation = gql`
//   query GetProductVariation($id: ID!) {
//     productVariation(id: $id, idType: DATABASE_ID) {
//       ...VariationContent
//     }
//   }
//   ${variationContent}
// `;
