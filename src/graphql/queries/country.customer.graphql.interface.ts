import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type GetCountriesQueryVariables = Types.Exact<{ [key: string]: never }>;

export type GetCountriesQuery = {
  __typename?: "RootQuery";
  countries?: Array<Types.CountriesEnum | null> | null;
};

export const GetCountriesDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "getCountries" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "countries" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetCountriesQuery, GetCountriesQueryVariables>;
