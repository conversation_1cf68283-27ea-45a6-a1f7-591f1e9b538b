export const getSkateboardsQuery = `query getSkateboards {
    category(id: "dGVybTo5MQ==") {
        name
        children(first: 5) {
          edges {
            node {
              name
            }
          }
        }
      }
    }
  `;

  export const getAccessoriesQuery = `query getAccessories {
    category(id: "dGVybTo0ODQ=") {
        name
        children(first: 5) {
          edges {
            node {
              name
            }
          }
        }
      }
    }
  `;

  export  const getFirstTextilQuery = `query getFirstTextil {
    category(id: "dGVybToyMjI=") {
        name
        children(first: 9) {
          edges {
            node {
              name
            }
          }
        }
      }
    }
  `;


  export  const getLastTextilQuery = `query getLastTextil {
    category(id: "dGVybToyMjI=") {
        name
        children(last: 4) {
          edges {
            node {
              name
            }
          }
        }
      }
    }
  `;
