import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type GetCustomerDataQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetCustomerDataQuery = {
  __typename?: "RootQuery";
  customer?: {
    __typename?: "Customer";
    displayName?: string | null;
    id: string;
    email?: string | null;
  } | null;
};

export type GetFullCustomerDataQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetFullCustomerDataQuery = {
  __typename?: "RootQuery";
  customer?: {
    __typename?: "Customer";
    id: string;
    email?: string | null;
    firstName?: string | null;
    lastName?: string | null;
    billing?: {
      __typename?: "CustomerAddress";
      address1?: string | null;
      phone?: string | null;
      postcode?: string | null;
      city?: string | null;
      country?: Types.CountriesEnum | null;
    } | null;
    shipping?: {
      __typename?: "CustomerAddress";
      address1?: string | null;
      postcode?: string | null;
      city?: string | null;
      country?: Types.CountriesEnum | null;
    } | null;
  } | null;
};

export const GetCustomerDataDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "getCustomerData" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "customer" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "displayName" } },
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetCustomerDataQuery,
  GetCustomerDataQueryVariables
>;
export const GetFullCustomerDataDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "getFullCustomerData" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "customer" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
                { kind: "Field", name: { kind: "Name", value: "firstName" } },
                { kind: "Field", name: { kind: "Name", value: "lastName" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "billing" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "address1" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "phone" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "postcode" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "city" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "country" },
                      },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "shipping" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "address1" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "postcode" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "city" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "country" },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetFullCustomerDataQuery,
  GetFullCustomerDataQueryVariables
>;
