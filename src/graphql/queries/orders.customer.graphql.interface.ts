import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type GetOrdersQueryVariables = Types.Exact<{ [key: string]: never }>;

export type GetOrdersQuery = {
  __typename?: "RootQuery";
  orders?: {
    __typename?: "RootQueryToOrderConnection";
    nodes: Array<{
      __typename?: "Order";
      date?: string | null;
      id: string;
      invoices?: Array<{
        __typename?: "Invoice";
        date_created?: string | null;
        product_total?: string | null;
        order_id?: number | null;
      } | null> | null;
      shipments?: Array<{
        __typename?: "Shipment";
        trackingUrl?: string | null;
      } | null> | null;
    }>;
  } | null;
};

export const GetOrdersDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "getOrders" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "orders" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "nodes" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "date" } },
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "invoices" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "date_created" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "product_total" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "order_id" },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "shipments" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "trackingUrl" },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetOrdersQuery, GetOrdersQueryVariables>;
