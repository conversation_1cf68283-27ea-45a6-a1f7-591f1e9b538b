import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";

import * as Types from "../__generated__/schema.graphql";
export type GetProductQueryVariables = Types.Exact<{
  id: Types.Scalars["ID"]["input"];
  idType?: Types.InputMaybe<Types.ProductIdTypeEnum>;
}>;

export type GetProductQuery = {
  __typename?: "RootQuery";
  product?:
    | {
        __typename?: "ExternalProduct";
        id: string;
        databaseId: number;
        slug?: string | null;
        name?: string | null;
        type?: Types.ProductTypesEnum | null;
        description?: string | null;
        shortDescription?: string | null;
        productCategories?: {
          __typename?: "ProductToProductCategoryConnection";
          nodes: Array<{
            __typename?: "ProductCategory";
            name?: string | null;
            id: string;
          }>;
        } | null;
        image?: {
          __typename?: "MediaItem";
          id: string;
          sourceUrl?: string | null;
          altText?: string | null;
        } | null;
        galleryImages?: {
          __typename?: "ProductToMediaItemConnection";
          nodes: Array<{
            __typename?: "MediaItem";
            id: string;
            sourceUrl?: string | null;
            altText?: string | null;
          }>;
        } | null;
        productTags?: {
          __typename?: "ProductToProductTagConnection";
          nodes: Array<{
            __typename?: "ProductTag";
            id: string;
            slug?: string | null;
            name?: string | null;
          }>;
        } | null;
        attributes?: {
          __typename?: "ProductToProductAttributeConnection";
          nodes: Array<
            | {
                __typename?: "GlobalProductAttribute";
                name?: string | null;
                options?: Array<string | null> | null;
                variation?: boolean | null;
                id: string;
                attributeId: number;
              }
            | {
                __typename?: "LocalProductAttribute";
                name?: string | null;
                options?: Array<string | null> | null;
                variation?: boolean | null;
                id: string;
                attributeId: number;
              }
          >;
        } | null;
      }
    | {
        __typename?: "GroupProduct";
        id: string;
        databaseId: number;
        slug?: string | null;
        name?: string | null;
        type?: Types.ProductTypesEnum | null;
        description?: string | null;
        shortDescription?: string | null;
        productCategories?: {
          __typename?: "ProductToProductCategoryConnection";
          nodes: Array<{
            __typename?: "ProductCategory";
            name?: string | null;
            id: string;
          }>;
        } | null;
        image?: {
          __typename?: "MediaItem";
          id: string;
          sourceUrl?: string | null;
          altText?: string | null;
        } | null;
        galleryImages?: {
          __typename?: "ProductToMediaItemConnection";
          nodes: Array<{
            __typename?: "MediaItem";
            id: string;
            sourceUrl?: string | null;
            altText?: string | null;
          }>;
        } | null;
        productTags?: {
          __typename?: "ProductToProductTagConnection";
          nodes: Array<{
            __typename?: "ProductTag";
            id: string;
            slug?: string | null;
            name?: string | null;
          }>;
        } | null;
        attributes?: {
          __typename?: "ProductToProductAttributeConnection";
          nodes: Array<
            | {
                __typename?: "GlobalProductAttribute";
                name?: string | null;
                options?: Array<string | null> | null;
                variation?: boolean | null;
                id: string;
                attributeId: number;
              }
            | {
                __typename?: "LocalProductAttribute";
                name?: string | null;
                options?: Array<string | null> | null;
                variation?: boolean | null;
                id: string;
                attributeId: number;
              }
          >;
        } | null;
      }
    | {
        __typename?: "SimpleProduct";
        onSale?: boolean | null;
        stockStatus?: Types.StockStatusEnum | null;
        price?: string | null;
        regularPrice?: string | null;
        salePrice?: string | null;
        stockQuantity?: number | null;
        soldIndividually?: boolean | null;
        id: string;
        databaseId: number;
        slug?: string | null;
        name?: string | null;
        type?: Types.ProductTypesEnum | null;
        description?: string | null;
        shortDescription?: string | null;
        rawPrice?: string | null;
        productCategories?: {
          __typename?: "ProductToProductCategoryConnection";
          nodes: Array<{
            __typename?: "ProductCategory";
            name?: string | null;
            id: string;
          }>;
        } | null;
        image?: {
          __typename?: "MediaItem";
          id: string;
          sourceUrl?: string | null;
          altText?: string | null;
        } | null;
        galleryImages?: {
          __typename?: "ProductToMediaItemConnection";
          nodes: Array<{
            __typename?: "MediaItem";
            id: string;
            sourceUrl?: string | null;
            altText?: string | null;
          }>;
        } | null;
        productTags?: {
          __typename?: "ProductToProductTagConnection";
          nodes: Array<{
            __typename?: "ProductTag";
            id: string;
            slug?: string | null;
            name?: string | null;
          }>;
        } | null;
        attributes?: {
          __typename?: "ProductToProductAttributeConnection";
          nodes: Array<
            | {
                __typename?: "GlobalProductAttribute";
                name?: string | null;
                options?: Array<string | null> | null;
                variation?: boolean | null;
                id: string;
                attributeId: number;
              }
            | {
                __typename?: "LocalProductAttribute";
                name?: string | null;
                options?: Array<string | null> | null;
                variation?: boolean | null;
                id: string;
                attributeId: number;
              }
          >;
        } | null;
      }
    | {
        __typename?: "VariableProduct";
        onSale?: boolean | null;
        price?: string | null;
        regularPrice?: string | null;
        salePrice?: string | null;
        stockStatus?: Types.StockStatusEnum | null;
        stockQuantity?: number | null;
        soldIndividually?: boolean | null;
        id: string;
        databaseId: number;
        slug?: string | null;
        name?: string | null;
        type?: Types.ProductTypesEnum | null;
        description?: string | null;
        shortDescription?: string | null;
        rawPrice?: string | null;
        variations?: {
          __typename?: "ProductWithVariationsToProductVariationConnection";
          nodes: Array<{
            __typename?: "SimpleProductVariation";
            id: string;
            databaseId: number;
            name?: string | null;
            price?: string | null;
            regularPrice?: string | null;
            salePrice?: string | null;
            onSale?: boolean | null;
            rawPrice?: string | null;
            attributes?: {
              __typename?: "ProductVariationToVariationAttributeConnection";
              nodes: Array<{
                __typename?: "VariationAttribute";
                name?: string | null;
                label?: string | null;
                value?: string | null;
              }>;
            } | null;
          }>;
        } | null;
        productCategories?: {
          __typename?: "ProductToProductCategoryConnection";
          nodes: Array<{
            __typename?: "ProductCategory";
            name?: string | null;
            id: string;
          }>;
        } | null;
        image?: {
          __typename?: "MediaItem";
          id: string;
          sourceUrl?: string | null;
          altText?: string | null;
        } | null;
        galleryImages?: {
          __typename?: "ProductToMediaItemConnection";
          nodes: Array<{
            __typename?: "MediaItem";
            id: string;
            sourceUrl?: string | null;
            altText?: string | null;
          }>;
        } | null;
        productTags?: {
          __typename?: "ProductToProductTagConnection";
          nodes: Array<{
            __typename?: "ProductTag";
            id: string;
            slug?: string | null;
            name?: string | null;
          }>;
        } | null;
        attributes?: {
          __typename?: "ProductToProductAttributeConnection";
          nodes: Array<
            | {
                __typename?: "GlobalProductAttribute";
                name?: string | null;
                options?: Array<string | null> | null;
                variation?: boolean | null;
                id: string;
                attributeId: number;
              }
            | {
                __typename?: "LocalProductAttribute";
                name?: string | null;
                options?: Array<string | null> | null;
                variation?: boolean | null;
                id: string;
                attributeId: number;
              }
          >;
        } | null;
      }
    | null;
};

export type GetProductVariationQueryVariables = Types.Exact<{
  id: Types.Scalars["ID"]["input"];
}>;

export type GetProductVariationQuery = {
  __typename?: "RootQuery";
  productVariation?: {
    __typename?: "SimpleProductVariation";
    id: string;
    name?: string | null;
    slug?: string | null;
    price?: string | null;
    regularPrice?: string | null;
    salePrice?: string | null;
    stockStatus?: Types.StockStatusEnum | null;
    stockQuantity?: number | null;
    onSale?: boolean | null;
    image?: {
      __typename?: "MediaItem";
      id: string;
      sourceUrl?: string | null;
      altText?: string | null;
    } | null;
  } | null;
};

export const GetProductDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "GetProduct" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "id" } },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "ID" } },
          },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "idType" },
          },
          type: {
            kind: "NamedType",
            name: { kind: "Name", value: "ProductIdTypeEnum" },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "product" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "id" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "id" },
                },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "idType" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "idType" },
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "FragmentSpread",
                  name: { kind: "Name", value: "ProductContentFull" },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "ProductContentFull" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "Product" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "databaseId" } },
          { kind: "Field", name: { kind: "Name", value: "slug" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "type" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "productCategories" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "nodes" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                    ],
                  },
                },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "description" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "shortDescription" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "format" },
                value: { kind: "EnumValue", value: "RAW" },
              },
            ],
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "image" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "sourceUrl" } },
                { kind: "Field", name: { kind: "Name", value: "altText" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "galleryImages" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "nodes" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "sourceUrl" },
                        arguments: [
                          {
                            kind: "Argument",
                            name: { kind: "Name", value: "size" },
                            value: {
                              kind: "EnumValue",
                              value: "WOOCOMMERCE_THUMBNAIL",
                            },
                          },
                        ],
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "altText" },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "productTags" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "IntValue", value: "20" },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "nodes" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "slug" } },
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "attributes" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "nodes" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "attributeId" },
                      },
                      {
                        kind: "InlineFragment",
                        typeCondition: {
                          kind: "NamedType",
                          name: {
                            kind: "Name",
                            value: "LocalProductAttribute",
                          },
                        },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "name" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "options" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "variation" },
                            },
                          ],
                        },
                      },
                      {
                        kind: "InlineFragment",
                        typeCondition: {
                          kind: "NamedType",
                          name: {
                            kind: "Name",
                            value: "GlobalProductAttribute",
                          },
                        },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "name" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "options" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "variation" },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "InlineFragment",
            typeCondition: {
              kind: "NamedType",
              name: { kind: "Name", value: "SimpleProduct" },
            },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "onSale" } },
                { kind: "Field", name: { kind: "Name", value: "stockStatus" } },
                { kind: "Field", name: { kind: "Name", value: "price" } },
                {
                  kind: "Field",
                  alias: { kind: "Name", value: "rawPrice" },
                  name: { kind: "Name", value: "price" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "format" },
                      value: { kind: "EnumValue", value: "RAW" },
                    },
                  ],
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "regularPrice" },
                },
                { kind: "Field", name: { kind: "Name", value: "salePrice" } },
                { kind: "Field", name: { kind: "Name", value: "stockStatus" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "stockQuantity" },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "soldIndividually" },
                },
              ],
            },
          },
          {
            kind: "InlineFragment",
            typeCondition: {
              kind: "NamedType",
              name: { kind: "Name", value: "VariableProduct" },
            },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "onSale" } },
                { kind: "Field", name: { kind: "Name", value: "price" } },
                {
                  kind: "Field",
                  alias: { kind: "Name", value: "rawPrice" },
                  name: { kind: "Name", value: "price" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "format" },
                      value: { kind: "EnumValue", value: "RAW" },
                    },
                  ],
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "regularPrice" },
                },
                { kind: "Field", name: { kind: "Name", value: "salePrice" } },
                { kind: "Field", name: { kind: "Name", value: "stockStatus" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "stockQuantity" },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "soldIndividually" },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "variations" },
                  arguments: [
                    {
                      kind: "Argument",
                      name: { kind: "Name", value: "first" },
                      value: { kind: "IntValue", value: "50" },
                    },
                  ],
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "nodes" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "id" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "databaseId" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "name" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "price" },
                            },
                            {
                              kind: "Field",
                              alias: { kind: "Name", value: "rawPrice" },
                              name: { kind: "Name", value: "price" },
                              arguments: [
                                {
                                  kind: "Argument",
                                  name: { kind: "Name", value: "format" },
                                  value: { kind: "EnumValue", value: "RAW" },
                                },
                              ],
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "regularPrice" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "salePrice" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "onSale" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "attributes" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "nodes" },
                                    selectionSet: {
                                      kind: "SelectionSet",
                                      selections: [
                                        {
                                          kind: "Field",
                                          name: { kind: "Name", value: "name" },
                                        },
                                        {
                                          kind: "Field",
                                          name: {
                                            kind: "Name",
                                            value: "label",
                                          },
                                        },
                                        {
                                          kind: "Field",
                                          name: {
                                            kind: "Name",
                                            value: "value",
                                          },
                                        },
                                      ],
                                    },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetProductQuery, GetProductQueryVariables>;
export const GetProductVariationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "GetProductVariation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "id" } },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "ID" } },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "productVariation" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "id" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "id" },
                },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "idType" },
                value: { kind: "EnumValue", value: "DATABASE_ID" },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "FragmentSpread",
                  name: { kind: "Name", value: "VariationContent" },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "VariationContent" },
      typeCondition: {
        kind: "NamedType",
        name: { kind: "Name", value: "ProductVariation" },
      },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "slug" } },
          { kind: "Field", name: { kind: "Name", value: "price" } },
          { kind: "Field", name: { kind: "Name", value: "regularPrice" } },
          { kind: "Field", name: { kind: "Name", value: "salePrice" } },
          { kind: "Field", name: { kind: "Name", value: "stockStatus" } },
          { kind: "Field", name: { kind: "Name", value: "stockQuantity" } },
          { kind: "Field", name: { kind: "Name", value: "onSale" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "image" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "sourceUrl" } },
                { kind: "Field", name: { kind: "Name", value: "altText" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetProductVariationQuery,
  GetProductVariationQueryVariables
>;
