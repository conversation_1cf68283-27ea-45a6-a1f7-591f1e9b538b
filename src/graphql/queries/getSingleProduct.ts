const getSingleProduct = `
  query getIdProduct ($id: ID!) {
    product(id: $id,idType: SLUG) {
      databaseId
      id
      slug
      name
      description
      image {
        mediaItemUrl
      }
      galleryImages {
        edges {
          node {
            mediaItemUrl
          }
        }
      }
      importOption {
        dropItem
      }
      brands {
        nodes {
          name
          thumbnailUrl
        }
      }
      productCategories {
        nodes {
          name
        }
      }
      ...VariableProductFragment
      ...ExternalProductFragment
      ...GroupProductFragment
      ...SimpleProductFragment
    }
  }

  fragment VariableProductFragment on VariableProduct {
    stockQuantity
    stockStatus
    price(format: RAW)
    regularPrice(format: RAW)
    salePrice(format: RAW)
    variations {
      edges {
        node {
          attributes {
            edges {
              node {
                value
              }
            }
          }
          name
          databaseId
          price
          regularPrice
          salePrice
          stockStatus
          stockQuantity
        }
      }
    }
  }

  fragment ExternalProductFragment on ExternalProduct {
    id
    name
    price
  }

  fragment GroupProductFragment on GroupProduct {
    id
    name
    price
  }

  fragment SimpleProductFragment on SimpleProduct {
    stockQuantity
    price(format: RAW)
    regularPrice(format: RAW)
    salePrice(format: RAW)
    stockStatus
  }
  
`;

export { getSingleProduct as getProduct };
