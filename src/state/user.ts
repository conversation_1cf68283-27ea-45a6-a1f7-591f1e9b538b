import CoCartAPI from "@cocart/cocart-rest-api";
import { toast } from "@components/ui/use-toast";
import {
  toast_DESCRIPTIONS,
  toast_TIMEOUT,
  toast_TITLES,
} from "@constants/toastConstants";
import type {} from "@redux-devtools/extension"; // required for devtools typing
import { create } from "zustand";
import { combine, devtools, persist } from "zustand/middleware";

import { Cart, Product } from "./schema";

/* @ts-ignore */
export const CoCart = new CoCartAPI({
  url: process.env.NEXT_PUBLIC_WORDPRESS_URI as string,
});

/* @ts-ignore */
export const CoCartV1 = new CoCartAPI({
  url: process.env.NEXT_PUBLIC_WORDPRESS_URI as string,
  version: "cocart/v1",
});

export const useUserStore = create(
  devtools(
    persist(
      combine(
        {
          loading: true,
          wishlistLoading: false,
          couponLoading: false,
          error: false,
          cartKey: "",
          cart: null as Cart | null,
          shipping: [],
          wishlist: [] as Product[],
          checkingOut: false,
        },
        (set, get) => ({
          setCheckingOut: (state: boolean) => {
            set({ checkingOut: state });
          },
          synchronize: async () => {
            const information = get();
            set({ loading: true });
            try {
              const cartReturn = await CoCart.get(
                `cart?cart_key=${information.cartKey}`
              );
              if (cartReturn?.data) {
                set({
                  cart: cartReturn.data as Cart,
                  error: false,
                  cartKey: cartReturn.data?.cart_key,
                });
                console.log("SET CART", cartReturn.data);
                set({ loading: false });
              } else {
                set({ error: true });
                set({ loading: false });
                console.info("NO CART DATA");
              }
            } catch (e) {
              set({ error: true });
              set({ loading: false });
              console.error("CART ERROR", e);
            }
          },
          synchronizeWishlist: async () => {
            const information = get();
            if (information.wishlist) {
              set({ wishlistLoading: true });
              let newWishlist = information.wishlist;
              let i = 0;
              while (i < information.wishlist.length) {
                const currentProduct = information.wishlist[i];
                try {
                  const { data } = await CoCart.get(
                    `products/${currentProduct.id}`
                  );
                  newWishlist = newWishlist.map((p) => {
                    if (p.id === data.id) {
                      return data;
                    }
                    return p;
                  });

                  set({
                    wishlist: newWishlist,
                  });
                } catch (e) {}
                i++;
              }
            }
          },
          addToCart: async (
            productId: number,
            quantity: number,
            variation: Record<string, any> | null
          ) => {
            const gotData = get();
            const requestData = {
              id: String(productId),
              quantity: String(quantity),
              variation: variation ?? undefined,
            };
            console.log("TRYING TO ADD", requestData);
            try {
              const response = await CoCart.post(
                `cart/add-item?cart_key=${gotData.cartKey}`,
                requestData
              );
              if (response.data) {
                set({ cart: response.data, cartKey: response.data?.cart_key });
                toast({
                  title: toast_TITLES.GENERAL_STATE.SUCCESS,
                  variant: "success",
                  description:
                    toast_DESCRIPTIONS.GQL_OPS.cart.addToCart_SUCCESS,
                  duration: toast_TIMEOUT.quick,
                });
              } else {
                console.info("COULD NOT ADD", response);
                toast({
                  title: toast_TITLES.GENERAL_STATE.ERROR,
                  variant: "destructive",
                  description: toast_DESCRIPTIONS.GQL_OPS.cart.addToCart_FAILED,
                });
              }
            } catch (e: any) {
              console.error("ERROR ADDING", e);
              toast({
                title: toast_TITLES.GENERAL_STATE.ERROR,
                variant: "destructive",
                description: e?.response?.data?.message ?? e?.message,
              });
            }
          },
          removeFromCart: async (key: string) => {
            const gotData = get();
            set({ loading: true });
            try {
              const response = await CoCart.delete(
                `cart/item/${key}?cart_key=${gotData.cartKey}`,
                {
                  return_status: true,
                }
              );
              if (response.data) {
                set({ cart: response.data, cartKey: response.data?.cart_key });
                toast({
                  title: toast_TITLES.GENERAL_STATE.SUCCESS,
                  variant: "success",
                  description:
                    toast_DESCRIPTIONS.GQL_OPS.cart.removeItemFromCart_SUCCESS,
                });
              } else {
                toast({
                  title: toast_TITLES.GENERAL_STATE.ERROR,
                  variant: "destructive",
                  description:
                    toast_DESCRIPTIONS.GQL_OPS.cart.removeItemFromCart_FAILED,
                });
              }
            } catch (e: any) {
              console.error("DELETE PRODUCT ERROR", e);
              toast({
                title: toast_TITLES.GENERAL_STATE.ERROR,
                variant: "destructive",
                description: e?.message,
              });
            }
            set({ loading: false });
          },
          updateCartStock: async (key: string, quantity: number) => {
            const gotData = get();
            try {
              const response = await CoCart.post(
                `cart/item/${key}?cart_key=${gotData.cartKey}`,
                {
                  quantity: String(quantity),
                  return_cart: true,
                }
              );
              if (response.data) {
                set({ cart: response.data, cartKey: response.data?.cart_key });
                toast({
                  title: toast_TITLES.GENERAL_STATE.SUCCESS,
                  variant: "success",
                  description:
                    toast_DESCRIPTIONS.GQL_OPS.cart
                      .updateItemQuantities_SUCCESS,
                });
              } else {
                toast({
                  title: toast_TITLES.GENERAL_STATE.ERROR,
                  variant: "destructive",
                  description:
                    toast_DESCRIPTIONS.GQL_OPS.cart.updateItemQuantities_FAILED,
                });
              }
            } catch (e: any) {
              console.error("UPDATE PRODUCT QUANTITY ERROR", e);
              console.log(`cart/item/${key}?cart_key=${gotData.cartKey}`);
              toast({
                title: toast_TITLES.GENERAL_STATE.ERROR,
                variant: "destructive",
                description: e?.message,
              });
            }
          },
          applyCoupon: async (coupon: string) => {
            // TODO: This is not working
            const gotData = get();
            try {
              const response = await CoCartV1.post(
                `coupon?cart_key=${gotData.cartKey}`,
                {
                  coupon: coupon,
                }
              );
              console.log(response, response.data);
              if (response.data?.response) {
                const cartReturn = await CoCart.get(
                  `cart?cart_key=${get().cartKey}`
                );
                if (cartReturn?.data) {
                  set({
                    cart: cartReturn.data as Cart,
                    error: false,
                    cartKey: cartReturn.data?.cart_key,
                  });
                }
                toast({
                  title: toast_TITLES.GENERAL_STATE.SUCCESS,
                  variant: "success",
                  description:
                    toast_DESCRIPTIONS.GQL_OPS.cart.applyCoupon_SUCCESS,
                });
              } else {
                toast({
                  title: toast_TITLES.GENERAL_STATE.ERROR,
                  variant: "destructive",
                  description:
                    toast_DESCRIPTIONS.GQL_OPS.cart.applyCoupon_FAILED,
                });
              }
            } catch (e: any) {
              toast({
                title: toast_TITLES.GENERAL_STATE.ERROR,
                variant: "destructive",
                description: e?.message,
              });
            }
          },
          updateShipping: async (countryCode: string) => {
            const gotData = get();
            try {
              const response = await CoCart.post(
                `calculate/shipping?cart_key=${gotData.cartKey}`,
                {
                  country: countryCode,
                  return_methods: true,
                }
              );
              console.log("SHIPPING", response.data);
            } catch (e) {
              console.error(e);
            }
          },
          toggleWishlistItem: async (productId: number | string) => {
            console.log("TOGGLE");
            const gotData = get();
            const wishlist = [...gotData.wishlist];
            const duplicateIndex = wishlist.findIndex(
              (w) => w.id === productId
            );
            if (duplicateIndex > -1) {
              wishlist.splice(duplicateIndex, 1);
              set({ wishlist: wishlist });
            } else {
              try {
                const { data } = await CoCart.get(
                  `products/${productId}?cart_key=${gotData.cartKey}`
                );
                console.log("DATA", data);
                wishlist.push(data);
                set({ wishlist: wishlist });
              } catch (e: any) {}
            }
          },
        })
      ),
      { name: "user-storage" }
    )
  )
);
