export interface Cart {
  cart_hash: string;
  cart_key: string;
  currency: Currency;
  customer: {
    billing_address: {
      billing_email: string;
      billing_first_name: string;
      billing_last_name: string;
      billing_company: string;
      billing_country: string;
      billing_address_1: string;
      billing_address_2: string;
      billing_pickup_location_notice: string;
      billing_postcode: string;
      billing_city: string;
      billing_state: string;
      billing_phone: string;
    };
    shipping_address: {
      shipping_first_name: string;
      shipping_last_name: string;
      shipping_company: string;
      shipping_country: string;
      shipping_address_1: string;
      shipping_address_2: string;
      shipping_pickup_location_notice: string;
      pickup_location_customer_number: string;
      shipping_postcode: string;
      shipping_city: string;
      shipping_state: string;
    };
  };
  items: CartProduct[];
  item_count: number;
  items_weight: string;
  coupons: unknown[];
  needs_payment: boolean;
  needs_shipping: boolean;
  shipping: {
    total_packages: number;
    show_package_details: boolean;
    has_calculated_shipping: boolean;
    packages: unknown[];
  };
  fees: unknown[];
  taxes: unknown[];
  totals: {
    subtotal: string;
    subtotal_tax: string;
    fee_total: string;
    fee_tax: string;
    discount_total: string;
    discount_tax: string;
    shipping_total: string;
    shipping_tax: string;
    total: string;
    total_tax: string;
  };
  removed_items: unknown[];
  cross_sells: unknown[];
  notices: unknown[];
}

export interface Currency {
  currency_code: string;
  currency_symbol: string;
  currency_symbol_pos: string;
  currency_minor_unit: number;
  currency_decimal_separator: string;
  currency_thousand_separator: string;
  currency_prefix: string;
  currency_suffix: string;
}

export interface Attribute {
  id: number;
  name: string;
  position: number;
  is_attribute_visible: boolean;
  used_for_variation: boolean;
  options: Record<string, string>;
}

export interface SingleAttribute {
  id: number;
  name: string;
  option: Option;
}

export type Option = Record<string, string>;

export interface Image {
  id: number;
  src: {
    thumbnail: string;
    medium: string;
    medium_large: string;
    large: string;
    full: string;
  } & Record<string, string>;
  name: string;
  alt: string;
  position: number;
  featured: boolean;
}

export interface CartProduct {
  backorders: string;
  cart_item_data: unknown[];
  featured_image: string;
  id: number;
  item_key: string;
  meta: {
    dimensions: {
      height: string;
      length: string;
      unit: string;
      width: string;
    };
    product_type: string;
    sku: string;
    variation: Record<string, string>;
    weight: string;
  };
  name: string;
  price: string;
  price_sale: string;
  price_regular: string;
  quantity: {
    max_purchase: number;
    min_purchase: number;
    value: number;
  };
  slug: string;
  title: string;
  totals: {
    subtotal: string;
    subtotal_tax: number;
    tax: number;
    total: number;
  };
}

export type VariationIdentification = Record<string, string>;

export interface Variation {
  id: number;
  parent_id: number;
  name: string;
  slug: string;
  permalink: string;
  sku: string;
  description: string;
  dates: {
    created: string;
    created_gmt: string;
    modified: string;
    modified_gmt: string;
  };
  featured: boolean;
  prices: {
    price: string;
    regular_price: string;
    sale_price: string;
    price_range?: unknown[];
    on_sale: boolean;
    date_on_sale: unknown;
    currency: Currency;
  };
  hidden_conditions: {
    virtual: boolean;
    downloadable: boolean;
    manage_stock: boolean;
    sold_individually: boolean;
    shipping_required: boolean;
  };
  images: Image[];
  featured_image?: Image["src"];
  categories: unknown[];
  tags: unknown[];
  attributes: Record<string, SingleAttribute>;
  stock: {
    is_in_stock: boolean;
    stock_quantity: number;
    stock_status: "instock" | "outofstock";
    backorders: "no" | "yes";
    backorders_allowed: boolean;
    backordered: boolean;
    low_stock_amount: string;
  };
  weight: { value: string; unit: string };
  dimensions: { length: string; width: string; height: string; unit: string };
  total_sales: string;
  add_to_cart: {
    text?: string;
    description?: string;
    is_purchasable: boolean;
    purchase_quantity: { min_purchase: number; max_purchase: number };
    rest_url: string;
  };
  meta_data: [];
  _links: unknown;
}

export interface Product {
  id: number;
  parent_id: number;
  name: string;
  type: "variable" | "simple";
  slug: string;
  permalink: string;
  sku: string | null;
  description: string;
  short_description: string;
  dates: Record<string, string>;
  featured: boolean;
  prices: {
    price: string;
    regular_price: string;
    sale_price: string;
    price_range:
      | {
          from: string;
          to: string;
        }
      | {
          from: string;
          to: string;
        }[];
    on_sale: boolean;
    date_on_sale: Record<string, string | null>;
    currency: Currency;
  };
  hidden_conditions: {
    virtual: boolean;
    downloadable: boolean;
    manage_stock: boolean;
    sold_individually: boolean;
    reviews_allowed: boolean;
    shipping_required: boolean;
  } & Record<string, boolean>;
  average_rating: string;
  review_count: number;
  rating_count: number;
  rated_out_of: string;
  images: Image[];
  categories: {
    id: number;
    name: string;
    slug: string;
    rest_url: string;
  }[];
  tags: {
    id: number;
    name: string;
    slug: string;
    rest_url: string;
  }[];
  attributes: Record<string, Attribute> | Record<string, Attribute>[];
  default_attributes: Record<string, Attribute> | Record<string, Attribute>[];
  variations: (Omit<
    Variation,
    | "parent_id"
    | "name"
    | "slug"
    | "permalink"
    | "dates"
    | "featured"
    | "hidden_conditions"
    | "images"
    | "categories"
    | "tags"
    | "weight"
    | "dimensions"
    | "total_sales"
    | "meta_data"
    | "_links"
    | "attributes"
  > & { attributes: Record<string, string> })[];
  grouped_products: unknown;
  stock: {
    is_in_stock: boolean;
    stock_quantity: number | null;
    stock_status: "instock" | "outofstock";
    backorders: "no" | "yes";
    backorders_allowed: boolean;
    backordered: boolean;
    low_stock_amount: string;
  };
  weight: {
    value: string;
    unit: string;
  };
  dimensions: {
    length: string;
    width: string;
    height: string;
    unit: string;
  };
  reviews: unknown;
  related: {
    id: number;
    name: string;
    permalink: string;
    price: string;
    add_to_cart: {
      text: string;
      description: string;
      rest_url: string;
    };
    rest_url: string;
  }[];
  upsells: {
    id: number;
    name: string;
    permalink: string;
    price: string;
    add_to_cart: {
      text: string;
      description: string;
      rest_url: string;
    };
    rest_url: string;
  }[];
  cross_sells: {
    id: number;
    name: string;
    permalink: string;
    price: string;
    add_to_cart: {
      text: string;
      description: string;
      rest_url: string;
    };
    rest_url: string;
  }[];
  total_sales: number;
  external_url: string;
  button_text: string;
  add_to_cart: {
    text: string;
    description: string;
    rest_url: string;
    has_options: boolean;
    is_purchasable: boolean;
    purchase_quantity:
      | {
          min_purchase: number;
          max_purchase: number;
        }
      | {
          min_purchase: number;
          max_purchase: number;
        }[];
  };
  meta_data: {
    id: number;
    key: string;
    value: unknown;
  }[];
  _links: {
    self: Link[];
    collection: Link[];
    variations?: Link[];
  };
}

export interface Link {
  permalink: string;
  href: string;
  targetHints?: {
    allow: string[];
  };
}
