import { index, int, sqliteTable, text } from "drizzle-orm/sqlite-core";

export const productsTable = sqliteTable(
  "products_table",
  {
    id: int().primaryKey(),
    slug: text(),
    name: text().notNull(),
    type: text().notNull(),
    dateCreated: int({ mode: "timestamp" }),
    sku: text(),
    futuraId: text("futura_id").notNull().unique(),
    regularPrice: text("regular_price"),
    salePrice: text("sale_price"),
    manageStock: int("manage_stock", { mode: "boolean" }),
    stockQuantity: int("stock_quantity"),
    isSynchronized: int("is_synchronized", { mode: "boolean" }),
    isPublished: int("is_published", { mode: "boolean" }),
    unit: text("unit"),
    unitValue: text("unit_value"),
    unitValueSlug: text("unit_value_slug"),
    dropItem: int("drop_item", { mode: "boolean" }),
    fuer: text("fuer"),
    description: text(),
    categories: text("categories", { mode: "json" }),
    images: text("images", { mode: "json" }),
    brands: text("brands", { mode: "json" }),
    publishedAt: int({ mode: "timestamp" }),
  },
  (table) => {
    return {
      skuIdx: index("sku_idx").on(table.sku),
    };
  }
);

export const variationsTable = sqliteTable(
  "variations_table",
  {
    id: int().primaryKey(),
    parent: int("parent_id")
      .references(() => productsTable.id)
      .notNull(),
    name: text().notNull(),
    sku: text().notNull().unique(),
    regularPrice: text("regular_price").notNull(),
    salePrice: text("sale_price"),
    manageStock: int("manage_stock", { mode: "boolean" }),
    stockQuantity: int("stock_quantity"),
    unit: text("unit"),
    unitValue: text("unit_value"),
    unitValueSlug: text("unit_value_slug"),
    isSynchronized: int("is_synchronized", { mode: "boolean" }),
  },
  (table) => {
    return [index("parent_idx").on(table.parent)];
  }
);

// Table with products and variations that need to be synced to the product and variations tables on the next run
export const syncTable = sqliteTable("sync_table", {
  key: int().primaryKey(),
  id: int(),
  parent: int("parent_id"),
  type: text(),
});

export const metaTable = sqliteTable("metadata_table", {
  id: int().primaryKey({ autoIncrement: true }),
  key: text().notNull().unique(),
  value: text(),
});

export const statisticsTable = sqliteTable("statistics_table", {
  id: int().primaryKey({ autoIncrement: true }),
  date: int({ mode: "timestamp" }).notNull(),
  action: text().notNull(),
  file: text(),
  items: int().notNull(),
  runtime: int().notNull(),
});

export const updateTable = sqliteTable("update_table", {
  id: int().primaryKey({ autoIncrement: true }),
  productId: int(),
  variationId: int(),
});
