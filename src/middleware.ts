import { NextRequest, NextResponse } from "next/server";

/**
 *
 *  DOCS see (NEXT) [https://nextjs.org/docs/app/building-your-application/routing/middleware#using-cookies]
 */
export function middleware(request: NextRequest) {
  const isAuthenticated = request.cookies.get("isAuthenticated")?.value;
  const requestUrl = request.url;

  const requestingAuthPages =
    requestUrl.toLowerCase().includes("login") ||
    requestUrl.toLowerCase().includes("register");
  const requestingDashboard = requestUrl.toLowerCase().includes("dashboard");

  if (isAuthenticated) {
    if (requestingAuthPages) {
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }
    if (requestingDashboard) {
      return NextResponse.next();
    }
  }
  if (!isAuthenticated) {
    if (requestingAuthPages) {
      return NextResponse.next();
    }
    if (requestingDashboard) {
      return NextResponse.redirect(new URL("/login", request.url));
    }
  }
}

export const config = {
  matcher: ["/dashboard/:path*", "/login", "/register", "/authSuccess"],
};
