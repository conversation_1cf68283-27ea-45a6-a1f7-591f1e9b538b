"use client";

import { Apollo<PERSON>rovider } from "@apollo/client";
import { apolloClient } from "@lib/apollo/client";
import { FC, ReactNode } from "react";

/**
 * @description this wrapper is used to wrap the ApolloClient context provider in a client-side component; ApolloClient cannot render server-side since it depends on React's Context API (React.CreateContext and such...) which are only available on client-side rendered pages.
 */

const ApolloClientProvider: FC<{ children: ReactNode }> = ({ children }) => {
  return <ApolloProvider client={apolloClient}>{children}</ApolloProvider>;
};

export default ApolloClientProvider;
