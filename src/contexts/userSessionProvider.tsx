"use client";
/**
 * @see reference: https://woographql.com/docs/handling-user-session-and-using-cart-mutations
 */

import { userSessionsReducer } from "@lib/reducers/userSessionProviderReducer";
import {
  getWishlist_LS,
  toggleItemInWishListLS,
} from "@lib/utilities/wishlistUtils";
import type {
  userSession,
  userSessionContext,
} from "@typesDeclarations/contextTypes/sessionContext.types";
import type { FC, ReactNode } from "react";
import { createContext, useContext, useReducer, useState } from "react";

/**
 * @description default state on initial/first visit for the context
 */
const initialContextState: userSessionContext = {
  state: { cart: null, customer: null },
  dispatch: () => {},
  checkoutManager: {
    checkingOut: false,
    setCheckingOut: () => {},
  },
  wishlistManager: {
    wishlist: [],
    toggleWishlistItem: () => {},
  },
};

/**
 * @description default state on initial/first visit for the reducer
 */
const initialSessionState: userSession = {
  cart: null,
  customer: null,
};

const SessionContext = createContext(initialContextState);

export const useSessionContext = (): userSessionContext =>
  useContext(SessionContext);

export const UserSessionProvider: FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(
    userSessionsReducer,
    initialSessionState
  );

  /**
   * ! using useState instead of Reducer to account for useReducer drawback
   *  @see https://react.dev/reference/react/useReducer#setstate-caveats
   */
  const [checkingOut, setCheckingOut] = useState(false);

  const [wishlist, setWishlist] = useState(() => getWishlist_LS());

  const contextValue: userSessionContext = {
    state: {
      cart: state.cart,
      customer: state.customer,
    },
    dispatch,
    checkoutManager: {
      checkingOut,
      setCheckingOut,
    },
    wishlistManager: {
      wishlist,
      toggleWishlistItem: (databaseId) => {
        const updatedWishlist = toggleItemInWishListLS(wishlist, databaseId);
        setWishlist(updatedWishlist);
      },
    },
  };
  return (
    <SessionContext.Provider value={contextValue}>
      {children}
    </SessionContext.Provider>
  );
};
