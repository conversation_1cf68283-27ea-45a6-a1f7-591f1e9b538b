"use client";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@components/ui/alert-dialog";
import { Button } from "@components/ui/Button";
import {
  DialogAction,
  DialogState,
} from "@typesDeclarations/contextTypes/dialogContext.types";
import * as React from "react";
import {
  createContext,
  ReactNode,
  useCallback,
  useReducer,
  useRef,
} from "react";

export const DialogContext = createContext<
  <T extends DialogAction>(
    _: T
  ) => Promise<T["type"] extends "alert" | "confirm" ? boolean : null | string>
>(() => null!);

export function dialogReducer(
  state: DialogState,
  action: DialogAction
): DialogState {
  switch (action.type) {
    case "close":
      return { ...state, open: false };
    case "alert":
    case "confirm":
      return {
        ...state,
        open: true,
        ...action,
        cancelButton:
          action.cancelButton || (action.type === "alert" ? "Okay" : "Cancel"),
        actionButton:
          ("actionButton" in action && action.actionButton) || "Okay",
      };
    default:
      return state;
  }
}

export function DialogProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(dialogReducer, {
    open: false,
    title: "",
    body: "",
    type: "alert",
    cancelButton: "Cancel",
    actionButton: "Okay",
    styleOptions: {
      title: "",
      description: "",
      actionButton: "",
      cancelButton: "",
    },
  });

  const resolveRef = useRef<(_: any) => void>();

  function close() {
    dispatch({ type: "close" });
    resolveRef.current?.(false);
  }

  function confirm(value?: string) {
    dispatch({ type: "close" });
    resolveRef.current?.(value ?? true);
  }

  const dialog = useCallback(async <T extends DialogAction>(params: T) => {
    dispatch(params);

    return new Promise<
      T["type"] extends "alert" | "confirm" ? boolean : null | string
    >((resolve) => {
      resolveRef.current = resolve;
    });
  }, []);

  return (
    <DialogContext.Provider value={dialog}>
      {children}
      <AlertDialog
        open={state.open}
        onOpenChange={(open: boolean) => {
          if (!open) close();
          return;
        }}
      >
        <AlertDialogContent asChild>
          <form
            onSubmit={(event) => {
              event.preventDefault();
              confirm(event.currentTarget.prompt?.value);
            }}
          >
            <AlertDialogHeader>
              <AlertDialogTitle
                className={`${state.styleOptions?.title ?? ""}`}
              >
                {state.title}
              </AlertDialogTitle>
              {state.body ? (
                <AlertDialogDescription
                  className={`${state.styleOptions?.description ?? ""}`}
                >
                  {state.body}
                </AlertDialogDescription>
              ) : null}
            </AlertDialogHeader>
            <AlertDialogFooter className="gap-y-2">
              <Button
                type="button"
                onClick={close}
                className={`${state?.styleOptions?.cancelButton ?? ""}`}
              >
                {state.cancelButton}
              </Button>
              {state.type === "alert" ? null : (
                <Button
                  type="submit"
                  className={`${state?.styleOptions?.actionButton ?? ""}`}
                >
                  {state.actionButton}
                </Button>
              )}
            </AlertDialogFooter>
          </form>
        </AlertDialogContent>
      </AlertDialog>
    </DialogContext.Provider>
  );
}
